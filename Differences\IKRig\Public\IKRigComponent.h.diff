--- Left: IKRigComponent.h
+++ Right: IKRigComponent.h
@@ -18,7 +18,7 @@
     void SetIKRigGoalTransform(const FName GoalName, const FTransform Transform, const float PositionAlpha, const float RotationAlpha);

     

     UFUNCTION(BlueprintCallable)

-    void SetIKRigGoalPositionAndRotation(const FName GoalName, const FVector Position, const FQuat Rotation, const float PositionAlpha, const float RotationAlpha);

+    void SetIKRigGoalPositionAndRotation(const FName GoalName, const FVector position, const FQuat Rotation, const float PositionAlpha, const float RotationAlpha);

     

     UFUNCTION(BlueprintCallable)

     void SetIKRigGoal(const FIKRigGoal& Goal);
