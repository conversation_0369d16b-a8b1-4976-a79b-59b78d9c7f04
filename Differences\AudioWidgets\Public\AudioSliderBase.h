DIFFERENCES IN: AudioWidgets\Public\AudioSliderBase.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AudioWidgets\Public\AudioSliderBase.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AudioWidgets\Public\AudioSliderBase.h
============================================================

SECTION starting at line 63 (left) / 63 (right):
----------------------------------------
  CONTEXT (line 63):     UWidget::FGetLinearColor WidgetBackgroundColorDelegate;
  CONTEXT (line 64): 
  CONTEXT (line 65):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 66):     TEnumAsByte<EOrientation> orientation;
  ADDED   (line 66):     TEnumAsByte<EOrientation> Orientation;
  CONTEXT (line 67): 
  CONTEXT (line 68):     UPROPERTY(BlueprintAssignable, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 69):     FOnFloatValueChangedEvent OnValueChanged;