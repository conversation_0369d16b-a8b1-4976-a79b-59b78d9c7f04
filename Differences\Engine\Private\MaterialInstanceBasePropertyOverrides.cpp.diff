--- Left: MaterialInstanceBasePropertyOverrides.cpp
+++ Right: MaterialInstanceBasePropertyOverrides.cpp
@@ -11,8 +11,6 @@
     this->bOverride_OutputTranslucentVelocity = false;

     this->bOverride_bHasPixelAnimation = false;

     this->bOverride_bEnableTessellation = false;

-    this->bOverride_bFullyRough = false;

-    this->bOverride_bFullyRoughOnMobile = false;

     this->bOverride_DisplacementScaling = false;

     this->bOverride_bEnableDisplacementFade = false;

     this->bOverride_DisplacementFadeRange = false;

@@ -24,8 +22,6 @@
     this->bOutputTranslucentVelocity = false;

     this->bHasPixelAnimation = false;

     this->bEnableTessellation = false;

-    this->bFullyRough = false;

-    this->bFullyRoughOnMobile = false;

     this->bEnableDisplacementFade = false;

     this->BlendMode = BLEND_Opaque;

     this->ShadingModel = MSM_Unlit;
