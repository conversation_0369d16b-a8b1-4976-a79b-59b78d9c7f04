DIFFERENCES IN: Engine\Public\RepRootMotionMontage.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\RepRootMotionMontage.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\RepRootMotionMontage.h
============================================================

SECTION starting at line 26 (left) / 26 (right):
----------------------------------------
  CONTEXT (line 26):     bool bRelativeRotation;
  CONTEXT (line 27): 
  CONTEXT (line 28):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 29):     float Position;
  ADDED   (line 29):     float position;
  CONTEXT (line 30): 
  CONTEXT (line 31):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 32):     FVector_NetQuantize100 Location;