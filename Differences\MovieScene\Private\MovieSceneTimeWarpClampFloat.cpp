DIFFERENCES IN: MovieScene\Private\MovieSceneTimeWarpClampFloat.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MovieScene\Private\MovieSceneTimeWarpClampFloat.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MovieScene\Private\MovieSceneTimeWarpClampFloat.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "MovieSceneTimeWarpClampFloat.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): FMovieSceneTimeWarpClampFloat::FMovieSceneTimeWarpClampFloat() {
  REMOVED (line 4):     this->Max = 0.00f;
  ADDED   (line 4):     this->max = 0.00f;
  CONTEXT (line 5): }
  CONTEXT (line 6): 