DIFFERENCES IN: Engine\Private\InterpToMovementComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\InterpToMovementComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\InterpToMovementComponent.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): UInterpToMovementComponent::UInterpToMovementComponent(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {
  CONTEXT (line 4):     this->bAutoRegisterPhysicsVolumeUpdates = false;
  REMOVED (line 5):     this->duration = 1.00f;
  ADDED   (line 5):     this->Duration = 1.00f;
  CONTEXT (line 6):     this->bPauseOnImpact = false;
  CONTEXT (line 7):     this->bSweep = true;
  CONTEXT (line 8):     this->TeleportType = ETeleportType::None;