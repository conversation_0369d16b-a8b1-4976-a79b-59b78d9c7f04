--- Left: GranularSynth.cpp
+++ Right: GranularSynth.cpp
@@ -49,7 +49,7 @@
 void UGranularSynth::SetAttackTime(const float AttackTimeMsec) {

 }

 

-void UGranularSynth::NoteOn(const float Note, const int32 Velocity, const float duration) {

+void UGranularSynth::NoteOn(const float Note, const int32 Velocity, const float Duration) {

 }

 

 void UGranularSynth::NoteOff(const float Note, const bool bKill) {
