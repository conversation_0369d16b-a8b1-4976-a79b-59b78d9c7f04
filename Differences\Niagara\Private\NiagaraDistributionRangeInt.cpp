DIFFERENCES IN: Niagara\Private\NiagaraDistributionRangeInt.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Niagara\Private\NiagaraDistributionRangeInt.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Niagara\Private\NiagaraDistributionRangeInt.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): FNiagaraDistributionRangeInt::FNiagaraDistributionRangeInt() {
  CONTEXT (line 4):     this->Mode = ENiagaraDistributionMode::Binding;
  REMOVED (line 5):     this->Min = 0;
  REMOVED (line 6):     this->Max = 0;
  ADDED   (line 5):     this->min = 0;
  ADDED   (line 6):     this->max = 0;
  CONTEXT (line 7): }
  CONTEXT (line 8): 