DIFFERENCES IN: AIModule\Private\EnvQueryManager.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AIModule\Private\EnvQueryManager.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AIModule\Private\EnvQueryManager.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): #include "Templates/SubclassOf.h"
  CONTEXT (line 3): 
  CONTEXT (line 4): UEnvQueryManager::UEnvQueryManager() {
  REMOVED (line 5):     this->MaxAllowedTestingTime = 0.00f;
  ADDED   (line 5):     this->MaxAllowedTestingTime = 0.01f;
  CONTEXT (line 6):     this->bTestQueriesUsingBreadth = true;
  CONTEXT (line 7):     this->QueryCountWarningThreshold = 0;
  CONTEXT (line 8):     this->QueryCountWarningInterval = 30.00f;