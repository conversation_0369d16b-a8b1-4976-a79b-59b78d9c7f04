DIFFERENCES IN: AIModule\Private\AIDataProvider_Random.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AIModule\Private\AIDataProvider_Random.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AIModule\Private\AIDataProvider_Random.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "AIDataProvider_Random.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): UAIDataProvider_Random::UAIDataProvider_Random() {
  REMOVED (line 4):     this->Min = 0.00f;
  REMOVED (line 5):     this->Max = 1.00f;
  ADDED   (line 4):     this->min = 0.00f;
  ADDED   (line 5):     this->max = 1.00f;
  CONTEXT (line 6):     this->bInteger = false;
  CONTEXT (line 7): }
  CONTEXT (line 8): 