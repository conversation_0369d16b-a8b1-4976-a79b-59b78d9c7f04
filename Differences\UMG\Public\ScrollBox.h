DIFFERENCES IN: UMG\Public\ScrollBox.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\UMG\Public\ScrollBox.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\UMG\Public\ScrollBox.h
============================================================

SECTION starting at line 35 (left) / 35 (right):
----------------------------------------
  CONTEXT (line 35):     FScrollBarStyle WidgetBarStyle;
  CONTEXT (line 36): 
  CONTEXT (line 37):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 38):     TEnumAsByte<EOrientation> orientation;
  ADDED   (line 38):     TEnumAsByte<EOrientation> Orientation;
  CONTEXT (line 39): 
  CONTEXT (line 40):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 41):     ESlateVisibility ScrollBarVisibility;