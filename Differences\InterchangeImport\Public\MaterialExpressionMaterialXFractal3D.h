DIFFERENCES IN: InterchangeImport\Public\MaterialExpressionMaterialXFractal3D.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\InterchangeImport\Public\MaterialExpressionMaterialXFractal3D.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\InterchangeImport\Public\MaterialExpressionMaterialXFractal3D.h
============================================================

SECTION starting at line 9 (left) / 9 (right):
----------------------------------------
  CONTEXT (line 9):     GENERATED_BODY()
  CONTEXT (line 10): public:
  CONTEXT (line 11):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 12):     FExpressionInput Position;
  ADDED   (line 12):     FExpressionInput position;
  CONTEXT (line 13): 
  CONTEXT (line 14):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 15):     FExpressionInput Amplitude;