DIFFERENCES IN: Engine\Public\DebugTextInfo.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\DebugTextInfo.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\DebugTextInfo.h
============================================================

SECTION starting at line 27 (left) / 27 (right):
----------------------------------------
  CONTEXT (line 27):     float TimeRemaining;
  CONTEXT (line 28): 
  CONTEXT (line 29):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 30):     float duration;
  ADDED   (line 30):     float Duration;
  CONTEXT (line 31): 
  CONTEXT (line 32):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 33):     FColor TextColor;