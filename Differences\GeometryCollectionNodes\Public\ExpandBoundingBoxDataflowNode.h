DIFFERENCES IN: GeometryCollectionNodes\Public\ExpandBoundingBoxDataflowNode.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCollectionNodes\Public\ExpandBoundingBoxDataflowNode.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCollectionNodes\Public\ExpandBoundingBoxDataflowNode.h
============================================================

SECTION starting at line 13 (left) / 13 (right):
----------------------------------------
  CONTEXT (line 13):     FBox BoundingBox;
  CONTEXT (line 14): 
  CONTEXT (line 15):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 16):     FVector Min;
  ADDED   (line 16):     FVector min;
  CONTEXT (line 17): 
  CONTEXT (line 18):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 19):     FVector Max;
  ADDED   (line 19):     FVector max;
  CONTEXT (line 20): 
  CONTEXT (line 21):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 22):     FVector Center;