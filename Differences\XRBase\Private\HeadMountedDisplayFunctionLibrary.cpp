DIFFERENCES IN: XRBase\Private\HeadMountedDisplayFunctionLibrary.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\XRBase\Private\HeadMountedDisplayFunctionLibrary.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\XRBase\Private\HeadMountedDisplayFunctionLibrary.cpp
============================================================

SECTION starting at line 126 (left) / 126 (right):
----------------------------------------
  CONTEXT (line 126): void UHeadMountedDisplayFunctionLibrary::GetHandTrackingState(UObject* WorldContext, const EXRSpaceType XRSpaceType, const EControllerHand Hand, FXRHandTrackingState& HandTrackingState) {
  CONTEXT (line 127): }
  CONTEXT (line 128): 
  REMOVED (line 129): void UHeadMountedDisplayFunctionLibrary::GetDeviceWorldPose(UObject* WorldContext, const FXRDeviceId& XRDeviceId, bool& bIsTracked, FRotator& orientation, bool& bHasPositionalTracking, FVector& Position) {
  ADDED   (line 129): void UHeadMountedDisplayFunctionLibrary::GetDeviceWorldPose(UObject* WorldContext, const FXRDeviceId& XRDeviceId, bool& bIsTracked, FRotator& Orientation, bool& bHasPositionalTracking, FVector& position) {
  CONTEXT (line 130): }
  CONTEXT (line 131): 
  REMOVED (line 132): void UHeadMountedDisplayFunctionLibrary::GetDevicePose(const FXRDeviceId& XRDeviceId, bool& bIsTracked, FRotator& orientation, bool& bHasPositionalTracking, FVector& Position) {
  ADDED   (line 132): void UHeadMountedDisplayFunctionLibrary::GetDevicePose(const FXRDeviceId& XRDeviceId, bool& bIsTracked, FRotator& Orientation, bool& bHasPositionalTracking, FVector& position) {
  CONTEXT (line 133): }
  CONTEXT (line 134): 
  CONTEXT (line 135): bool UHeadMountedDisplayFunctionLibrary::GetCurrentInteractionProfile(const EControllerHand Hand, FString& InteractionProfile) {
  CONTEXT (line 136):     return false;
  CONTEXT (line 137): }
  CONTEXT (line 138): 
  REMOVED (line 139): bool UHeadMountedDisplayFunctionLibrary::GetControllerTransformForTime2(UObject* WorldContext, const int32 ControllerIndex, const FName MotionSource, FTimespan Time, bool& bTimeWasUsed, FRotator& orientation, FVector& Position, bool& bProvidedLinearVelocity, FVector& LinearVelocity, bool& bProvidedAngularVelocity, FRotator& AngularVelocity, bool& bProvidedLinearAcceleration, FVector& LinearAcceleration) {
  ADDED   (line 139): bool UHeadMountedDisplayFunctionLibrary::GetControllerTransformForTime2(UObject* WorldContext, const int32 ControllerIndex, const FName MotionSource, FTimespan Time, bool& bTimeWasUsed, FRotator& Orientation, FVector& position, bool& bProvidedLinearVelocity, FVector& LinearVelocity, bool& bProvidedAngularVelocity, FRotator& AngularVelocity, bool& bProvidedLinearAcceleration, FVector& LinearAcceleration) {
  CONTEXT (line 140):     return false;
  CONTEXT (line 141): }
  CONTEXT (line 142): 