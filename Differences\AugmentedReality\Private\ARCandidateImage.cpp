DIFFERENCES IN: AugmentedReality\Private\ARCandidateImage.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AugmentedReality\Private\ARCandidateImage.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AugmentedReality\Private\ARCandidateImage.cpp
============================================================

SECTION starting at line 4 (left) / 4 (right):
----------------------------------------
  CONTEXT (line 4):     this->CandidateTexture = NULL;
  CONTEXT (line 5):     this->Width = 0.00f;
  CONTEXT (line 6):     this->Height = 0.00f;
  REMOVED (line 7):     this->orientation = EARCandidateImageOrientation::Landscape;
  ADDED   (line 7):     this->Orientation = EARCandidateImageOrientation::Landscape;
  CONTEXT (line 8): }
  CONTEXT (line 9): 
  CONTEXT (line 10): float UARCandidateImage::GetPhysicalWidth() const {