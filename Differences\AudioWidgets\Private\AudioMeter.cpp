DIFFERENCES IN: AudioWidgets\Private\AudioMeter.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AudioWidgets\Private\AudioMeter.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AudioWidgets\Private\AudioMeter.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): UAudioMeter::UAudioMeter() {
  CONTEXT (line 4):     this->MeterChannelInfo.AddDefaulted(1);
  REMOVED (line 5):     this->orientation = Orient_Vertical;
  ADDED   (line 5):     this->Orientation = Orient_Vertical;
  CONTEXT (line 6): }
  CONTEXT (line 7): 
  CONTEXT (line 8): void UAudioMeter::SetMeterValueColor(FLinearColor InValue) {