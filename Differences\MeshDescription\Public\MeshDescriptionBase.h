DIFFERENCES IN: MeshDescription\Public\MeshDescriptionBase.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MeshDescription\Public\MeshDescriptionBase.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MeshDescription\Public\MeshDescriptionBase.h
============================================================

SECTION starting at line 17 (left) / 17 (right):
----------------------------------------
  CONTEXT (line 17):     UMeshDescriptionBase();
  CONTEXT (line 18): 
  CONTEXT (line 19):     UFUNCTION(BlueprintCallable)
  REMOVED (line 20):     void SetVertexPosition(FVertexID VertexID, const FVector& Position);
  ADDED   (line 20):     void SetVertexPosition(FVertexID VertexID, const FVector& position);
  CONTEXT (line 21): 
  CONTEXT (line 22):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 23):     void SetPolygonVertexInstances(FPolygonID PolygonID, const TArray<FVertexInstanceID>& VertexInstanceIDs);