--- Left: MaterialInstanceBasePropertyOverrides.h
+++ Right: MaterialInstanceBasePropertyOverrides.h
@@ -41,12 +41,6 @@
     uint8 bOverride_bEnableTessellation: 1;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bOverride_bFullyRough: 1;

-    

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bOverride_bFullyRoughOnMobile: 1;

-    

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bOverride_DisplacementScaling: 1;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

@@ -80,12 +74,6 @@
     uint8 bEnableTessellation: 1;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bFullyRough: 1;

-    

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bFullyRoughOnMobile: 1;

-    

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bEnableDisplacementFade: 1;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
