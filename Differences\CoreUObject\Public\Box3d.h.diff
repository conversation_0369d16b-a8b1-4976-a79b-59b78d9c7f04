--- Left: Box3d.h
+++ Right: Box3d.h
@@ -8,10 +8,10 @@
     GENERATED_BODY()

 public:

     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))

-    FVector3d Min;

+    FVector3d min;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))

-    FVector3d Max;

+    FVector3d max;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))

     bool IsValid;
