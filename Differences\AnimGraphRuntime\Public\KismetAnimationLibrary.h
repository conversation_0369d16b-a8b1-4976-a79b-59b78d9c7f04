DIFFERENCES IN: AnimGraphRuntime\Public\KismetAnimationLibrary.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AnimGraphRuntime\Public\KismetAnimationLibrary.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AnimGraphRuntime\Public\KismetAnimationLibrary.h
============================================================

SECTION starting at line 46 (left) / 46 (right):
----------------------------------------
  CONTEXT (line 46):     static float K2_CalculateVelocityFromSockets(float DeltaSeconds, USkeletalMeshComponent* Component, const FName SocketOrBoneName, const FName ReferenceSocketOrBone, TEnumAsByte<ERelativeTransformSpace> SocketSpace, FVector OffsetInBoneSpace, UPARAM(Ref) FPositionHistory& History, int32 NumberOfSamples, float VelocityMin, float VelocityMax, EEasingFuncType EasingType, const FRuntimeFloatCurve& CustomCurve);
  CONTEXT (line 47): 
  CONTEXT (line 48):     UFUNCTION(BlueprintCallable)
  REMOVED (line 49):     static float K2_CalculateVelocityFromPositionHistory(float DeltaSeconds, FVector Position, UPARAM(Ref) FPositionHistory& History, int32 NumberOfSamples, float VelocityMin, float VelocityMax);
  ADDED   (line 49):     static float K2_CalculateVelocityFromPositionHistory(float DeltaSeconds, FVector position, UPARAM(Ref) FPositionHistory& History, int32 NumberOfSamples, float VelocityMin, float VelocityMax);
  CONTEXT (line 50): 
  CONTEXT (line 51):     UFUNCTION(BlueprintCallable, BlueprintPure)
  CONTEXT (line 52):     static float CalculateDirection(const FVector& Velocity, const FRotator& BaseRotation);