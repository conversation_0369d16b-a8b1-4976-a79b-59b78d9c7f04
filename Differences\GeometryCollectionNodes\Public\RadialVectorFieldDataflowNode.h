DIFFERENCES IN: GeometryCollectionNodes\Public\RadialVectorFieldDataflowNode.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCollectionNodes\Public\RadialVectorFieldDataflowNode.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCollectionNodes\Public\RadialVectorFieldDataflowNode.h
============================================================

SECTION starting at line 20 (left) / 20 (right):
----------------------------------------
  CONTEXT (line 20):     float Magnitude;
  CONTEXT (line 21): 
  CONTEXT (line 22):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 23):     FVector Position;
  ADDED   (line 23):     FVector position;
  CONTEXT (line 24): 
  CONTEXT (line 25):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 26):     TArray<FVector> FieldVectorResult;