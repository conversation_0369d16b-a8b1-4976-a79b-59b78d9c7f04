DIFFERENCES IN: Engine\Public\AnimNotifyEvent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\AnimNotifyEvent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\AnimNotifyEvent.h
============================================================

SECTION starting at line 31 (left) / 31 (right):
----------------------------------------
  CONTEXT (line 31):     UAnimNotifyState* NotifyStateClass;
  CONTEXT (line 32): 
  CONTEXT (line 33):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 34):     float duration;
  ADDED   (line 34):     float Duration;
  CONTEXT (line 35): 
  CONTEXT (line 36):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 37):     FAnimLinkableElement EndLink;