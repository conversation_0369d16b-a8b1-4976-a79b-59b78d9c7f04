DIFFERENCES IN: Engine\Public\BlendParameter.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\BlendParameter.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\BlendParameter.h
============================================================

SECTION starting at line 10 (left) / 10 (right):
----------------------------------------
  CONTEXT (line 10):     FString DisplayName;
  CONTEXT (line 11): 
  CONTEXT (line 12):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 13):     float Min;
  ADDED   (line 13):     float min;
  CONTEXT (line 14): 
  CONTEXT (line 15):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 16):     float Max;
  ADDED   (line 16):     float max;
  CONTEXT (line 17): 
  CONTEXT (line 18):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 19):     int32 GridNum;