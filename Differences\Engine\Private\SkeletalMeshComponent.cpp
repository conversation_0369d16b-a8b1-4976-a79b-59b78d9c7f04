DIFFERENCES IN: Engine\Private\SkeletalMeshComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\SkeletalMeshComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\SkeletalMeshComponent.cpp
============================================================

SECTION starting at line 232 (left) / 232 (right):
----------------------------------------
  CONTEXT (line 232): void USkeletalMeshComponent::Play(bool bLooping) {
  CONTEXT (line 233): }
  CONTEXT (line 234): 
  REMOVED (line 235): void USkeletalMeshComponent::OverrideAnimationData(UAnimationAsset* InAnimToPlay, bool bIsLooping, bool bIsPlaying, float Position, float PlayRate) {
  ADDED   (line 235): void USkeletalMeshComponent::OverrideAnimationData(UAnimationAsset* InAnimToPlay, bool bIsLooping, bool bIsPlaying, float position, float PlayRate) {
  CONTEXT (line 236): }
  CONTEXT (line 237): 
  CONTEXT (line 238): void USkeletalMeshComponent::LinkAnimGraphByTag(FName InTag, TSubclassOf<UAnimInstance> InClass) {