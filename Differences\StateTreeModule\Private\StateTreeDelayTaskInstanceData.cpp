DIFFERENCES IN: StateTreeModule\Private\StateTreeDelayTaskInstanceData.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\StateTreeModule\Private\StateTreeDelayTaskInstanceData.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\StateTreeModule\Private\StateTreeDelayTaskInstanceData.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "StateTreeDelayTaskInstanceData.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): FStateTreeDelayTaskInstanceData::FStateTreeDelayTaskInstanceData() {
  REMOVED (line 4):     this->duration = 0.00f;
  ADDED   (line 4):     this->Duration = 0.00f;
  CONTEXT (line 5):     this->RandomDeviation = 0.00f;
  CONTEXT (line 6):     this->bRunForever = false;
  CONTEXT (line 7): }