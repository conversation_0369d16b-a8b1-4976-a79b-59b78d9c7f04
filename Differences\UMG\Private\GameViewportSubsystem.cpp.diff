--- Left: GameViewportSubsystem.cpp
+++ Right: GameViewportSubsystem.cpp
@@ -3,7 +3,7 @@
 UGameViewportSubsystem::UGameViewportSubsystem() {

 }

 

-FGameViewportWidgetSlot UGameViewportSubsystem::SetWidgetSlotPosition(FGameViewportWidgetSlot Slot, const UWidget* Widget, FVector2D Position, bool bRemoveDPIScale) {

+FGameViewportWidgetSlot UGameViewportSubsystem::SetWidgetSlotPosition(FGameViewportWidgetSlot Slot, const UWidget* Widget, FVector2D position, bool bRemoveDPIScale) {

     return FGameViewportWidgetSlot{};

 }

 
