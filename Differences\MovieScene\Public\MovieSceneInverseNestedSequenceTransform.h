DIFFERENCES IN: MovieScene\Public\MovieSceneInverseNestedSequenceTransform.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MovieScene\Public\MovieSceneInverseNestedSequenceTransform.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MovieScene\Public\MovieSceneInverseNestedSequenceTransform.h
============================================================

SECTION starting at line 10 (left) / 10 (right):
----------------------------------------
  CONTEXT (line 10): public:
  CONTEXT (line 11): private:
  CONTEXT (line 12):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 13):     FMovieSceneTimeWarpVariant timescale;
  ADDED   (line 13):     FMovieSceneTimeWarpVariant TimeScale;
  CONTEXT (line 14): 
  CONTEXT (line 15):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 16):     FFrameTime Offset;