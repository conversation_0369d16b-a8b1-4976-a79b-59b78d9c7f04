DIFFERENCES IN: MediaAssets\Public\BaseMediaSource.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MediaAssets\Public\BaseMediaSource.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MediaAssets\Public\BaseMediaSource.h
============================================================

SECTION starting at line 9 (left) / 9 (right):
----------------------------------------
  CONTEXT (line 9): public:
  CONTEXT (line 10): private:
  CONTEXT (line 11):     UPROPERTY(BlueprintReadWrite, EditAnywhere, Transient, meta=(AllowPrivateAccess=true))
  REMOVED (line 12):     FName playerName;
  ADDED   (line 12):     FName PlayerName;
  CONTEXT (line 13): 
  CONTEXT (line 14): public:
  CONTEXT (line 15):     UBaseMediaSource();