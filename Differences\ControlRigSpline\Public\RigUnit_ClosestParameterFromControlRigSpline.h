DIFFERENCES IN: ControlRigSpline\Public\RigUnit_ClosestParameterFromControlRigSpline.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\ControlRigSpline\Public\RigUnit_ClosestParameterFromControlRigSpline.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\ControlRigSpline\Public\RigUnit_ClosestParameterFromControlRigSpline.h
============================================================

SECTION starting at line 13 (left) / 13 (right):
----------------------------------------
  CONTEXT (line 13):     FControlRigSpline Spline;
  CONTEXT (line 14): 
  CONTEXT (line 15):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 16):     FVector Position;
  ADDED   (line 16):     FVector position;
  CONTEXT (line 17): 
  CONTEXT (line 18):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 19):     float U;