DIFFERENCES IN: Engine\Private\LightComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\LightComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\LightComponent.cpp
============================================================

SECTION starting at line 66 (left) / 66 (right):
----------------------------------------
  CONTEXT (line 66): void ULightComponent::SetShadowBias(float NewValue) {
  CONTEXT (line 67): }
  CONTEXT (line 68): 
  REMOVED (line 69): void ULightComponent::SetLightingChannels(bool bChannel0, bool bChannel1) {
  ADDED   (line 69): void ULightComponent::SetLightingChannels(bool bChannel0, bool bChannel1, bool bChannel2) {
  CONTEXT (line 70): }
  CONTEXT (line 71): 
  CONTEXT (line 72): void ULightComponent::SetLightFunctionScale(FVector NewLightFunctionScale) {