DIFFERENCES IN: RigVM\Public\RigVMFunction_RandomFloat.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\RigVM\Public\RigVMFunction_RandomFloat.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\RigVM\Public\RigVMFunction_RandomFloat.h
============================================================

SECTION starting at line 17 (left) / 17 (right):
----------------------------------------
  CONTEXT (line 17):     float Maximum;
  CONTEXT (line 18): 
  CONTEXT (line 19):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 20):     float duration;
  ADDED   (line 20):     float Duration;
  CONTEXT (line 21): 
  CONTEXT (line 22):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 23):     float Result;