--- Left: MaterialExpressionClamp.h
+++ Right: MaterialExpressionClamp.h
@@ -13,10 +13,10 @@
     FExpressionInput Input;

     

     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))

-    FExpressionInput Min;

+    FExpressionInput min;

     

     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))

-    FExpressionInput Max;

+    FExpressionInput max;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     TEnumAsByte<EClampMode> ClampMode;
