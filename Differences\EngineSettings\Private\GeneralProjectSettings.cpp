DIFFERENCES IN: EngineSettings\Private\GeneralProjectSettings.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\EngineSettings\Private\GeneralProjectSettings.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\EngineSettings\Private\GeneralProjectSettings.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): UGeneralProjectSettings::UGeneralProjectSettings() {
  CONTEXT (line 4):     this->CopyrightNotice = TEXT("Fill out your copyright notice in the Description page of Project Settings.");
  REMOVED (line 5):     this->ProjectVersion = TEXT("0.14.0");
  ADDED   (line 5):     this->ProjectVersion = TEXT("*******");
  CONTEXT (line 6):     this->bShouldWindowPreserveAspectRatio = true;
  CONTEXT (line 7):     this->bUseBorderlessWindow = false;
  CONTEXT (line 8):     this->bStartInVR = true;
  REMOVED (line 9):     this->bAllowWindowResize = false;
  ADDED   (line 9):     this->bAllowWindowResize = true;
  CONTEXT (line 10):     this->bAllowClose = true;
  CONTEXT (line 11):     this->bAllowMaximize = true;
  CONTEXT (line 12):     this->bAllowMinimize = true;