DIFFERENCES IN: EngineSettings\Private\GameMapsSettings.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\EngineSettings\Private\GameMapsSettings.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\EngineSettings\Private\GameMapsSettings.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "GameMapsSettings.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): UGameMapsSettings::UGameMapsSettings() {
  REMOVED (line 4):     this->bUseSplitscreen = false;
  ADDED   (line 4):     this->bUseSplitscreen = true;
  CONTEXT (line 5):     this->TwoPlayerSplitscreenLayout = ETwoPlayerSplitScreenType::Horizontal;
  CONTEXT (line 6):     this->ThreePlayerSplitscreenLayout = EThreePlayerSplitScreenType::FavorTop;
  CONTEXT (line 7):     this->FourPlayerSplitscreenLayout = EFourPlayerSplitScreenType::Grid;