DIFFERENCES IN: Engine\Public\PlayerState.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\PlayerState.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\PlayerState.h
============================================================

SECTION starting at line 21 (left) / 21 (right):
----------------------------------------
  CONTEXT (line 21):     float Score;
  CONTEXT (line 22): 
  CONTEXT (line 23):     UPROPERTY(BlueprintReadWrite, EditAnywhere, ReplicatedUsing=OnRep_PlayerId, meta=(AllowPrivateAccess=true))
  REMOVED (line 24):     int32 PlayerID;
  ADDED   (line 24):     int32 PlayerId;
  CONTEXT (line 25): 
  CONTEXT (line 26):     UPROPERTY(BlueprintReadWrite, EditAnywhere, Replicated, meta=(AllowPrivateAccess=true))
  CONTEXT (line 27):     uint8 CompressedPing;