--- Left: SplineComponent.h
+++ Right: SplineComponent.h
@@ -22,7 +22,7 @@
     int32 ReparamStepsPerSegment;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float duration;

+    float Duration;

     

     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Replicated, meta=(AllowPrivateAccess=true))

     bool bStationaryEndpoints;

@@ -373,16 +373,16 @@
     void ClearSplinePoints(bool bUpdateSpline);

     

     UFUNCTION(BlueprintCallable)

-    void AddSplineWorldPoint(const FVector& Position);

-    

-    UFUNCTION(BlueprintCallable)

-    void AddSplinePointAtIndex(const FVector& Position, int32 Index, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline);

-    

-    UFUNCTION(BlueprintCallable)

-    void AddSplinePoint(const FVector& Position, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline);

-    

-    UFUNCTION(BlueprintCallable)

-    void AddSplineLocalPoint(const FVector& Position);

+    void AddSplineWorldPoint(const FVector& position);

+    

+    UFUNCTION(BlueprintCallable)

+    void AddSplinePointAtIndex(const FVector& position, int32 Index, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline);

+    

+    UFUNCTION(BlueprintCallable)

+    void AddSplinePoint(const FVector& position, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline);

+    

+    UFUNCTION(BlueprintCallable)

+    void AddSplineLocalPoint(const FVector& position);

     

     UFUNCTION(BlueprintCallable)

     void AddPoints(const TArray<FSplinePoint>& Points, bool bUpdateSpline);
