DIFFERENCES IN: InterchangeNodes\Private\InterchangeSkeletalAnimationTrackNode.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\InterchangeNodes\Private\InterchangeSkeletalAnimationTrackNode.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\InterchangeNodes\Private\InterchangeSkeletalAnimationTrackNode.cpp
============================================================

SECTION starting at line 19 (left) / 19 (right):
----------------------------------------
  CONTEXT (line 19):     return false;
  CONTEXT (line 20): }
  CONTEXT (line 21): 
  REMOVED (line 22): bool UInterchangeSkeletalAnimationTrackNode::SetAnimationPayloadKeyForSceneNodeUid(const FString& SceneNodeUid, const FString& InUniqueID, const EInterchangeAnimationPayLoadType& InType) {
  ADDED   (line 22): bool UInterchangeSkeletalAnimationTrackNode::SetAnimationPayloadKeyForSceneNodeUid(const FString& SceneNodeUid, const FString& InUniqueId, const EInterchangeAnimationPayLoadType& InType) {
  CONTEXT (line 23):     return false;
  CONTEXT (line 24): }
  CONTEXT (line 25): 
  REMOVED (line 26): bool UInterchangeSkeletalAnimationTrackNode::SetAnimationPayloadKeyForMorphTargetNodeUid(const FString& MorphTargetNodeUid, const FString& InUniqueID, const EInterchangeAnimationPayLoadType& InType) {
  ADDED   (line 26): bool UInterchangeSkeletalAnimationTrackNode::SetAnimationPayloadKeyForMorphTargetNodeUid(const FString& MorphTargetNodeUid, const FString& InUniqueId, const EInterchangeAnimationPayLoadType& InType) {
  CONTEXT (line 27):     return false;
  CONTEXT (line 28): }
  CONTEXT (line 29): 