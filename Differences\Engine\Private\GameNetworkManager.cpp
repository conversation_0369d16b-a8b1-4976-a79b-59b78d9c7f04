DIFFERENCES IN: Engine\Private\GameNetworkManager.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\GameNetworkManager.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\GameNetworkManager.cpp
============================================================

SECTION starting at line 7 (left) / 7 (right):
----------------------------------------
  CONTEXT (line 7):     this->SeverePingThreshold = 500;
  CONTEXT (line 8):     this->AdjustedNetSpeed = 0;
  CONTEXT (line 9):     this->LastNetSpeedUpdateTime = 0.00f;
  REMOVED (line 10):     this->TotalNetBandwidth = 64000;
  ADDED   (line 10):     this->TotalNetBandwidth = 32000;
  CONTEXT (line 11):     this->MinDynamicBandwidth = 4000;
  REMOVED (line 12):     this->MaxDynamicBandwidth = 15000;
  ADDED   (line 12):     this->MaxDynamicBandwidth = 7000;
  CONTEXT (line 13):     this->bIsStandbyCheckingEnabled = false;
  CONTEXT (line 14):     this->bHasStandbyCheatTriggered = false;
  CONTEXT (line 15):     this->StandbyRxCheatTime = 0.00f;

SECTION starting at line 19 (left) / 19 (right):
----------------------------------------
  CONTEXT (line 19):     this->PercentForBadPing = 0.00f;
  CONTEXT (line 20):     this->JoinInProgressStandbyWaitTime = 0.00f;
  CONTEXT (line 21):     this->MoveRepSize = 42.00f;
  REMOVED (line 22):     this->MAXPOSITIONERRORSQUARED = 10000.00f;
  ADDED   (line 22):     this->MAXPOSITIONERRORSQUARED = 3.00f;
  CONTEXT (line 23):     this->MAXNEARZEROVELOCITYSQUARED = 9.00f;
  CONTEXT (line 24):     this->CLIENTADJUSTUPDATECOST = 180.00f;
  CONTEXT (line 25):     this->MAXCLIENTUPDATEINTERVAL = 0.25f;

SECTION starting at line 36 (left) / 36 (right):
----------------------------------------
  CONTEXT (line 36):     this->ClientErrorUpdateRateLimit = 0.00f;
  CONTEXT (line 37):     this->ClientNetCamUpdateDeltaTime = 0.02f;
  CONTEXT (line 38):     this->ClientNetCamUpdatePositionLimit = 1000.00f;
  REMOVED (line 39):     this->ClientAuthorativePosition = true;
  ADDED   (line 39):     this->ClientAuthorativePosition = false;
  CONTEXT (line 40):     this->bMovementTimeDiscrepancyDetection = false;
  CONTEXT (line 41):     this->bMovementTimeDiscrepancyResolution = false;
  CONTEXT (line 42):     this->MovementTimeDiscrepancyMaxTimeMargin = 0.25f;