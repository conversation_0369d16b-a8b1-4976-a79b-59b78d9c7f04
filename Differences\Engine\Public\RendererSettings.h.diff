--- Left: RendererSettings.h
+++ Right: RendererSettings.h
@@ -60,12 +60,6 @@
     uint8 bMobileAllowDitheredLODTransition: 1;

     

     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bMobileAllowCustomOcclusionCulling: 1;

-    

-    UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bTestDeferredAllowCustomOcclusionCulling: 1;

-    

-    UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bMobileVirtualTextures: 1;

     

     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

@@ -93,9 +87,6 @@
     uint8 bUseDXT5NormalMaps: 1;

     

     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

-    FSoftObjectPath EnvironmentMaskShapesTextureArrayPath;

-    

-    UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bVirtualTextures: 1;

     

     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

@@ -453,15 +444,6 @@
     uint8 bRoundRobinOcclusion: 1;

     

     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bMobileSupportSpaceWarp: 1;

-    

-    UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bSupportsXRSoftOcclusions: 1;

-    

-    UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

-    bool bVulkanUseEmulatedUBs;

-    

-    UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bMeshStreaming: 1;

     

     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

@@ -541,12 +523,6 @@
     

     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

     TEnumAsByte<EMobileLocalLightSetting> MobileLocalLightSetting;

-    

-    UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bMobilePackLightGridLightDataToUBO: 1;

-    

-    UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bMobileUniformLocalLights: 1;

     

     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bMobileForwardEnableClusteredReflections: 1;
