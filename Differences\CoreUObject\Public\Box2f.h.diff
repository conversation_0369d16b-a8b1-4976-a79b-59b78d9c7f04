--- Left: Box2f.h
+++ Right: Box2f.h
@@ -8,10 +8,10 @@
     GENERATED_BODY()

 public:

     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))

-    FVector2f Min;

+    FVector2f min;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))

-    FVector2f Max;

+    FVector2f max;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))

     bool bIsValid;
