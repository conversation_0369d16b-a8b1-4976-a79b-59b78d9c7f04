DIFFERENCES IN: GeometryCache\Private\GeometryCacheTrack.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCache\Private\GeometryCacheTrack.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCache\Private\GeometryCacheTrack.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "GeometryCacheTrack.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): UGeometryCacheTrack::UGeometryCacheTrack() {
  REMOVED (line 4):     this->duration = 0.00f;
  ADDED   (line 4):     this->Duration = 0.00f;
  CONTEXT (line 5): }
  CONTEXT (line 6): 
  CONTEXT (line 7): 