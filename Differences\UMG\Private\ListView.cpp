DIFFERENCES IN: UMG\Private\ListView.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\UMG\Private\ListView.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\UMG\Private\ListView.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "ListView.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): UListView::UListView() {
  REMOVED (line 4):     this->orientation = Orient_Vertical;
  ADDED   (line 4):     this->Orientation = Orient_Vertical;
  CONTEXT (line 5):     this->SelectionMode = ESelectionMode::Single;
  CONTEXT (line 6):     this->ConsumeMouseWheel = EConsumeMouseWheel::WhenScrollingPossible;
  CONTEXT (line 7):     this->bClearSelectionOnClick = false;