DIFFERENCES IN: GeometryCollectionNodes\Private\CollectionTransformSelectionByIntAttrDataflowNode.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCollectionNodes\Private\CollectionTransformSelectionByIntAttrDataflowNode.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCollectionNodes\Private\CollectionTransformSelectionByIntAttrDataflowNode.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "CollectionTransformSelectionByIntAttrDataflowNode.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): FCollectionTransformSelectionByIntAttrDataflowNode::FCollectionTransformSelectionByIntAttrDataflowNode() {
  REMOVED (line 4):     this->Min = 0;
  REMOVED (line 5):     this->Max = 0;
  ADDED   (line 4):     this->min = 0;
  ADDED   (line 5):     this->max = 0;
  CONTEXT (line 6):     this->RangeSetting = ERangeSettingEnum::Dataflow_RangeSetting_InsideRange;
  CONTEXT (line 7):     this->bInclusive = false;
  CONTEXT (line 8): }