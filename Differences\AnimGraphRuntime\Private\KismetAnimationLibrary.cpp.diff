--- Left: KismetAnimationLibrary.cpp
+++ Right: KismetAnimationLibrary.cpp
@@ -37,7 +37,7 @@
     return 0.0f;

 }

 

-float UKismetAnimationLibrary::K2_CalculateVelocityFromPositionHistory(float DeltaSeconds, FVector Position, FPositionHistory& History, int32 NumberOfSamples, float VelocityMin, float VelocityMax) {

+float UKismetAnimationLibrary::K2_CalculateVelocityFromPositionHistory(float DeltaSeconds, FVector position, FPositionHistory& History, int32 NumberOfSamples, float VelocityMin, float VelocityMax) {

     return 0.0f;

 }

 
