--- Left: Box.h
+++ Right: Box.h
@@ -8,10 +8,10 @@
     GENERATED_BODY()

 public:

     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))

-    FVector Min;

+    FVector min;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))

-    FVector Max;

+    FVector max;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))

     bool IsValid;
