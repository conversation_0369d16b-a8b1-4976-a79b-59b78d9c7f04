--- Left: SequencePlayerLibrary.h
+++ Right: SequencePlayerLibrary.h
@@ -55,7 +55,7 @@
     static FSequencePlayerReference ConvertToSequencePlayer(const FAnimNodeReference& Node, EAnimNodeReferenceConversionResult& Result);

     

     UFUNCTION(BlueprintCallable, BlueprintPure)

-    static float ComputePlayRateFromDuration(const FSequencePlayerReference& SequencePlayer, float duration);

+    static float ComputePlayRateFromDuration(const FSequencePlayerReference& SequencePlayer, float Duration);

     

 };

 
