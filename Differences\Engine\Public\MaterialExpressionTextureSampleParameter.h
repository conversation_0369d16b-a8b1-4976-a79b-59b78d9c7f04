DIFFERENCES IN: Engine\Public\MaterialExpressionTextureSampleParameter.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\MaterialExpressionTextureSampleParameter.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\MaterialExpressionTextureSampleParameter.h
============================================================

SECTION starting at line 24 (left) / 24 (right):
----------------------------------------
  CONTEXT (line 24):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 25):     FParameterChannelNames ChannelNames;
  CONTEXT (line 26): 
  REMOVED (line 27):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 28):     bool bUseOnMobile;
  REMOVED (line 29): 
  REMOVED (line 30):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 31):     bool bUseOnDesktop;
  REMOVED (line 32): 
  CONTEXT (line 33):     UMaterialExpressionTextureSampleParameter();
  CONTEXT (line 34): 
  CONTEXT (line 35): };