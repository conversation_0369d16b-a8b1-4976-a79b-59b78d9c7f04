--- Left: PostProcessSettings.cpp
+++ Right: PostProcessSettings.cpp
@@ -151,12 +151,6 @@
     this->bOverride_LPVDirectionalOcclusionFadeRange = false;

     this->bOverride_IndirectLightingColor = false;

     this->bOverride_IndirectLightingIntensity = false;

-    this->bOverride_IndirectLightingColorFar = false;

-    this->bOverride_IndirectLightingIntensityFar = false;

-    this->bOverride_IndirectLightingFarBegin = false;

-    this->bOverride_IndirectLightingFarEnd = false;

-    this->bOverride_IndirectLightingBrightnessThreshold = false;

-    this->bOverride_IndirectLightingBrightnessSmoothness = false;

     this->bOverride_ColorGradingIntensity = false;

     this->bOverride_ColorGradingLUT = false;

     this->bOverride_DepthOfFieldFocalDistance = false;

@@ -277,11 +271,6 @@
     this->BloomDirtMaskIntensity = 0.00f;

     this->DynamicGlobalIlluminationMethod = EDynamicGlobalIlluminationMethod::None;

     this->IndirectLightingIntensity = 0.00f;

-    this->IndirectLightingIntensityFar = 0.00f;

-    this->IndirectLightingFarBegin = 0.00f;

-    this->IndirectLightingFarEnd = 0.00f;

-    this->IndirectLightingBrightnessThreshold = 0.00f;

-    this->IndirectLightingBrightnessSmoothness = 0.00f;

     this->LumenRayLightingMode = ELumenRayLightingModeOverride::Default;

     this->LumenSceneLightingQuality = 0.00f;

     this->LumenSceneDetail = 0.00f;
