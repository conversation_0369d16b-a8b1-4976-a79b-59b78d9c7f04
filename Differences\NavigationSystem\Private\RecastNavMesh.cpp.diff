--- Left: RecastNavMesh.cpp
+++ Right: RecastNavMesh.cpp
@@ -26,17 +26,17 @@
     this->DrawOffset = 10.00f;

     this->bFixedTilePoolSize = false;

     this->TilePoolSize = 1024;

-    this->TileSizeUU = 2048.00f;

-    this->CellSize = 8.00f;

-    this->CellHeight = 15.00f;

+    this->TileSizeUU = 1000.00f;

+    this->CellSize = 19.00f;

+    this->CellHeight = 10.00f;

     this->AgentMaxStepHeight = 35.00f;

-    this->AgentRadius = 18.00f;

-    this->AgentHeight = 180.00f;

+    this->AgentRadius = 34.00f;

+    this->AgentHeight = 144.00f;

     this->AgentMaxSlope = 44.00f;

-    this->MinRegionArea = 100.00f;

+    this->MinRegionArea = 0.00f;

     this->MergeRegionSize = 400.00f;

     this->MaxVerticalMergeError = 2147483647;

-    this->MaxSimplificationError = 2.00f;

+    this->MaxSimplificationError = 1.30f;

     this->SimplificationElevationRatio = 0.00f;

     this->MaxSimultaneousTileGenerationJobsCount = 1024;

     this->TileNumberHardLimit = 1048576;

@@ -47,9 +47,9 @@
     this->DefaultMaxSearchNodes = 2048.00f;

     this->DefaultMaxHierarchicalSearchNodes = 2048.00f;

     this->LedgeSlopeFilterMode = ENavigationLedgeSlopeFilterMode::Recast;

-    this->RegionPartitioning = ERecastPartitioning::ChunkyMonotone;

+    this->RegionPartitioning = ERecastPartitioning::Watershed;

     this->LayerPartitioning = ERecastPartitioning::Watershed;

-    this->RegionChunkSplits = 8;

+    this->RegionChunkSplits = 2;

     this->LayerChunkSplits = 2;

     this->bSortNavigationAreasByCost = true;

     this->bIsWorldPartitioned = false;
