--- Left: Box3f.h
+++ Right: Box3f.h
@@ -8,10 +8,10 @@
     GENERATED_BODY()

 public:

     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))

-    FVector3f Min;

+    FVector3f min;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))

-    FVector3f Max;

+    FVector3f max;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))

     bool IsValid;
