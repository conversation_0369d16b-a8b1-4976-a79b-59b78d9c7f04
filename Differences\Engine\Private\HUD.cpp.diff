--- Left: HUD.cpp
+++ Right: HUD.cpp
@@ -91,10 +91,10 @@
 void AHUD::Deproject(float ScreenX, float ScreenY, FVector& WorldPosition, FVector& WorldDirection) const {

 }

 

-void AHUD::AddHitBox(FVector2D Position, FVector2D Size, FName InName, bool bConsumesInput, int32 Priority) {

+void AHUD::AddHitBox(FVector2D position, FVector2D Size, FName InName, bool bConsumesInput, int32 Priority) {

 }

 

-void AHUD::AddDebugText_Implementation(const FString& DebugText, AActor* SrcActor, float duration, FVector Offset, FVector DesiredOffset, FColor TextColor, bool bSkipOverwriteCheck, bool bAbsoluteLocation, bool bKeepAttachedToActor, UFont* InFont, float FontScale, bool bDrawShadow) {

+void AHUD::AddDebugText_Implementation(const FString& DebugText, AActor* SrcActor, float Duration, FVector Offset, FVector DesiredOffset, FColor TextColor, bool bSkipOverwriteCheck, bool bAbsoluteLocation, bool bKeepAttachedToActor, UFont* InFont, float FontScale, bool bDrawShadow) {

 }

 

 
