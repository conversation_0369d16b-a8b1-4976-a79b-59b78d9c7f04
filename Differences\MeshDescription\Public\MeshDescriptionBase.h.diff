--- Left: MeshDescriptionBase.h
+++ Right: MeshDescriptionBase.h
@@ -17,7 +17,7 @@
     UMeshDescriptionBase();

 

     UFUNCTION(BlueprintCallable)

-    void SetVertexPosition(FVertexID VertexID, const FVector& Position);

+    void SetVertexPosition(FVertexID VertexID, const FVector& position);

     

     UFUNCTION(BlueprintCallable)

     void SetPolygonVertexInstances(FPolygonID PolygonID, const TArray<FVertexInstanceID>& VertexInstanceIDs);
