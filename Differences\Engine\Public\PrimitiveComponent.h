DIFFERENCES IN: Engine\Public\PrimitiveComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\PrimitiveComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\PrimitiveComponent.h
============================================================

SECTION starting at line 167 (left) / 167 (right):
----------------------------------------
  CONTEXT (line 167):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 168):     uint8 bHasPerInstanceHitProxies: 1;
  CONTEXT (line 169): 
  REMOVED (line 170):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 171):     uint8 bShouldBeExcludedFromLightmaps: 1;
  REMOVED (line 172): 
  CONTEXT (line 173):     UPROPERTY(BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))
  CONTEXT (line 174):     uint8 CastShadow: 1;
  CONTEXT (line 175): 

SECTION starting at line 511 (left) / 508 (right):
----------------------------------------
  CONTEXT (line 511):     void SetLinearDamping(float InDamping);
  CONTEXT (line 512): 
  CONTEXT (line 513):     UFUNCTION(BlueprintCallable)
  REMOVED (line 514):     void SetLightingChannels(bool bChannel0, bool bChannel1);
  ADDED   (line 511):     void SetLightingChannels(bool bChannel0, bool bChannel1, bool bChannel2);
  CONTEXT (line 515): 
  CONTEXT (line 516):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 517):     void SetLightAttachmentsAsGroup(bool bInLightAttachmentsAsGroup);