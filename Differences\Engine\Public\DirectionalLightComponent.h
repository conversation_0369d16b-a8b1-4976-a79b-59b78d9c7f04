DIFFERENCES IN: Engine\Public\DirectionalLightComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\DirectionalLightComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\DirectionalLightComponent.h
============================================================

SECTION starting at line 67 (left) / 67 (right):
----------------------------------------
  CONTEXT (line 67): 
  CONTEXT (line 68):     UPROPERTY(BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))
  CONTEXT (line 69):     float LightSourceSoftAngle;
  REMOVED (line 70): 
  REMOVED (line 71):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 72):     float LightDistanceAttenuationBegin;
  REMOVED (line 73): 
  REMOVED (line 74):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 75):     float LightDistanceAttenuationEnd;
  REMOVED (line 76): 
  REMOVED (line 77):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 78):     float LightDistanceAttenuationMinIntensity;
  REMOVED (line 79): 
  REMOVED (line 80):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 81):     float DistantLightMinThreshold;
  REMOVED (line 82): 
  REMOVED (line 83):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 84):     float DistantLightMaxThreshold;
  CONTEXT (line 85): 
  CONTEXT (line 86):     UPROPERTY(BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))
  CONTEXT (line 87):     float ShadowSourceAngleFactor;

SECTION starting at line 179 (left) / 164 (right):
----------------------------------------
  CONTEXT (line 179):     void SetLightShaftOverrideDirection(FVector NewValue);
  CONTEXT (line 180): 
  CONTEXT (line 181):     UFUNCTION(BlueprintCallable)
  REMOVED (line 182):     void SetLightDistanceAttenuationMinIntensity(float NewValue);
  REMOVED (line 183): 
  REMOVED (line 184):     UFUNCTION(BlueprintCallable)
  REMOVED (line 185):     void SetLightDistanceAttenuationEnd(float NewValue);
  REMOVED (line 186): 
  REMOVED (line 187):     UFUNCTION(BlueprintCallable)
  REMOVED (line 188):     void SetLightDistanceAttenuationBegin(float NewValue);
  REMOVED (line 189): 
  REMOVED (line 190):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 191):     void SetForwardShadingPriority(int32 NewValue);
  CONTEXT (line 192): 
  CONTEXT (line 193):     UFUNCTION(BlueprintCallable)

SECTION starting at line 201 (left) / 177 (right):
----------------------------------------
  CONTEXT (line 201): 
  CONTEXT (line 202):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 203):     void SetDynamicShadowCascades(int32 NewValue);
  REMOVED (line 204): 
  REMOVED (line 205):     UFUNCTION(BlueprintCallable)
  REMOVED (line 206):     void SetDistantLightMinThreshold(float NewValue);
  REMOVED (line 207): 
  REMOVED (line 208):     UFUNCTION(BlueprintCallable)
  REMOVED (line 209):     void SetDistantLightMaxThreshold(float NewValue);
  CONTEXT (line 210): 
  CONTEXT (line 211):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 212):     void SetCascadeTransitionFraction(float NewValue);