DIFFERENCES IN: StateTreeModule\Public\StateTreeMakeIntervalPropertyFunctionInstanceData.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\StateTreeModule\Public\StateTreeMakeIntervalPropertyFunctionInstanceData.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\StateTreeModule\Public\StateTreeMakeIntervalPropertyFunctionInstanceData.h
============================================================

SECTION starting at line 8 (left) / 8 (right):
----------------------------------------
  CONTEXT (line 8):     GENERATED_BODY()
  CONTEXT (line 9): public:
  CONTEXT (line 10):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 11):     float Min;
  ADDED   (line 11):     float min;
  CONTEXT (line 12): 
  CONTEXT (line 13):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 14):     float Max;
  ADDED   (line 14):     float max;
  CONTEXT (line 15): 
  CONTEXT (line 16):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 17):     FFloatInterval Result;