DIFFERENCES IN: Engine\Public\Timeline.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\Timeline.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\Timeline.h
============================================================

SECTION starting at line 37 (left) / 37 (right):
----------------------------------------
  CONTEXT (line 37):     float PlayRate;
  CONTEXT (line 38): 
  CONTEXT (line 39):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 40):     float Position;
  ADDED   (line 40):     float position;
  CONTEXT (line 41): 
  CONTEXT (line 42):     UPROPERTY(BlueprintReadWrite, EditAnywhere, NotReplicated, meta=(AllowPrivateAccess=true))
  CONTEXT (line 43):     TArray<FTimelineEventEntry> Events;