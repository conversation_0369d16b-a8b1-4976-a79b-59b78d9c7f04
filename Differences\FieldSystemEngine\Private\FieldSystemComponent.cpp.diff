--- Left: FieldSystemComponent.cpp
+++ Right: FieldSystemComponent.cpp
@@ -12,19 +12,19 @@
 void UFieldSystemComponent::RemovePersistentFields() {

 }

 

-void UFieldSystemComponent::ApplyUniformVectorFalloffForce(bool Enabled, FVector Position, FVector Direction, float Radius, float Magnitude) {

+void UFieldSystemComponent::ApplyUniformVectorFalloffForce(bool Enabled, FVector position, FVector Direction, float Radius, float Magnitude) {

 }

 

-void UFieldSystemComponent::ApplyStrainField(bool Enabled, FVector Position, float Radius, float Magnitude, int32 Iterations) {

+void UFieldSystemComponent::ApplyStrainField(bool Enabled, FVector position, float Radius, float Magnitude, int32 Iterations) {

 }

 

-void UFieldSystemComponent::ApplyStayDynamicField(bool Enabled, FVector Position, float Radius) {

+void UFieldSystemComponent::ApplyStayDynamicField(bool Enabled, FVector position, float Radius) {

 }

 

-void UFieldSystemComponent::ApplyRadialVectorFalloffForce(bool Enabled, FVector Position, float Radius, float Magnitude) {

+void UFieldSystemComponent::ApplyRadialVectorFalloffForce(bool Enabled, FVector position, float Radius, float Magnitude) {

 }

 

-void UFieldSystemComponent::ApplyRadialForce(bool Enabled, FVector Position, float Magnitude) {

+void UFieldSystemComponent::ApplyRadialForce(bool Enabled, FVector position, float Magnitude) {

 }

 

 void UFieldSystemComponent::ApplyPhysicsField(bool Enabled, TEnumAsByte<EFieldPhysicsType> Target, UFieldSystemMetaData* MetaData, UFieldNodeBase* Field) {
