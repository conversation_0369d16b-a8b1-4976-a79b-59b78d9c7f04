DIFFERENCES IN: Engine\Private\MaterialExpressionTextureSampleParameter.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\MaterialExpressionTextureSampleParameter.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\MaterialExpressionTextureSampleParameter.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): UMaterialExpressionTextureSampleParameter::UMaterialExpressionTextureSampleParameter() {
  CONTEXT (line 4):     this->SortPriority = 32;
  REMOVED (line 5):     this->bUseOnMobile = true;
  REMOVED (line 6):     this->bUseOnDesktop = true;
  CONTEXT (line 7): }
  CONTEXT (line 8): 
  CONTEXT (line 9): 