--- Left: PlayerController.h
+++ Right: PlayerController.h
@@ -333,10 +333,10 @@
     void ServerUpdateCamera(FVector_NetQuantize CamLoc, int32 CamPitchAndYaw);

     

     UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)

-    void ServerUnmutePlayer(FUniqueNetIdRepl PlayerID);

-    

-    UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)

-    void ServerUnblockPlayer(FUniqueNetIdRepl PlayerID);

+    void ServerUnmutePlayer(FUniqueNetIdRepl PlayerId);

+    

+    UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)

+    void ServerUnblockPlayer(FUniqueNetIdRepl PlayerId);

     

     UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)

     void ServerToggleAILogging();

@@ -366,7 +366,7 @@
     void ServerNotifyLoadedWorld(FName WorldPackageName);

     

     UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)

-    void ServerMutePlayer(FUniqueNetIdRepl PlayerID);

+    void ServerMutePlayer(FUniqueNetIdRepl PlayerId);

     

     UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)

     void ServerExecRPC(const FString& Msg);

@@ -387,7 +387,7 @@
     void ServerCamera(FName NewMode);

     

     UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)

-    void ServerBlockPlayer(FUniqueNetIdRepl PlayerID);

+    void ServerBlockPlayer(FUniqueNetIdRepl PlayerId);

     

     UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)

     void ServerAcknowledgePossession(APawn* P);

@@ -411,7 +411,7 @@
     void PlayHapticEffect(UHapticFeedbackEffect_Base* HapticEffect, EControllerHand Hand, float Scale, bool bLoop);

     

     UFUNCTION(BlueprintCallable, meta=(Latent, LatentInfo="LatentInfo"))

-    void PlayDynamicForceFeedback(float Intensity, float duration, bool bAffectsLeftLarge, bool bAffectsLeftSmall, bool bAffectsRightLarge, bool bAffectsRightSmall, TEnumAsByte<EDynamicForceFeedbackAction::Type> Action, FLatentActionInfo LatentInfo);

+    void PlayDynamicForceFeedback(float Intensity, float Duration, bool bAffectsLeftLarge, bool bAffectsLeftSmall, bool bAffectsRightLarge, bool bAffectsRightSmall, TEnumAsByte<EDynamicForceFeedbackAction::Type> Action, FLatentActionInfo LatentInfo);

     

     UFUNCTION(BlueprintCallable, Exec)

     void Pause();

@@ -540,7 +540,7 @@
     void ClientUnmutePlayers(const TArray<FUniqueNetIdRepl>& PlayerIds);

     

     UFUNCTION(BlueprintCallable, Client, Reliable)

-    void ClientUnmutePlayer(FUniqueNetIdRepl PlayerID);

+    void ClientUnmutePlayer(FUniqueNetIdRepl PlayerId);

     

     UFUNCTION(BlueprintCallable, Client, Reliable)

     void ClientTravelInternal(const FString& URL, TEnumAsByte<ETravelType> TravelType, bool bSeamless, FGuid MapPackageGuid);

@@ -644,7 +644,7 @@
     

 public:

     UFUNCTION(BlueprintCallable, Client, Reliable)

-    void ClientMutePlayer(FUniqueNetIdRepl PlayerID);

+    void ClientMutePlayer(FUniqueNetIdRepl PlayerId);

     

     UFUNCTION(BlueprintCallable, Client, Reliable)

     void ClientMessage(const FString& S, FName Type, float MsgLifeTime);

@@ -691,7 +691,7 @@
     void ClientCancelPendingMapChange();

     

     UFUNCTION(BlueprintCallable, Client, Reliable)

-    void ClientAddTextureStreamingLoc(FVector InLoc, float duration, bool bOverrideLocation);

+    void ClientAddTextureStreamingLoc(FVector InLoc, float Duration, bool bOverrideLocation);

     

     UFUNCTION(BlueprintCallable, Client, Reliable)

     void ClientAckUpdateLevelVisibility(FName PackageName, FNetLevelVisibilityTransactionId TransactionId, bool bClientAckCanMakeVisible);
