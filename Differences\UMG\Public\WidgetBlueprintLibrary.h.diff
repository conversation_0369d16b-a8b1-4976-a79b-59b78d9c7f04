--- Left: WidgetBlueprintLibrary.h
+++ Right: WidgetBlueprintLibrary.h
@@ -160,10 +160,10 @@
     static FEventReply EndDragDrop(UPARAM(Ref) FEventReply& Reply);

     

     UFUNCTION(BlueprintCallable)

-    static void DrawTextFormatted(UPARAM(Ref) FPaintContext& Context, const FText& Text, FVector2D Position, UFont* Font, float FontSize, FName FontTypeFace, FLinearColor Tint);

-    

-    UFUNCTION(BlueprintCallable)

-    static void DrawText(UPARAM(Ref) FPaintContext& Context, const FString& InString, FVector2D Position, FLinearColor Tint);

+    static void DrawTextFormatted(UPARAM(Ref) FPaintContext& Context, const FText& Text, FVector2D position, UFont* Font, float FontSize, FName FontTypeFace, FLinearColor Tint);

+    

+    UFUNCTION(BlueprintCallable)

+    static void DrawText(UPARAM(Ref) FPaintContext& Context, const FString& InString, FVector2D position, FLinearColor Tint);

     

     UFUNCTION(BlueprintCallable)

     static void DrawSpline(UPARAM(Ref) FPaintContext& Context, FVector2D Start, FVector2D StartDir, FVector2D End, FVector2D EndDir, FLinearColor Tint, float Thickness);

@@ -175,7 +175,7 @@
     static void DrawLine(UPARAM(Ref) FPaintContext& Context, FVector2D PositionA, FVector2D PositionB, FLinearColor Tint, bool bAntiAlias, float Thickness);

     

     UFUNCTION(BlueprintCallable)

-    static void DrawBox(UPARAM(Ref) FPaintContext& Context, FVector2D Position, FVector2D Size, USlateBrushAsset* Brush, FLinearColor Tint);

+    static void DrawBox(UPARAM(Ref) FPaintContext& Context, FVector2D position, FVector2D Size, USlateBrushAsset* Brush, FLinearColor Tint);

     

     UFUNCTION(BlueprintCallable, BlueprintCosmetic)

     static void DismissAllMenus();
