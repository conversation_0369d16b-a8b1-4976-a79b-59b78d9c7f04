--- Left: InterchangeSkeletalAnimationTrackNode.cpp
+++ Right: InterchangeSkeletalAnimationTrackNode.cpp
@@ -19,11 +19,11 @@
     return false;

 }

 

-bool UInterchangeSkeletalAnimationTrackNode::SetAnimationPayloadKeyForSceneNodeUid(const FString& SceneNodeUid, const FString& InUniqueID, const EInterchangeAnimationPayLoadType& InType) {

+bool UInterchangeSkeletalAnimationTrackNode::SetAnimationPayloadKeyForSceneNodeUid(const FString& SceneNodeUid, const FString& InUniqueId, const EInterchangeAnimationPayLoadType& InType) {

     return false;

 }

 

-bool UInterchangeSkeletalAnimationTrackNode::SetAnimationPayloadKeyForMorphTargetNodeUid(const FString& MorphTargetNodeUid, const FString& InUniqueID, const EInterchangeAnimationPayLoadType& InType) {

+bool UInterchangeSkeletalAnimationTrackNode::SetAnimationPayloadKeyForMorphTargetNodeUid(const FString& MorphTargetNodeUid, const FString& InUniqueId, const EInterchangeAnimationPayLoadType& InType) {

     return false;

 }

 
