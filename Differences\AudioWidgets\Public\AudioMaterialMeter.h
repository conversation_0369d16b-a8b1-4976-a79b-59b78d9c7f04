DIFFERENCES IN: AudioWidgets\Public\AudioMaterialMeter.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AudioWidgets\Public\AudioMaterialMeter.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AudioWidgets\Public\AudioMaterialMeter.h
============================================================

SECTION starting at line 16 (left) / 16 (right):
----------------------------------------
  CONTEXT (line 16):     FAudioMaterialMeterStyle WidgetStyle;
  CONTEXT (line 17): 
  CONTEXT (line 18):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 19):     TEnumAsByte<EOrientation> orientation;
  ADDED   (line 19):     TEnumAsByte<EOrientation> Orientation;
  CONTEXT (line 20): 
  CONTEXT (line 21):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 22):     FGetMeterChannelInfo MeterChannelInfoDelegate;