DIFFERENCES IN: InterchangeCore\Public\InterchangeBaseNode.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\InterchangeCore\Public\InterchangeBaseNode.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\InterchangeCore\Public\InterchangeBaseNode.h
============================================================

SECTION starting at line 41 (left) / 41 (right):
----------------------------------------
  CONTEXT (line 41):     bool GetVector2Attribute(const FString& NodeAttributeKey, FVector2f& OutValue) const;
  CONTEXT (line 42): 
  CONTEXT (line 43):     UFUNCTION(BlueprintCallable, BlueprintPure)
  REMOVED (line 44):     FString GetUniqueId() const;
  ADDED   (line 44):     FString GetUniqueID() const;
  CONTEXT (line 45): 
  CONTEXT (line 46):     UFUNCTION(BlueprintCallable, BlueprintPure)
  CONTEXT (line 47):     void GetTargetNodeUids(TArray<FString>& OutTargetAssets) const;