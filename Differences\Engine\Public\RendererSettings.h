DIFFERENCES IN: Engine\Public\RendererSettings.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\RendererSettings.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\RendererSettings.h
============================================================

SECTION starting at line 60 (left) / 60 (right):
----------------------------------------
  CONTEXT (line 60):     uint8 bMobileAllowDitheredLODTransition: 1;
  CONTEXT (line 61): 
  CONTEXT (line 62):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 63):     uint8 bMobileAllowCustomOcclusionCulling: 1;
  REMOVED (line 64): 
  REMOVED (line 65):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 66):     uint8 bTestDeferredAllowCustomOcclusionCulling: 1;
  REMOVED (line 67): 
  REMOVED (line 68):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 69):     uint8 bMobileVirtualTextures: 1;
  CONTEXT (line 70): 
  CONTEXT (line 71):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

SECTION starting at line 93 (left) / 87 (right):
----------------------------------------
  CONTEXT (line 93):     uint8 bUseDXT5NormalMaps: 1;
  CONTEXT (line 94): 
  CONTEXT (line 95):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 96):     FSoftObjectPath EnvironmentMaskShapesTextureArrayPath;
  REMOVED (line 97): 
  REMOVED (line 98):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 99):     uint8 bVirtualTextures: 1;
  CONTEXT (line 100): 
  CONTEXT (line 101):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

SECTION starting at line 453 (left) / 444 (right):
----------------------------------------
  CONTEXT (line 453):     uint8 bRoundRobinOcclusion: 1;
  CONTEXT (line 454): 
  CONTEXT (line 455):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 456):     uint8 bMobileSupportSpaceWarp: 1;
  REMOVED (line 457): 
  REMOVED (line 458):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 459):     uint8 bSupportsXRSoftOcclusions: 1;
  REMOVED (line 460): 
  REMOVED (line 461):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 462):     bool bVulkanUseEmulatedUBs;
  REMOVED (line 463): 
  REMOVED (line 464):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 465):     uint8 bMeshStreaming: 1;
  CONTEXT (line 466): 
  CONTEXT (line 467):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))

SECTION starting at line 541 (left) / 523 (right):
----------------------------------------
  CONTEXT (line 541): 
  CONTEXT (line 542):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 543):     TEnumAsByte<EMobileLocalLightSetting> MobileLocalLightSetting;
  REMOVED (line 544): 
  REMOVED (line 545):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 546):     uint8 bMobilePackLightGridLightDataToUBO: 1;
  REMOVED (line 547): 
  REMOVED (line 548):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 549):     uint8 bMobileUniformLocalLights: 1;
  CONTEXT (line 550): 
  CONTEXT (line 551):     UPROPERTY(BlueprintReadWrite, Config, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 552):     uint8 bMobileForwardEnableClusteredReflections: 1;