DIFFERENCES IN: MovieScene\Public\MovieSceneTimeWarpLoop.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MovieScene\Public\MovieSceneTimeWarpLoop.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MovieScene\Public\MovieSceneTimeWarpLoop.h
============================================================

SECTION starting at line 8 (left) / 8 (right):
----------------------------------------
  CONTEXT (line 8):     GENERATED_BODY()
  CONTEXT (line 9): public:
  CONTEXT (line 10):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 11):     FFrameNumber duration;
  ADDED   (line 11):     FFrameNumber Duration;
  CONTEXT (line 12): 
  CONTEXT (line 13):     MOVIESCENE_API FMovieSceneTimeWarpLoop();
  CONTEXT (line 14): };