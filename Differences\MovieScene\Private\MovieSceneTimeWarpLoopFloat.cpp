DIFFERENCES IN: MovieScene\Private\MovieSceneTimeWarpLoopFloat.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MovieScene\Private\MovieSceneTimeWarpLoopFloat.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MovieScene\Private\MovieSceneTimeWarpLoopFloat.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "MovieSceneTimeWarpLoopFloat.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): FMovieSceneTimeWarpLoopFloat::FMovieSceneTimeWarpLoopFloat() {
  REMOVED (line 4):     this->duration = 0.00f;
  ADDED   (line 4):     this->Duration = 0.00f;
  CONTEXT (line 5): }
  CONTEXT (line 6): 