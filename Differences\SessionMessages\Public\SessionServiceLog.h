DIFFERENCES IN: SessionMessages\Public\SessionServiceLog.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\SessionMessages\Public\SessionServiceLog.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\SessionMessages\Public\SessionServiceLog.h
============================================================

SECTION starting at line 14 (left) / 14 (right):
----------------------------------------
  CONTEXT (line 14):     FString Data;
  CONTEXT (line 15): 
  CONTEXT (line 16):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 17):     FGuid InstanceID;
  ADDED   (line 17):     FGuid InstanceId;
  CONTEXT (line 18): 
  CONTEXT (line 19):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 20):     double TimeSeconds;