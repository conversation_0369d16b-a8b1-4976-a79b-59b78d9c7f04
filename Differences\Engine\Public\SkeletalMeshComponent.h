DIFFERENCES IN: Engine\Public\SkeletalMeshComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\SkeletalMeshComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\SkeletalMeshComponent.h
============================================================

SECTION starting at line 424 (left) / 424 (right):
----------------------------------------
  CONTEXT (line 424):     void Play(bool bLooping);
  CONTEXT (line 425): 
  CONTEXT (line 426):     UFUNCTION(BlueprintCallable)
  REMOVED (line 427):     void OverrideAnimationData(UAnimationAsset* InAnimToPlay, bool bIsLooping, bool bIsPlaying, float Position, float PlayRate);
  ADDED   (line 427):     void OverrideAnimationData(UAnimationAsset* InAnimToPlay, bool bIsLooping, bool bIsPlaying, float position, float PlayRate);
  CONTEXT (line 428): 
  CONTEXT (line 429):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 430):     void LinkAnimGraphByTag(FName InTag, TSubclassOf<UAnimInstance> InClass);