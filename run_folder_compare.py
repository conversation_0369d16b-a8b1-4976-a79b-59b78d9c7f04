#!/usr/bin/env python3
"""
Simple launcher for the Folder Comparison GUI Tool
"""

import sys
import os

# Add current directory to path to ensure imports work
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from folder_compare import main
    main()
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Please ensure all required dependencies are installed.")
    input("Press Enter to exit...")
except Exception as e:
    print(f"An error occurred: {e}")
    input("Press Enter to exit...")
