--- Left: ModularSynthComponent.cpp
+++ Right: ModularSynthComponent.cpp
@@ -173,7 +173,7 @@
 void UModularSynthComponent::SetAttackTime(float AttackTimeMsec) {

 }

 

-void UModularSynthComponent::NoteOn(const float Note, const int32 Velocity, const float duration) {

+void UModularSynthComponent::NoteOn(const float Note, const int32 Velocity, const float Duration) {

 }

 

 void UModularSynthComponent::NoteOff(const float Note, const bool bAllNotesOff, const bool bKillAllNotes) {
