--- Left: LandscapeSplineMeshEntry.h
+++ Right: LandscapeSplineMeshEntry.h
@@ -35,7 +35,7 @@
     FVector Scale;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    TEnumAsByte<LandscapeSplineMeshOrientation> orientation;

+    TEnumAsByte<LandscapeSplineMeshOrientation> Orientation;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     TEnumAsByte<ESplineMeshAxis::Type> ForwardAxis;
