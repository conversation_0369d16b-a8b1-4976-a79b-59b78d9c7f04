DIFFERENCES IN: Engine\Public\PlayerCameraManager.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\PlayerCameraManager.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\PlayerCameraManager.h
============================================================

SECTION starting at line 173 (left) / 173 (right):
----------------------------------------
  CONTEXT (line 173):     UCameraShakeBase* StartCameraShake(TSubclassOf<UCameraShakeBase> ShakeClass, float Scale, ECameraShakePlaySpace PlaySpace, FRotator UserPlaySpaceRot);
  CONTEXT (line 174): 
  CONTEXT (line 175):     UFUNCTION(BlueprintCallable)
  REMOVED (line 176):     void StartCameraFade(float FromAlpha, float ToAlpha, float duration, FLinearColor Color, bool bShouldFadeAudio, bool bHoldWhenFinished);
  ADDED   (line 176):     void StartCameraFade(float FromAlpha, float ToAlpha, float Duration, FLinearColor Color, bool bShouldFadeAudio, bool bHoldWhenFinished);
  CONTEXT (line 177): 
  CONTEXT (line 178):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 179):     void SetManualCameraFade(float InFadeAmount, FLinearColor Color, bool bInFadeAudio);