DIFFERENCES IN: AudioWidgets\Public\AudioMeter.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AudioWidgets\Public\AudioMeter.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AudioWidgets\Public\AudioMeter.h
============================================================

SECTION starting at line 23 (left) / 23 (right):
----------------------------------------
  CONTEXT (line 23):     FAudioMeterStyle WidgetStyle;
  CONTEXT (line 24): 
  CONTEXT (line 25):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 26):     TEnumAsByte<EOrientation> orientation;
  ADDED   (line 26):     TEnumAsByte<EOrientation> Orientation;
  CONTEXT (line 27): 
  CONTEXT (line 28):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 29):     FLinearColor BackgroundColor;