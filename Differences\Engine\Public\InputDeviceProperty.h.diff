--- Left: InputDeviceProperty.h
+++ Right: InputDeviceProperty.h
@@ -21,7 +21,7 @@
     void ResetDeviceProperty(const FPlatformUserId PlatformUser, const FInputDeviceId DeviceID, bool bForceReset);

     

     UFUNCTION(BlueprintCallable, BlueprintNativeEvent)

-    void EvaluateDeviceProperty(const FPlatformUserId PlatformUser, const FInputDeviceId DeviceID, const float DeltaTime, const float duration);

+    void EvaluateDeviceProperty(const FPlatformUserId PlatformUser, const FInputDeviceId DeviceID, const float DeltaTime, const float Duration);

     

     UFUNCTION(BlueprintCallable)

     void ApplyDeviceProperty(const FPlatformUserId UserId, const FInputDeviceId DeviceID);
