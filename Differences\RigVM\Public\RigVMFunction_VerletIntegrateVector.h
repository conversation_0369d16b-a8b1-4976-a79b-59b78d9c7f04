DIFFERENCES IN: RigVM\Public\RigVMFunction_VerletIntegrateVector.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\RigVM\Public\RigVMFunction_VerletIntegrateVector.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\RigVM\Public\RigVMFunction_VerletIntegrateVector.h
============================================================

SECTION starting at line 25 (left) / 25 (right):
----------------------------------------
  CONTEXT (line 25):     FVector Force;
  CONTEXT (line 26): 
  CONTEXT (line 27):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 28):     FVector Position;
  ADDED   (line 28):     FVector position;
  CONTEXT (line 29): 
  CONTEXT (line 30):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 31):     FVector Velocity;