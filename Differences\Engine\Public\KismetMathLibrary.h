DIFFERENCES IN: Engine\Public\KismetMathLibrary.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\KismetMathLibrary.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\KismetMathLibrary.h
============================================================

SECTION starting at line 975 (left) / 975 (right):
----------------------------------------
  CONTEXT (line 975):     static void MinAreaRectangle(UObject* WorldContextObject, const TArray<FVector>& InPoints, const FVector& SampleSurfaceNormal, FVector& OutRectCenter, FRotator& OutRectRotation, float& OutRectLengthX, float& OutRectLengthY, bool bDebugDraw);
  CONTEXT (line 976): 
  CONTEXT (line 977):     UFUNCTION(BlueprintCallable, BlueprintPure)
  REMOVED (line 978):     static int32 Min(int32 A, int32 B);
  ADDED   (line 978):     static int32 min(int32 A, int32 B);
  CONTEXT (line 979): 
  CONTEXT (line 980):     UFUNCTION(BlueprintCallable, BlueprintPure)
  CONTEXT (line 981):     static void MedianOfIntArray(TArray<int32> IntArray, float& MedianValue);

SECTION starting at line 993 (left) / 993 (right):
----------------------------------------
  CONTEXT (line 993):     static int64 MaxInt64(int64 A, int64 B);
  CONTEXT (line 994): 
  CONTEXT (line 995):     UFUNCTION(BlueprintCallable, BlueprintPure)
  REMOVED (line 996):     static int32 Max(int32 A, int32 B);
  ADDED   (line 996):     static int32 max(int32 A, int32 B);
  CONTEXT (line 997): 
  CONTEXT (line 998):     UFUNCTION(BlueprintCallable, BlueprintPure)
  CONTEXT (line 999):     static FVector4 Matrix_TransformVector4(const FMatrix& M, FVector4 V);