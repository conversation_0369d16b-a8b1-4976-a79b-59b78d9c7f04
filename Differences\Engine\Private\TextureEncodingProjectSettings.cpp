DIFFERENCES IN: Engine\Private\TextureEncodingProjectSettings.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\TextureEncodingProjectSettings.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\TextureEncodingProjectSettings.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): UTextureEncodingProjectSettings::UTextureEncodingProjectSettings() {
  CONTEXT (line 4):     this->bSharedLinearTextureEncoding = false;
  REMOVED (line 5):     this->bFinalUsesRDO = true;
  REMOVED (line 6):     this->FinalRDOLambda = 45;
  REMOVED (line 7):     this->FinalEffortLevel = ETextureEncodeEffort::High;
  ADDED   (line 5):     this->bFinalUsesRDO = false;
  ADDED   (line 6):     this->FinalRDOLambda = 30;
  ADDED   (line 7):     this->FinalEffortLevel = ETextureEncodeEffort::Normal;
  CONTEXT (line 8):     this->FinalUniversalTiling = ETextureUniversalTiling::Disabled;
  CONTEXT (line 9):     this->bFastUsesRDO = false;
  REMOVED (line 10):     this->FastRDOLambda = 45;
  ADDED   (line 10):     this->FastRDOLambda = 30;
  CONTEXT (line 11):     this->FastEffortLevel = ETextureEncodeEffort::Normal;
  CONTEXT (line 12):     this->FastUniversalTiling = ETextureUniversalTiling::Disabled;
  CONTEXT (line 13):     this->CookUsesSpeed = ETextureEncodeSpeed::Final;
  REMOVED (line 14):     this->EditorUsesSpeed = ETextureEncodeSpeed::Fast;
  ADDED   (line 14):     this->EditorUsesSpeed = ETextureEncodeSpeed::FinalIfAvailable;
  CONTEXT (line 15): }
  CONTEXT (line 16): 
  CONTEXT (line 17): 