DIFFERENCES IN: UMG\Public\GameViewportSubsystem.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\UMG\Public\GameViewportSubsystem.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\UMG\Public\GameViewportSubsystem.h
============================================================

SECTION starting at line 15 (left) / 15 (right):
----------------------------------------
  CONTEXT (line 15):     UGameViewportSubsystem();
  CONTEXT (line 16): 
  CONTEXT (line 17):     UFUNCTION(BlueprintCallable)
  REMOVED (line 18):     static FGameViewportWidgetSlot SetWidgetSlotPosition(FGameViewportWidgetSlot Slot, const UWidget* Widget, FVector2D Position, bool bRemoveDPIScale);
  ADDED   (line 18):     static FGameViewportWidgetSlot SetWidgetSlotPosition(FGameViewportWidgetSlot Slot, const UWidget* Widget, FVector2D position, bool bRemoveDPIScale);
  CONTEXT (line 19): 
  CONTEXT (line 20):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 21):     static FGameViewportWidgetSlot SetWidgetSlotDesiredSize(FGameViewportWidgetSlot Slot, FVector2D Size);