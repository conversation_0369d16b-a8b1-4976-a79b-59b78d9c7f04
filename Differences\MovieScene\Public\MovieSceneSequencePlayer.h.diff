--- Left: MovieSceneSequencePlayer.h
+++ Right: MovieSceneSequencePlayer.h
@@ -102,7 +102,7 @@
     void SetWeight(double InWeight);

     

     UFUNCTION(BlueprintCallable)

-    void SetTimeRange(float NewStartTime, float duration);

+    void SetTimeRange(float NewStartTime, float Duration);

     

     UFUNCTION(BlueprintCallable)

     void SetPlayRate(float PlayRate);

@@ -117,7 +117,7 @@
     void SetFrameRate(FFrameRate FrameRate);

     

     UFUNCTION(BlueprintCallable)

-    void SetFrameRange(int32 StartFrame, int32 duration, float SubFrames);

+    void SetFrameRange(int32 StartFrame, int32 Duration, float SubFrames);

     

     UFUNCTION(BlueprintCallable)

     void SetDisableCameraCuts(bool bInDisableCameraCuts);
