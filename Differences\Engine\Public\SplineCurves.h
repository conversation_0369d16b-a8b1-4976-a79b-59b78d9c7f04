DIFFERENCES IN: Engine\Public\SplineCurves.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\SplineCurves.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\SplineCurves.h
============================================================

SECTION starting at line 10 (left) / 10 (right):
----------------------------------------
  CONTEXT (line 10):     GENERATED_BODY()
  CONTEXT (line 11): public:
  CONTEXT (line 12):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 13):     FInterpCurveVector Position;
  ADDED   (line 13):     FInterpCurveVector position;
  CONTEXT (line 14): 
  CONTEXT (line 15):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 16):     FInterpCurveQuat Rotation;