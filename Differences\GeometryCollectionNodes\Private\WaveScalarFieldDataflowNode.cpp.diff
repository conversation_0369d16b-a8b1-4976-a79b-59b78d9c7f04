--- Left: WaveScalarFieldDataflowNode.cpp
+++ Right: WaveScalarFieldDataflowNode.cpp
@@ -2,7 +2,7 @@
 

 FWaveScalarFieldDataflowNode::FWaveScalarFieldDataflowNode() {

     this->Magnitude = 0.00f;

-    this->WaveLength = 0.00f;

+    this->Wavelength = 0.00f;

     this->Period = 0.00f;

     this->FunctionType = EDataflowWaveFunctionType::Dataflow_WaveFunctionType_Cosine;

     this->FalloffType = EDataflowFieldFalloffType::Dataflow_FieldFalloffType_None;
