--- Left: NiagaraPreviewAxis_InterpParamVector4.h
+++ Right: NiagaraPreviewAxis_InterpParamVector4.h
@@ -10,10 +10,10 @@
 public:

 private:

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    FVector4 Min;

+    FVector4 min;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    FVector4 Max;

+    FVector4 max;

     

 public:

     UNiagaraPreviewAxis_InterpParamVector4();
