DIFFERENCES IN: Landscape\Public\LandscapeSplineMeshEntry.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Landscape\Public\LandscapeSplineMeshEntry.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Landscape\Public\LandscapeSplineMeshEntry.h
============================================================

SECTION starting at line 35 (left) / 35 (right):
----------------------------------------
  CONTEXT (line 35):     FVector Scale;
  CONTEXT (line 36): 
  CONTEXT (line 37):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 38):     TEnumAsByte<LandscapeSplineMeshOrientation> orientation;
  ADDED   (line 38):     TEnumAsByte<LandscapeSplineMeshOrientation> Orientation;
  CONTEXT (line 39): 
  CONTEXT (line 40):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 41):     TEnumAsByte<ESplineMeshAxis::Type> ForwardAxis;