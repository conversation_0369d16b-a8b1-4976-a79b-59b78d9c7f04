--- Left: DirectionalLightComponent.h
+++ Right: DirectionalLightComponent.h
@@ -67,21 +67,6 @@
     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))

     float LightSourceSoftAngle;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float LightDistanceAttenuationBegin;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float LightDistanceAttenuationEnd;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float LightDistanceAttenuationMinIntensity;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float DistantLightMinThreshold;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float DistantLightMaxThreshold;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))

     float ShadowSourceAngleFactor;

@@ -179,15 +164,6 @@
     void SetLightShaftOverrideDirection(FVector NewValue);

     

     UFUNCTION(BlueprintCallable)

-    void SetLightDistanceAttenuationMinIntensity(float NewValue);

-    

-    UFUNCTION(BlueprintCallable)

-    void SetLightDistanceAttenuationEnd(float NewValue);

-    

-    UFUNCTION(BlueprintCallable)

-    void SetLightDistanceAttenuationBegin(float NewValue);

-    

-    UFUNCTION(BlueprintCallable)

     void SetForwardShadingPriority(int32 NewValue);

     

     UFUNCTION(BlueprintCallable)

@@ -201,12 +177,6 @@
     

     UFUNCTION(BlueprintCallable)

     void SetDynamicShadowCascades(int32 NewValue);

-    

-    UFUNCTION(BlueprintCallable)

-    void SetDistantLightMinThreshold(float NewValue);

-    

-    UFUNCTION(BlueprintCallable)

-    void SetDistantLightMaxThreshold(float NewValue);

     

     UFUNCTION(BlueprintCallable)

     void SetCascadeTransitionFraction(float NewValue);
