--- Left: KismetMathLibrary.h
+++ Right: KismetMathLibrary.h
@@ -975,7 +975,7 @@
     static void MinAreaRectangle(UObject* WorldContextObject, const TArray<FVector>& InPoints, const FVector& SampleSurfaceNormal, FVector& OutRectCenter, FRotator& OutRectRotation, float& OutRectLengthX, float& OutRectLengthY, bool bDebugDraw);

     

     UFUNCTION(BlueprintCallable, BlueprintPure)

-    static int32 Min(int32 A, int32 B);

+    static int32 min(int32 A, int32 B);

     

     UFUNCTION(BlueprintCallable, BlueprintPure)

     static void MedianOfIntArray(TArray<int32> IntArray, float& MedianValue);

@@ -993,7 +993,7 @@
     static int64 MaxInt64(int64 A, int64 B);

     

     UFUNCTION(BlueprintCallable, BlueprintPure)

-    static int32 Max(int32 A, int32 B);

+    static int32 max(int32 A, int32 B);

     

     UFUNCTION(BlueprintCallable, BlueprintPure)

     static FVector4 Matrix_TransformVector4(const FMatrix& M, FVector4 V);
