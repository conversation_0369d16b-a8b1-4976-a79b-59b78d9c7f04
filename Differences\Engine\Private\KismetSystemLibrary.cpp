DIFFERENCES IN: Engine\Private\KismetSystemLibrary.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\KismetSystemLibrary.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\KismetSystemLibrary.cpp
============================================================

SECTION starting at line 147 (left) / 147 (right):
----------------------------------------
  CONTEXT (line 147): void UKismetSystemLibrary::SetBoolPropertyByName(UObject* Object, FName PropertyName, bool Value) {
  CONTEXT (line 148): }
  CONTEXT (line 149): 
  REMOVED (line 150): void UKismetSystemLibrary::RetriggerableDelay(const UObject* WorldContextObject, float duration, FLatentActionInfo LatentInfo) {
  ADDED   (line 150): void UKismetSystemLibrary::RetriggerableDelay(const UObject* WorldContextObject, float Duration, FLatentActionInfo LatentInfo) {
  CONTEXT (line 151): }
  CONTEXT (line 152): 
  CONTEXT (line 153): void UKismetSystemLibrary::ResetGamepadAssignmentToController(int32 ControllerId) {

SECTION starting at line 165 (left) / 165 (right):
----------------------------------------
  CONTEXT (line 165): void UKismetSystemLibrary::PrintWarning(const FString& InString) {
  CONTEXT (line 166): }
  CONTEXT (line 167): 
  REMOVED (line 168): void UKismetSystemLibrary::PrintText(const UObject* WorldContextObject, const FText InText, bool bPrintToScreen, bool bPrintToLog, FLinearColor TextColor, float duration, const FName Key) {
  REMOVED (line 169): }
  REMOVED (line 170): 
  REMOVED (line 171): void UKismetSystemLibrary::PrintString(const UObject* WorldContextObject, const FString& InString, bool bPrintToScreen, bool bPrintToLog, FLinearColor TextColor, float duration, const FName Key) {
  ADDED   (line 168): void UKismetSystemLibrary::PrintText(const UObject* WorldContextObject, const FText InText, bool bPrintToScreen, bool bPrintToLog, FLinearColor TextColor, float Duration, const FName Key) {
  ADDED   (line 169): }
  ADDED   (line 170): 
  ADDED   (line 171): void UKismetSystemLibrary::PrintString(const UObject* WorldContextObject, const FString& InString, bool bPrintToScreen, bool bPrintToLog, FLinearColor TextColor, float Duration, const FName Key) {
  CONTEXT (line 172): }
  CONTEXT (line 173): 
  CONTEXT (line 174): bool UKismetSystemLibrary::ParseParamValue(const FString& InString, const FString& InParam, FString& OutValue) {

SECTION starting at line 767 (left) / 767 (right):
----------------------------------------
  CONTEXT (line 767):     return 0;
  CONTEXT (line 768): }
  CONTEXT (line 769): 
  REMOVED (line 770): void UKismetSystemLibrary::DrawDebugString(const UObject* WorldContextObject, const FVector TextLocation, const FString& Text, AActor* TestBaseActor, FLinearColor TextColor, float duration) {
  REMOVED (line 771): }
  REMOVED (line 772): 
  REMOVED (line 773): void UKismetSystemLibrary::DrawDebugSphere(const UObject* WorldContextObject, const FVector Center, float Radius, int32 Segments, FLinearColor LineColor, float duration, float Thickness) {
  REMOVED (line 774): }
  REMOVED (line 775): 
  REMOVED (line 776): void UKismetSystemLibrary::DrawDebugPoint(const UObject* WorldContextObject, const FVector Position, float Size, FLinearColor PointColor, float duration) {
  REMOVED (line 777): }
  REMOVED (line 778): 
  REMOVED (line 779): void UKismetSystemLibrary::DrawDebugPlane(const UObject* WorldContextObject, const FPlane& PlaneCoordinates, const FVector Location, float Size, FLinearColor PlaneColor, float duration) {
  REMOVED (line 780): }
  REMOVED (line 781): 
  REMOVED (line 782): void UKismetSystemLibrary::DrawDebugLine(const UObject* WorldContextObject, const FVector LineStart, const FVector LineEnd, FLinearColor LineColor, float duration, float Thickness) {
  REMOVED (line 783): }
  REMOVED (line 784): 
  REMOVED (line 785): void UKismetSystemLibrary::DrawDebugFrustum(const UObject* WorldContextObject, const FTransform& FrustumTransform, FLinearColor FrustumColor, float duration, float Thickness) {
  REMOVED (line 786): }
  REMOVED (line 787): 
  REMOVED (line 788): void UKismetSystemLibrary::DrawDebugFloatHistoryTransform(const UObject* WorldContextObject, const FDebugFloatHistory& FloatHistory, const FTransform& DrawTransform, FVector2D DrawSize, FLinearColor DrawColor, float duration) {
  REMOVED (line 789): }
  REMOVED (line 790): 
  REMOVED (line 791): void UKismetSystemLibrary::DrawDebugFloatHistoryLocation(const UObject* WorldContextObject, const FDebugFloatHistory& FloatHistory, FVector DrawLocation, FVector2D DrawSize, FLinearColor DrawColor, float duration) {
  REMOVED (line 792): }
  REMOVED (line 793): 
  REMOVED (line 794): void UKismetSystemLibrary::DrawDebugCylinder(const UObject* WorldContextObject, const FVector Start, const FVector End, float Radius, int32 Segments, FLinearColor LineColor, float duration, float Thickness) {
  REMOVED (line 795): }
  REMOVED (line 796): 
  REMOVED (line 797): void UKismetSystemLibrary::DrawDebugCoordinateSystem(const UObject* WorldContextObject, const FVector AxisLoc, const FRotator AxisRot, float Scale, float duration, float Thickness) {
  REMOVED (line 798): }
  REMOVED (line 799): 
  REMOVED (line 800): void UKismetSystemLibrary::DrawDebugConeInDegrees(const UObject* WorldContextObject, const FVector Origin, const FVector Direction, float Length, float AngleWidth, float AngleHeight, int32 NumSides, FLinearColor LineColor, float duration, float Thickness) {
  REMOVED (line 801): }
  REMOVED (line 802): 
  REMOVED (line 803): void UKismetSystemLibrary::DrawDebugCone(const UObject* WorldContextObject, const FVector Origin, const FVector Direction, float Length, float AngleWidth, float AngleHeight, int32 NumSides, FLinearColor LineColor, float duration, float Thickness) {
  REMOVED (line 804): }
  REMOVED (line 805): 
  REMOVED (line 806): void UKismetSystemLibrary::DrawDebugCircle(const UObject* WorldContextObject, FVector Center, float Radius, int32 NumSegments, FLinearColor LineColor, float duration, float Thickness, FVector YAxis, FVector ZAxis, bool bDrawAxis) {
  REMOVED (line 807): }
  REMOVED (line 808): 
  REMOVED (line 809): void UKismetSystemLibrary::DrawDebugCapsule(const UObject* WorldContextObject, const FVector Center, float HalfHeight, float Radius, const FRotator Rotation, FLinearColor LineColor, float duration, float Thickness) {
  REMOVED (line 810): }
  REMOVED (line 811): 
  REMOVED (line 812): void UKismetSystemLibrary::DrawDebugCamera(const ACameraActor* CameraActor, FLinearColor CameraColor, float duration) {
  REMOVED (line 813): }
  REMOVED (line 814): 
  REMOVED (line 815): void UKismetSystemLibrary::DrawDebugBox(const UObject* WorldContextObject, const FVector Center, FVector Extent, FLinearColor LineColor, const FRotator Rotation, float duration, float Thickness) {
  REMOVED (line 816): }
  REMOVED (line 817): 
  REMOVED (line 818): void UKismetSystemLibrary::DrawDebugArrow(const UObject* WorldContextObject, const FVector LineStart, const FVector LineEnd, float ArrowSize, FLinearColor LineColor, float duration, float Thickness) {
  ADDED   (line 770): void UKismetSystemLibrary::DrawDebugString(const UObject* WorldContextObject, const FVector TextLocation, const FString& Text, AActor* TestBaseActor, FLinearColor TextColor, float Duration) {
  ADDED   (line 771): }
  ADDED   (line 772): 
  ADDED   (line 773): void UKismetSystemLibrary::DrawDebugSphere(const UObject* WorldContextObject, const FVector Center, float Radius, int32 Segments, FLinearColor LineColor, float Duration, float Thickness) {
  ADDED   (line 774): }
  ADDED   (line 775): 
  ADDED   (line 776): void UKismetSystemLibrary::DrawDebugPoint(const UObject* WorldContextObject, const FVector position, float Size, FLinearColor PointColor, float Duration) {
  ADDED   (line 777): }
  ADDED   (line 778): 
  ADDED   (line 779): void UKismetSystemLibrary::DrawDebugPlane(const UObject* WorldContextObject, const FPlane& PlaneCoordinates, const FVector Location, float Size, FLinearColor PlaneColor, float Duration) {
  ADDED   (line 780): }
  ADDED   (line 781): 
  ADDED   (line 782): void UKismetSystemLibrary::DrawDebugLine(const UObject* WorldContextObject, const FVector LineStart, const FVector LineEnd, FLinearColor LineColor, float Duration, float Thickness) {
  ADDED   (line 783): }
  ADDED   (line 784): 
  ADDED   (line 785): void UKismetSystemLibrary::DrawDebugFrustum(const UObject* WorldContextObject, const FTransform& FrustumTransform, FLinearColor FrustumColor, float Duration, float Thickness) {
  ADDED   (line 786): }
  ADDED   (line 787): 
  ADDED   (line 788): void UKismetSystemLibrary::DrawDebugFloatHistoryTransform(const UObject* WorldContextObject, const FDebugFloatHistory& FloatHistory, const FTransform& DrawTransform, FVector2D DrawSize, FLinearColor DrawColor, float Duration) {
  ADDED   (line 789): }
  ADDED   (line 790): 
  ADDED   (line 791): void UKismetSystemLibrary::DrawDebugFloatHistoryLocation(const UObject* WorldContextObject, const FDebugFloatHistory& FloatHistory, FVector DrawLocation, FVector2D DrawSize, FLinearColor DrawColor, float Duration) {
  ADDED   (line 792): }
  ADDED   (line 793): 
  ADDED   (line 794): void UKismetSystemLibrary::DrawDebugCylinder(const UObject* WorldContextObject, const FVector Start, const FVector End, float Radius, int32 Segments, FLinearColor LineColor, float Duration, float Thickness) {
  ADDED   (line 795): }
  ADDED   (line 796): 
  ADDED   (line 797): void UKismetSystemLibrary::DrawDebugCoordinateSystem(const UObject* WorldContextObject, const FVector AxisLoc, const FRotator AxisRot, float Scale, float Duration, float Thickness) {
  ADDED   (line 798): }
  ADDED   (line 799): 
  ADDED   (line 800): void UKismetSystemLibrary::DrawDebugConeInDegrees(const UObject* WorldContextObject, const FVector Origin, const FVector Direction, float Length, float AngleWidth, float AngleHeight, int32 NumSides, FLinearColor LineColor, float Duration, float Thickness) {
  ADDED   (line 801): }
  ADDED   (line 802): 
  ADDED   (line 803): void UKismetSystemLibrary::DrawDebugCone(const UObject* WorldContextObject, const FVector Origin, const FVector Direction, float Length, float AngleWidth, float AngleHeight, int32 NumSides, FLinearColor LineColor, float Duration, float Thickness) {
  ADDED   (line 804): }
  ADDED   (line 805): 
  ADDED   (line 806): void UKismetSystemLibrary::DrawDebugCircle(const UObject* WorldContextObject, FVector Center, float Radius, int32 NumSegments, FLinearColor LineColor, float Duration, float Thickness, FVector YAxis, FVector ZAxis, bool bDrawAxis) {
  ADDED   (line 807): }
  ADDED   (line 808): 
  ADDED   (line 809): void UKismetSystemLibrary::DrawDebugCapsule(const UObject* WorldContextObject, const FVector Center, float HalfHeight, float Radius, const FRotator Rotation, FLinearColor LineColor, float Duration, float Thickness) {
  ADDED   (line 810): }
  ADDED   (line 811): 
  ADDED   (line 812): void UKismetSystemLibrary::DrawDebugCamera(const ACameraActor* CameraActor, FLinearColor CameraColor, float Duration) {
  ADDED   (line 813): }
  ADDED   (line 814): 
  ADDED   (line 815): void UKismetSystemLibrary::DrawDebugBox(const UObject* WorldContextObject, const FVector Center, FVector Extent, FLinearColor LineColor, const FRotator Rotation, float Duration, float Thickness) {
  ADDED   (line 816): }
  ADDED   (line 817): 
  ADDED   (line 818): void UKismetSystemLibrary::DrawDebugArrow(const UObject* WorldContextObject, const FVector LineStart, const FVector LineEnd, float ArrowSize, FLinearColor LineColor, float Duration, float Thickness) {
  CONTEXT (line 819): }
  CONTEXT (line 820): 
  CONTEXT (line 821): bool UKismetSystemLibrary::DoesImplementInterface(const UObject* TestObject, TSubclassOf<UInterface> Interface) {

SECTION starting at line 829 (left) / 829 (right):
----------------------------------------
  CONTEXT (line 829): void UKismetSystemLibrary::DelayUntilNextTick(const UObject* WorldContextObject, FLatentActionInfo LatentInfo) {
  CONTEXT (line 830): }
  CONTEXT (line 831): 
  REMOVED (line 832): void UKismetSystemLibrary::Delay(const UObject* WorldContextObject, float duration, FLatentActionInfo LatentInfo) {
  ADDED   (line 832): void UKismetSystemLibrary::Delay(const UObject* WorldContextObject, float Duration, FLatentActionInfo LatentInfo) {
  CONTEXT (line 833): }
  CONTEXT (line 834): 
  CONTEXT (line 835): void UKismetSystemLibrary::CreateCopyForUndoBuffer(UObject* ObjectToModify) {

SECTION starting at line 968 (left) / 968 (right):
----------------------------------------
  CONTEXT (line 968): void UKismetSystemLibrary::BreakARFilter(FARFilter InARFilter, TArray<FName>& PackageNames, TArray<FName>& PackagePaths, TArray<FSoftObjectPath>& SoftObjectPaths, TArray<FTopLevelAssetPath>& ClassPaths, TSet<FTopLevelAssetPath>& RecursiveClassPathsExclusionSet, TArray<FName>& ClassNames, TSet<FName>& RecursiveClassesExclusionSet, bool& bRecursivePaths, bool& bRecursiveClasses, bool& bIncludeOnlyOnDiskAssets) {
  CONTEXT (line 969): }
  CONTEXT (line 970): 
  REMOVED (line 971): bool UKismetSystemLibrary::BoxTraceSingleForObjects(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator orientation, const TArray<TEnumAsByte<EObjectTypeQuery>>& ObjectTypes, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime) {
  REMOVED (line 972):     return false;
  REMOVED (line 973): }
  REMOVED (line 974): 
  REMOVED (line 975): bool UKismetSystemLibrary::BoxTraceSingleByProfile(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator orientation, FName ProfileName, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime) {
  REMOVED (line 976):     return false;
  REMOVED (line 977): }
  REMOVED (line 978): 
  REMOVED (line 979): bool UKismetSystemLibrary::BoxTraceSingle(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator orientation, TEnumAsByte<ETraceTypeQuery> TraceChannel, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime) {
  REMOVED (line 980):     return false;
  REMOVED (line 981): }
  REMOVED (line 982): 
  REMOVED (line 983): bool UKismetSystemLibrary::BoxTraceMultiForObjects(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator orientation, const TArray<TEnumAsByte<EObjectTypeQuery>>& ObjectTypes, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime) {
  REMOVED (line 984):     return false;
  REMOVED (line 985): }
  REMOVED (line 986): 
  REMOVED (line 987): bool UKismetSystemLibrary::BoxTraceMultiByProfile(const UObject* WorldContextObject, const FVector Start, const FVector End, FVector HalfSize, const FRotator orientation, FName ProfileName, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime) {
  REMOVED (line 988):     return false;
  REMOVED (line 989): }
  REMOVED (line 990): 
  REMOVED (line 991): bool UKismetSystemLibrary::BoxTraceMulti(const UObject* WorldContextObject, const FVector Start, const FVector End, FVector HalfSize, const FRotator orientation, TEnumAsByte<ETraceTypeQuery> TraceChannel, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime) {
  ADDED   (line 971): bool UKismetSystemLibrary::BoxTraceSingleForObjects(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator Orientation, const TArray<TEnumAsByte<EObjectTypeQuery>>& ObjectTypes, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime) {
  ADDED   (line 972):     return false;
  ADDED   (line 973): }
  ADDED   (line 974): 
  ADDED   (line 975): bool UKismetSystemLibrary::BoxTraceSingleByProfile(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator Orientation, FName ProfileName, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime) {
  ADDED   (line 976):     return false;
  ADDED   (line 977): }
  ADDED   (line 978): 
  ADDED   (line 979): bool UKismetSystemLibrary::BoxTraceSingle(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator Orientation, TEnumAsByte<ETraceTypeQuery> TraceChannel, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime) {
  ADDED   (line 980):     return false;
  ADDED   (line 981): }
  ADDED   (line 982): 
  ADDED   (line 983): bool UKismetSystemLibrary::BoxTraceMultiForObjects(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator Orientation, const TArray<TEnumAsByte<EObjectTypeQuery>>& ObjectTypes, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime) {
  ADDED   (line 984):     return false;
  ADDED   (line 985): }
  ADDED   (line 986): 
  ADDED   (line 987): bool UKismetSystemLibrary::BoxTraceMultiByProfile(const UObject* WorldContextObject, const FVector Start, const FVector End, FVector HalfSize, const FRotator Orientation, FName ProfileName, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime) {
  ADDED   (line 988):     return false;
  ADDED   (line 989): }
  ADDED   (line 990): 
  ADDED   (line 991): bool UKismetSystemLibrary::BoxTraceMulti(const UObject* WorldContextObject, const FVector Start, const FVector End, FVector HalfSize, const FRotator Orientation, TEnumAsByte<ETraceTypeQuery> TraceChannel, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime) {
  CONTEXT (line 992):     return false;
  CONTEXT (line 993): }
  CONTEXT (line 994): 