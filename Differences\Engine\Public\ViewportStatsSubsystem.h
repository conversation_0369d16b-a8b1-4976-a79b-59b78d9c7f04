DIFFERENCES IN: Engine\Public\ViewportStatsSubsystem.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\ViewportStatsSubsystem.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\ViewportStatsSubsystem.h
============================================================

SECTION starting at line 16 (left) / 16 (right):
----------------------------------------
  CONTEXT (line 16):     void RemoveDisplayDelegate(const int32 IndexToRemove);
  CONTEXT (line 17): 
  CONTEXT (line 18):     UFUNCTION(BlueprintCallable)
  REMOVED (line 19):     void AddTimedDisplay(FText Text, FLinearColor Color, float duration, const FVector2D& DisplayOffset);
  ADDED   (line 19):     void AddTimedDisplay(FText Text, FLinearColor Color, float Duration, const FVector2D& DisplayOffset);
  CONTEXT (line 20): 
  CONTEXT (line 21):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 22):     int32 AddDisplayDelegate(const FViewportDisplayCallback& Delegate);