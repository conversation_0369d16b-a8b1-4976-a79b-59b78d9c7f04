--- Left: AssetManagerSettings.cpp
+++ Right: AssetManagerSettings.cpp
@@ -1,9 +1,9 @@
 #include "AssetManagerSettings.h"

 

 UAssetManagerSettings::UAssetManagerSettings() {

-    this->PrimaryAssetTypesToScan.AddDefaulted(6);

+    this->PrimaryAssetTypesToScan.AddDefaulted(2);

     this->bOnlyCookProductionAssets = false;

-    this->bShouldManagerDetermineTypeAndName = true;

+    this->bShouldManagerDetermineTypeAndName = false;

     this->bShouldGuessTypeAndNameInEditor = true;

     this->bShouldAcquireMissingChunksOnLoad = false;

     this->bShouldWarnAboutInvalidAssets = true;
