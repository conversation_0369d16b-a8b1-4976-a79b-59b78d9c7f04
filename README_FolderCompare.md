# Folder Comparison Tool - GUI Version

A comprehensive Python application with a user-friendly GUI that recursively compares two folders and identifies files that are different, missing, or added.

## Features

- **User-friendly GUI interface** - Easy to use graphical interface
- **Recursive folder comparison** - Compares all files and subdirectories
- **Multiple comparison modes**:
  - Content comparison (MD5 hash-based)
  - File size comparison
  - Timestamp comparison
- **Real-time progress tracking** - Shows progress during comparison
- **Detailed results display** with filtering options
- **Export capabilities** - Export results to JSON or CSV formats
- **Configurable ignore patterns** - Skip files matching specific patterns
- **Color-coded results** - Visual indicators for different file statuses
- **Detailed difference viewer** - Double-click files to see actual content differences
- **Advanced filtering** - Show only differences where files exist in left folder or both
- **Text diff highlighting** - Syntax-highlighted side-by-side comparison for text files

## Requirements

- Python 3.6 or higher
- tkinter (usually included with Python)
- Standard Python libraries (os, hashlib, json, csv, threading, pathlib)

## How to Run

### Method 1: Direct execution
```bash
python folder_compare.py
```

### Method 2: Using the launcher
```bash
python run_folder_compare.py
```

## How to Use

1. **Select Folders**:
   - Click "Browse" next to "Left Folder" to select the first folder
   - Click "Browse" next to "Right Folder" to select the second folder

2. **Configure Options**:
   - **Compare file content**: Enable for accurate content comparison (slower)
   - **Compare timestamps**: Enable to detect timestamp differences
   - **Ignore patterns**: Specify patterns to ignore (e.g., `*.tmp,__pycache__,*.pyc,.git`)

3. **Start Comparison**:
   - Click "Start Comparison" to begin
   - Monitor progress in the progress bar
   - Click "Stop" to cancel if needed

4. **View Results**:
   - Results appear in the table below
   - Use the filter dropdown to show specific types of differences:
     - **All**: Show all files
     - **Different**: Show only files with differences
     - **Only in Left**: Show files that exist only in the left folder
     - **Only in Right**: Show files that exist only in the right folder
     - **Identical**: Show identical files
     - **Errors**: Show files with comparison errors
     - **Differences (Left or Both)**: Show only differences where files exist in the left folder or both folders
   - Color coding:
     - Red background: Different files
     - Blue background: Files only in left folder
     - Green background: Files only in right folder
     - Light red background: Errors

5. **View Detailed Differences**:
   - **Double-click** any file in the results to see detailed differences
   - Click **"Show Differences"** button to see summary of all differences
   - For text files, view side-by-side content differences with syntax highlighting
   - See file information including sizes, modification times, and MD5 hashes

6. **Export Results**:
   - Click "Export to JSON" for detailed machine-readable format
   - Click "Export to CSV" for spreadsheet-compatible format
   - Click "Export Differences" to save actual differences to individual files

7. **Quick Actions**:
   - Click "Show Only Differences" to quickly filter to differences where files exist in left folder or both
   - Use this to focus on meaningful changes and exclude files that only exist in the right folder

## File Status Types

- **Identical**: Files are exactly the same
- **Different**: Files exist in both folders but have differences
- **Only In Left**: Files exist only in the left folder
- **Only In Right**: Files exist only in the right folder
- **Error**: Could not read or compare the files

## Difference Types

- **Content**: File contents are different (based on MD5 hash)
- **Size**: File sizes are different
- **Timestamp**: File modification times are different

## Ignore Patterns

You can specify patterns to ignore certain files or directories:
- `*.tmp` - Ignore all .tmp files
- `__pycache__` - Ignore Python cache directories
- `*.pyc` - Ignore Python compiled files
- `.git` - Ignore Git directories
- `node_modules` - Ignore Node.js modules

## Performance Tips

- **Content comparison** is more accurate but slower for large files
- **Size comparison** is faster but may miss files with same size but different content
- Use **ignore patterns** to skip unnecessary files and improve performance
- For very large directories, consider comparing smaller subdirectories first

## Export Formats

### JSON Export
Contains complete comparison data including:
- Comparison metadata (folders, timestamp, statistics)
- Detailed file information for each compared file
- File sizes, modification times, and hash values

### CSV Export
Spreadsheet-friendly format with columns:
- File Path
- Status
- Difference Type
- Left Size
- Right Size
- Left Modified
- Right Modified

### Export Differences
**NEW FEATURE**: Exports actual differences to individual files maintaining directory structure:

**For Text Files:**
- Creates files with the same path and name as originals
- Contains only the different lines in a readable format
- Shows which lines were added, removed, or modified
- Also creates `.diff` files with unified diff format

**For Binary Files:**
- Creates `.info.txt` files with file comparison information
- Shows file sizes, modification times, and hash differences

**Directory Structure:**
```
output_folder/
├── path/to/file1.txt          # Readable differences
├── path/to/file1.txt.diff     # Unified diff
├── path/to/file2.cpp          # Readable differences
├── path/to/file2.cpp.diff     # Unified diff
└── path/to/binary.exe.info.txt # Binary file info
```

This feature is perfect for:
- **Code reviews** - See exactly what changed in each file
- **Documentation** - Create change logs with specific differences
- **Analysis** - Process differences programmatically
- **Archiving** - Save differences for future reference

## Example Use Cases

1. **Software Development**: Compare different versions of code repositories
2. **Backup Verification**: Ensure backup copies match original files
3. **System Migration**: Verify file transfers between systems
4. **Content Management**: Track changes in document repositories
5. **Quality Assurance**: Compare test environments with production

## Troubleshooting

- **Permission Errors**: Ensure you have read access to both folders
- **Large Directories**: Use ignore patterns to skip unnecessary files
- **Memory Issues**: For very large comparisons, consider comparing subdirectories separately
- **Slow Performance**: Disable content comparison for faster results (less accurate)

## Technical Details

The application uses:
- **MD5 hashing** for content comparison
- **Multi-threading** to keep the GUI responsive during comparison
- **tkinter** for the graphical interface
- **Recursive directory traversal** using Python's pathlib
- **Pattern matching** using fnmatch for ignore patterns
