# Folder Comparison Tool - GUI Version

A comprehensive Python application with a user-friendly GUI that recursively compares two folders and identifies files that are different, missing, or added.

## Features

- **User-friendly GUI interface** - Easy to use graphical interface
- **Recursive folder comparison** - Compares all files and subdirectories
- **Multiple comparison modes**:
  - Content comparison (MD5 hash-based)
  - File size comparison
  - Timestamp comparison
- **Real-time progress tracking** - Shows progress during comparison
- **Detailed results display** with filtering options
- **Export capabilities** - Export results to JSON or CSV formats
- **Configurable ignore patterns** - Skip files matching specific patterns
- **Color-coded results** - Visual indicators for different file statuses

## Requirements

- Python 3.6 or higher
- tkinter (usually included with Python)
- Standard Python libraries (os, hashlib, json, csv, threading, pathlib)

## How to Run

### Method 1: Direct execution
```bash
python folder_compare.py
```

### Method 2: Using the launcher
```bash
python run_folder_compare.py
```

## How to Use

1. **Select Folders**:
   - Click "Browse" next to "Left Folder" to select the first folder
   - Click "Browse" next to "Right Folder" to select the second folder

2. **Configure Options**:
   - **Compare file content**: Enable for accurate content comparison (slower)
   - **Compare timestamps**: Enable to detect timestamp differences
   - **Ignore patterns**: Specify patterns to ignore (e.g., `*.tmp,__pycache__,*.pyc,.git`)

3. **Start Comparison**:
   - Click "Start Comparison" to begin
   - Monitor progress in the progress bar
   - Click "Stop" to cancel if needed

4. **View Results**:
   - Results appear in the table below
   - Use the filter dropdown to show specific types of differences
   - Color coding:
     - Red background: Different files
     - Blue background: Files only in left folder
     - Green background: Files only in right folder
     - Light red background: Errors

5. **Export Results**:
   - Click "Export to JSON" for detailed machine-readable format
   - Click "Export to CSV" for spreadsheet-compatible format

## File Status Types

- **Identical**: Files are exactly the same
- **Different**: Files exist in both folders but have differences
- **Only In Left**: Files exist only in the left folder
- **Only In Right**: Files exist only in the right folder
- **Error**: Could not read or compare the files

## Difference Types

- **Content**: File contents are different (based on MD5 hash)
- **Size**: File sizes are different
- **Timestamp**: File modification times are different

## Ignore Patterns

You can specify patterns to ignore certain files or directories:
- `*.tmp` - Ignore all .tmp files
- `__pycache__` - Ignore Python cache directories
- `*.pyc` - Ignore Python compiled files
- `.git` - Ignore Git directories
- `node_modules` - Ignore Node.js modules

## Performance Tips

- **Content comparison** is more accurate but slower for large files
- **Size comparison** is faster but may miss files with same size but different content
- Use **ignore patterns** to skip unnecessary files and improve performance
- For very large directories, consider comparing smaller subdirectories first

## Export Formats

### JSON Export
Contains complete comparison data including:
- Comparison metadata (folders, timestamp, statistics)
- Detailed file information for each compared file
- File sizes, modification times, and hash values

### CSV Export
Spreadsheet-friendly format with columns:
- File Path
- Status
- Difference Type
- Left Size
- Right Size
- Left Modified
- Right Modified

## Example Use Cases

1. **Software Development**: Compare different versions of code repositories
2. **Backup Verification**: Ensure backup copies match original files
3. **System Migration**: Verify file transfers between systems
4. **Content Management**: Track changes in document repositories
5. **Quality Assurance**: Compare test environments with production

## Troubleshooting

- **Permission Errors**: Ensure you have read access to both folders
- **Large Directories**: Use ignore patterns to skip unnecessary files
- **Memory Issues**: For very large comparisons, consider comparing subdirectories separately
- **Slow Performance**: Disable content comparison for faster results (less accurate)

## Technical Details

The application uses:
- **MD5 hashing** for content comparison
- **Multi-threading** to keep the GUI responsive during comparison
- **tkinter** for the graphical interface
- **Recursive directory traversal** using Python's pathlib
- **Pattern matching** using fnmatch for ignore patterns
