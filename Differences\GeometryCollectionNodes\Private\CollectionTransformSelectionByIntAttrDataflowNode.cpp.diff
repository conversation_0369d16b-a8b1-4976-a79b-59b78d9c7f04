--- Left: CollectionTransformSelectionByIntAttrDataflowNode.cpp
+++ Right: CollectionTransformSelectionByIntAttrDataflowNode.cpp
@@ -1,8 +1,8 @@
 #include "CollectionTransformSelectionByIntAttrDataflowNode.h"

 

 FCollectionTransformSelectionByIntAttrDataflowNode::FCollectionTransformSelectionByIntAttrDataflowNode() {

-    this->Min = 0;

-    this->Max = 0;

+    this->min = 0;

+    this->max = 0;

     this->RangeSetting = ERangeSettingEnum::Dataflow_RangeSetting_InsideRange;

     this->bInclusive = false;

 }
