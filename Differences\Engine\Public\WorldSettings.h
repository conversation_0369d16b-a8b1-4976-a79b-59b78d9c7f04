DIFFERENCES IN: Engine\Public\WorldSettings.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\WorldSettings.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\WorldSettings.h
============================================================

SECTION starting at line 36 (left) / 36 (right):
----------------------------------------
  CONTEXT (line 36):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 37):     TEnumAsByte<EVisibilityAggressiveness> VisibilityAggressiveness;
  CONTEXT (line 38): 
  REMOVED (line 39):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 40):     float VisibilityMaxDrawDistanceScale;
  REMOVED (line 41): 
  CONTEXT (line 42):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 43):     uint8 bPrecomputeVisibility: 1;
  CONTEXT (line 44): 