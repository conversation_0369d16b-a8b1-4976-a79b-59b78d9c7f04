DIFFERENCES IN: SequencerScripting\Private\MovieSceneSequenceExtensions.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\SequencerScripting\Private\MovieSceneSequenceExtensions.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\SequencerScripting\Private\MovieSceneSequenceExtensions.cpp
============================================================

SECTION starting at line 72 (left) / 72 (right):
----------------------------------------
  CONTEXT (line 72): void UMovieSceneSequenceExtensions::RemoveRootFolderFromSequence(UMovieSceneSequence* Sequence, UMovieSceneFolder* Folder) {
  CONTEXT (line 73): }
  CONTEXT (line 74): 
  REMOVED (line 75): FSequencerScriptingRange UMovieSceneSequenceExtensions::MakeRangeSeconds(UMovieSceneSequence* Sequence, float StartTime, float duration) {
  ADDED   (line 75): FSequencerScriptingRange UMovieSceneSequenceExtensions::MakeRangeSeconds(UMovieSceneSequence* Sequence, float StartTime, float Duration) {
  CONTEXT (line 76):     return FSequencerScriptingRange{};
  CONTEXT (line 77): }
  CONTEXT (line 78): 
  REMOVED (line 79): FSequencerScriptingRange UMovieSceneSequenceExtensions::MakeRange(UMovieSceneSequence* Sequence, int32 StartFrame, int32 duration) {
  ADDED   (line 79): FSequencerScriptingRange UMovieSceneSequenceExtensions::MakeRange(UMovieSceneSequence* Sequence, int32 StartFrame, int32 Duration) {
  CONTEXT (line 80):     return FSequencerScriptingRange{};
  CONTEXT (line 81): }
  CONTEXT (line 82): 