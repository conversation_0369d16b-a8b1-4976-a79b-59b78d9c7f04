DIFFERENCES IN: Engine\Private\PhysicsSettings.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\PhysicsSettings.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\PhysicsSettings.cpp
============================================================

SECTION starting at line 13 (left) / 13 (right):
----------------------------------------
  CONTEXT (line 13):     this->bSimulateAnimPhysicsAfterReset = false;
  CONTEXT (line 14):     this->MinPhysicsDeltaTime = 0.00f;
  CONTEXT (line 15):     this->MaxPhysicsDeltaTime = 0.03f;
  REMOVED (line 16):     this->bSubstepping = true;
  ADDED   (line 16):     this->bSubstepping = false;
  CONTEXT (line 17):     this->bSubsteppingAsync = false;
  CONTEXT (line 18):     this->bTickPhysicsAsync = false;
  CONTEXT (line 19):     this->AsyncFixedTimeStepSize = 0.03f;
  CONTEXT (line 20):     this->MaxSubstepDeltaTime = 0.02f;
  REMOVED (line 21):     this->MaxSubsteps = 3;
  ADDED   (line 21):     this->MaxSubsteps = 6;
  CONTEXT (line 22):     this->SyncSceneSmoothingFactor = 0.00f;
  CONTEXT (line 23):     this->InitialAverageFrameRate = 0.02f;
  CONTEXT (line 24):     this->PhysXTreeRebuildRate = 10;
  REMOVED (line 25):     this->PhysicalSurfaces.AddDefaulted(13);
  CONTEXT (line 26):     this->MinDeltaVelocityForHitEvents = 0.00f;
  CONTEXT (line 27): }
  CONTEXT (line 28): 