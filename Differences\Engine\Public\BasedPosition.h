DIFFERENCES IN: Engine\Public\BasedPosition.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\BasedPosition.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\BasedPosition.h
============================================================

SECTION starting at line 14 (left) / 14 (right):
----------------------------------------
  CONTEXT (line 14):     AActor* base;
  CONTEXT (line 15): 
  CONTEXT (line 16):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 17):     FVector Position;
  ADDED   (line 17):     FVector position;
  CONTEXT (line 18): 
  CONTEXT (line 19):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 20):     FVector CachedBaseLocation;