--- Left: WorldSettings.h
+++ Right: WorldSettings.h
@@ -36,9 +36,6 @@
     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     TEnumAsByte<EVisibilityAggressiveness> VisibilityAggressiveness;

     

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float VisibilityMaxDrawDistanceScale;

-    

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bPrecomputeVisibility: 1;

     
