DIFFERENCES IN: FieldSystemEngine\Public\WaveScalar.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\FieldSystemEngine\Public\WaveScalar.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\FieldSystemEngine\Public\WaveScalar.h
============================================================

SECTION starting at line 16 (left) / 16 (right):
----------------------------------------
  CONTEXT (line 16):     float Magnitude;
  CONTEXT (line 17): 
  CONTEXT (line 18):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 19):     FVector Position;
  ADDED   (line 19):     FVector position;
  CONTEXT (line 20): 
  CONTEXT (line 21):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 22):     float WaveLength;
  ADDED   (line 22):     float Wavelength;
  CONTEXT (line 23): 
  CONTEXT (line 24):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 25):     float Period;

SECTION starting at line 33 (left) / 33 (right):
----------------------------------------
  CONTEXT (line 33):     UWaveScalar(const FObjectInitializer& ObjectInitializer);
  CONTEXT (line 34): 
  CONTEXT (line 35):     UFUNCTION(BlueprintCallable, BlueprintPure)
  REMOVED (line 36):     UWaveScalar* SetWaveScalar(float NewMagnitude, FVector NewPosition, float NewWaveLength, float NewPeriod, float Time, TEnumAsByte<EWaveFunctionType> NewFunction, TEnumAsByte<EFieldFalloffType> NewFalloff);
  ADDED   (line 36):     UWaveScalar* SetWaveScalar(float NewMagnitude, FVector NewPosition, float NewWavelength, float NewPeriod, float Time, TEnumAsByte<EWaveFunctionType> NewFunction, TEnumAsByte<EFieldFalloffType> NewFalloff);
  CONTEXT (line 37): 
  CONTEXT (line 38): };
  CONTEXT (line 39): 