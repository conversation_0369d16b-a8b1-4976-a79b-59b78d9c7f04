--- Left: SplineComponent.cpp
+++ Right: SplineComponent.cpp
@@ -3,7 +3,7 @@
 

 USplineComponent::USplineComponent(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {

     this->ReparamStepsPerSegment = 10;

-    this->duration = 1.00f;

+    this->Duration = 1.00f;

     this->bStationaryEndpoints = false;

     this->bSplineHasBeenEdited = false;

     this->bModifiedByConstructionScript = false;

@@ -410,16 +410,16 @@
 void USplineComponent::ClearSplinePoints(bool bUpdateSpline) {

 }

 

-void USplineComponent::AddSplineWorldPoint(const FVector& Position) {

-}

-

-void USplineComponent::AddSplinePointAtIndex(const FVector& Position, int32 Index, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline) {

-}

-

-void USplineComponent::AddSplinePoint(const FVector& Position, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline) {

-}

-

-void USplineComponent::AddSplineLocalPoint(const FVector& Position) {

+void USplineComponent::AddSplineWorldPoint(const FVector& position) {

+}

+

+void USplineComponent::AddSplinePointAtIndex(const FVector& position, int32 Index, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline) {

+}

+

+void USplineComponent::AddSplinePoint(const FVector& position, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline) {

+}

+

+void USplineComponent::AddSplineLocalPoint(const FVector& position) {

 }

 

 void USplineComponent::AddPoints(const TArray<FSplinePoint>& Points, bool bUpdateSpline) {
