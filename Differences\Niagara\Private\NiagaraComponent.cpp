DIFFERENCES IN: Niagara\Private\NiagaraComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Niagara\Private\NiagaraComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Niagara\Private\NiagaraComponent.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): UNiagaraComponent::UNiagaraComponent(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {
  CONTEXT (line 4):     this->bAutoActivate = true;
  ADDED   (line 5):     this->bReceivesDecals = false;
  CONTEXT (line 5):     this->Asset = NULL;
  CONTEXT (line 6):     this->TickBehavior = ENiagaraTickBehavior::UsePrereqs;
  CONTEXT (line 7):     this->RandomSeedOffset = 0;