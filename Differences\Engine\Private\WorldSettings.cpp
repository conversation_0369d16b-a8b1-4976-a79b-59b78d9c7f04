DIFFERENCES IN: Engine\Private\WorldSettings.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\WorldSettings.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\WorldSettings.cpp
============================================================

SECTION starting at line 12 (left) / 12 (right):
----------------------------------------
  CONTEXT (line 12):     (*p_RemoteRole->ContainerPtrToValuePtr<TEnumAsByte<ENetRole>>(this)) = ROLE_SimulatedProxy;
  CONTEXT (line 13):     this->VisibilityCellSize = 200;
  CONTEXT (line 14):     this->VisibilityAggressiveness = VIS_LeastAggressive;
  REMOVED (line 15):     this->VisibilityMaxDrawDistanceScale = 1.00f;
  CONTEXT (line 16):     this->bPrecomputeVisibility = false;
  CONTEXT (line 17):     this->bPlaceCellsOnlyAlongCameraTracks = false;
  CONTEXT (line 18):     this->bEnableWorldBoundsChecks = true;