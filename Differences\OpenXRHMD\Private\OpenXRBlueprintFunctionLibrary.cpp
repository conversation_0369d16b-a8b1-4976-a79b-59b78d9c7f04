DIFFERENCES IN: OpenXRHMD\Private\OpenXRBlueprintFunctionLibrary.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\OpenXRHMD\Private\OpenXRBlueprintFunctionLibrary.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\OpenXRHMD\Private\OpenXRBlueprintFunctionLibrary.cpp
============================================================

SECTION starting at line 6 (left) / 6 (right):
----------------------------------------
  CONTEXT (line 6): void UOpenXRBlueprintFunctionLibrary::SetEnvironmentBlendMode(int32 NewBlendMode) {
  CONTEXT (line 7): }
  CONTEXT (line 8): 
  REMOVED (line 9): int32 UOpenXRBlueprintFunctionLibrary::GetPrimarySwapChainWidth() {
  REMOVED (line 10):     return 0;
  REMOVED (line 11): }
  CONTEXT (line 12): 
  REMOVED (line 13): int32 UOpenXRBlueprintFunctionLibrary::GetPrimarySwapChainHeight() {
  REMOVED (line 14):     return 0;
  REMOVED (line 15): }
  REMOVED (line 16): 
  REMOVED (line 17): float UOpenXRBlueprintFunctionLibrary::GetHMDDisplayRefreshRate() {
  REMOVED (line 18):     return 0.0f;
  REMOVED (line 19): }
  REMOVED (line 20): 
  REMOVED (line 21): 