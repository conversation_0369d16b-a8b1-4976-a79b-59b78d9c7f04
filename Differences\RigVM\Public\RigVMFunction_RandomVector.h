DIFFERENCES IN: RigVM\Public\RigVMFunction_RandomVector.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\RigVM\Public\RigVMFunction_RandomVector.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\RigVM\Public\RigVMFunction_RandomVector.h
============================================================

SECTION starting at line 18 (left) / 18 (right):
----------------------------------------
  CONTEXT (line 18):     float Maximum;
  CONTEXT (line 19): 
  CONTEXT (line 20):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 21):     float duration;
  ADDED   (line 21):     float Duration;
  CONTEXT (line 22): 
  CONTEXT (line 23):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 24):     FVector Result;