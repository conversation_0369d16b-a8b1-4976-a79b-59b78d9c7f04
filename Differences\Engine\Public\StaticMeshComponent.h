DIFFERENCES IN: Engine\Public\StaticMeshComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\StaticMeshComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\StaticMeshComponent.h
============================================================

SECTION starting at line 175 (left) / 175 (right):
----------------------------------------
  CONTEXT (line 175):     void OnRep_StaticMesh(UStaticMesh* OldStaticMesh);
  CONTEXT (line 176): 
  CONTEXT (line 177):     UFUNCTION(BlueprintCallable, BlueprintPure)
  REMOVED (line 178):     void GetLocalBounds(FVector& Min, FVector& Max) const;
  ADDED   (line 178):     void GetLocalBounds(FVector& min, FVector& max) const;
  CONTEXT (line 179): 
  CONTEXT (line 180):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 181):     bool GetInitialEvaluateWorldPositionOffset();