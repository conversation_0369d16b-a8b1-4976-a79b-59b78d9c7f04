DIFFERENCES IN: RigVM\Private\RigVMFunction_TimeLoop.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\RigVM\Private\RigVMFunction_TimeLoop.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\RigVM\Private\RigVMFunction_TimeLoop.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): FRigVMFunction_TimeLoop::FRigVMFunction_TimeLoop() {
  CONTEXT (line 4):     this->Speed = 0.00f;
  REMOVED (line 5):     this->duration = 0.00f;
  ADDED   (line 5):     this->Duration = 0.00f;
  CONTEXT (line 6):     this->Normalize = false;
  CONTEXT (line 7):     this->Absolute = 0.00f;
  CONTEXT (line 8):     this->Relative = 0.00f;