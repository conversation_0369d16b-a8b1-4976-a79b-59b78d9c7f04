DIFFERENCES IN: Engine\Public\InterpToMovementComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\InterpToMovementComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\InterpToMovementComponent.h
============================================================

SECTION starting at line 19 (left) / 19 (right):
----------------------------------------
  CONTEXT (line 19):     DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInterpToResetDelegate, const FHitResult&, ImpactResult, float, Time);
  CONTEXT (line 20): 
  CONTEXT (line 21):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 22):     float duration;
  ADDED   (line 22):     float Duration;
  CONTEXT (line 23): 
  CONTEXT (line 24):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 25):     uint8 bPauseOnImpact: 1;