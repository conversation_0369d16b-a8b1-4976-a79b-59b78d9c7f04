--- Left: GameViewportSubsystem.h
+++ Right: GameViewportSubsystem.h
@@ -15,7 +15,7 @@
     UGameViewportSubsystem();

 

     UFUNCTION(BlueprintCallable)

-    static FGameViewportWidgetSlot SetWidgetSlotPosition(FGameViewportWidgetSlot Slot, const UWidget* Widget, FVector2D Position, bool bRemoveDPIScale);

+    static FGameViewportWidgetSlot SetWidgetSlotPosition(FGameViewportWidgetSlot Slot, const UWidget* Widget, FVector2D position, bool bRemoveDPIScale);

     

     UFUNCTION(BlueprintCallable)

     static FGameViewportWidgetSlot SetWidgetSlotDesiredSize(FGameViewportWidgetSlot Slot, FVector2D Size);
