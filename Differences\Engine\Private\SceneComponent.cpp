DIFFERENCES IN: Engine\Private\SceneComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\SceneComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\SceneComponent.cpp
============================================================

SECTION starting at line 14 (left) / 14 (right):
----------------------------------------
  CONTEXT (line 14):     this->bShouldSnapScaleWhenAttached = false;
  CONTEXT (line 15):     this->bShouldUpdatePhysicsVolume = false;
  CONTEXT (line 16):     this->bHiddenInGame = false;
  REMOVED (line 17):     this->bIsCookedForMobile = true;
  REMOVED (line 18):     this->bIsCookedForDesktop = true;
  CONTEXT (line 19):     this->bBoundsChangeTriggersStreamingDataRebuild = false;
  CONTEXT (line 20):     this->bUseAttachParentBound = false;
  CONTEXT (line 21):     this->bComputeFastLocalBounds = false;