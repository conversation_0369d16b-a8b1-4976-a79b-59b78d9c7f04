--- Left: AudioMeter.h
+++ Right: AudioMeter.h
@@ -23,7 +23,7 @@
     FAudioMeterStyle WidgetStyle;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    TEnumAsByte<EOrientation> orientation;

+    TEnumAsByte<EOrientation> Orientation;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     FLinearColor BackgroundColor;
