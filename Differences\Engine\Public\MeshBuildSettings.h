DIFFERENCES IN: Engine\Public\MeshBuildSettings.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\MeshBuildSettings.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\MeshBuildSettings.h
============================================================

SECTION starting at line 40 (left) / 40 (right):
----------------------------------------
  CONTEXT (line 40):     uint8 bGenerateLightmapUVs: 1;
  CONTEXT (line 41): 
  CONTEXT (line 42):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 43):     uint8 bUseWithVolumetricLightmapsOnly: 1;
  REMOVED (line 44): 
  REMOVED (line 45):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 46):     uint8 bGenerateDistanceFieldAsIfTwoSided: 1;
  CONTEXT (line 47): 
  CONTEXT (line 48):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))