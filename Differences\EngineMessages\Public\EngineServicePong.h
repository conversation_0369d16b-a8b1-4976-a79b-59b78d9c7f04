DIFFERENCES IN: EngineMessages\Public\EngineServicePong.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\EngineMessages\Public\EngineServicePong.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\EngineMessages\Public\EngineServicePong.h
============================================================

SECTION starting at line 17 (left) / 17 (right):
----------------------------------------
  CONTEXT (line 17):     bool HasBegunPlay;
  CONTEXT (line 18): 
  CONTEXT (line 19):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 20):     FGuid InstanceID;
  ADDED   (line 20):     FGuid InstanceId;
  CONTEXT (line 21): 
  CONTEXT (line 22):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 23):     FString InstanceType;