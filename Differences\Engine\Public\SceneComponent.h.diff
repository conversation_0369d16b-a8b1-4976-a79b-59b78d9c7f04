--- Left: SceneComponent.h
+++ Right: SceneComponent.h
@@ -86,12 +86,6 @@
     UPROPERTY(BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))

     uint8 bHiddenInGame: 1;

     

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bIsCookedForMobile: 1;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bIsCookedForDesktop: 1;

-    

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bBoundsChangeTriggersStreamingDataRebuild: 1;

     
