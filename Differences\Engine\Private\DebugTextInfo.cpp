DIFFERENCES IN: Engine\Private\DebugTextInfo.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\DebugTextInfo.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\DebugTextInfo.cpp
============================================================

SECTION starting at line 3 (left) / 3 (right):
----------------------------------------
  CONTEXT (line 3): FDebugTextInfo::FDebugTextInfo() {
  CONTEXT (line 4):     this->SrcActor = NULL;
  CONTEXT (line 5):     this->TimeRemaining = 0.00f;
  REMOVED (line 6):     this->duration = 0.00f;
  ADDED   (line 6):     this->Duration = 0.00f;
  CONTEXT (line 7):     this->bAbsoluteLocation = false;
  CONTEXT (line 8):     this->bKeepAttachedToActor = false;
  CONTEXT (line 9):     this->bDrawShadow = false;