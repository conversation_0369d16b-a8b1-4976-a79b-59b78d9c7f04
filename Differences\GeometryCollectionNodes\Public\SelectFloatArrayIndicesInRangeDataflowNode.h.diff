--- Left: SelectFloatArrayIndicesInRangeDataflowNode.h
+++ Right: SelectFloatArrayIndicesInRangeDataflowNode.h
@@ -12,10 +12,10 @@
     TArray<float> Values;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float Min;

+    float min;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float Max;

+    float max;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     ERangeSettingEnum RangeSetting;
