--- Left: DecalComponent.h
+++ Right: DecalComponent.h
@@ -53,10 +53,10 @@
     void SetFadeScreenSize(float NewFadeScreenSize);

     

     UFUNCTION(BlueprintCallable)

-    void SetFadeOut(float StartDelay, float duration, bool DestroyOwnerAfterFade);

+    void SetFadeOut(float StartDelay, float Duration, bool DestroyOwnerAfterFade);

     

     UFUNCTION(BlueprintCallable)

-    void SetFadeIn(float StartDelay, float duration);

+    void SetFadeIn(float StartDelay, float Duration);

     

     UFUNCTION(BlueprintCallable)

     void SetDecalMaterial(UMaterialInterface* NewDecalMaterial);
