DIFFERENCES IN: Engine\Public\PlayerController.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\PlayerController.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\PlayerController.h
============================================================

SECTION starting at line 333 (left) / 333 (right):
----------------------------------------
  CONTEXT (line 333):     void ServerUpdateCamera(FVector_NetQuantize CamLoc, int32 CamPitchAndYaw);
  CONTEXT (line 334): 
  CONTEXT (line 335):     UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)
  REMOVED (line 336):     void ServerUnmutePlayer(FUniqueNetIdRepl PlayerID);
  REMOVED (line 337): 
  REMOVED (line 338):     UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)
  REMOVED (line 339):     void ServerUnblockPlayer(FUniqueNetIdRepl PlayerID);
  ADDED   (line 336):     void ServerUnmutePlayer(FUniqueNetIdRepl PlayerId);
  ADDED   (line 337): 
  ADDED   (line 338):     UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)
  ADDED   (line 339):     void ServerUnblockPlayer(FUniqueNetIdRepl PlayerId);
  CONTEXT (line 340): 
  CONTEXT (line 341):     UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)
  CONTEXT (line 342):     void ServerToggleAILogging();

SECTION starting at line 366 (left) / 366 (right):
----------------------------------------
  CONTEXT (line 366):     void ServerNotifyLoadedWorld(FName WorldPackageName);
  CONTEXT (line 367): 
  CONTEXT (line 368):     UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)
  REMOVED (line 369):     void ServerMutePlayer(FUniqueNetIdRepl PlayerID);
  ADDED   (line 369):     void ServerMutePlayer(FUniqueNetIdRepl PlayerId);
  CONTEXT (line 370): 
  CONTEXT (line 371):     UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)
  CONTEXT (line 372):     void ServerExecRPC(const FString& Msg);

SECTION starting at line 387 (left) / 387 (right):
----------------------------------------
  CONTEXT (line 387):     void ServerCamera(FName NewMode);
  CONTEXT (line 388): 
  CONTEXT (line 389):     UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)
  REMOVED (line 390):     void ServerBlockPlayer(FUniqueNetIdRepl PlayerID);
  ADDED   (line 390):     void ServerBlockPlayer(FUniqueNetIdRepl PlayerId);
  CONTEXT (line 391): 
  CONTEXT (line 392):     UFUNCTION(BlueprintCallable, Reliable, Server, WithValidation)
  CONTEXT (line 393):     void ServerAcknowledgePossession(APawn* P);

SECTION starting at line 411 (left) / 411 (right):
----------------------------------------
  CONTEXT (line 411):     void PlayHapticEffect(UHapticFeedbackEffect_Base* HapticEffect, EControllerHand Hand, float Scale, bool bLoop);
  CONTEXT (line 412): 
  CONTEXT (line 413):     UFUNCTION(BlueprintCallable, meta=(Latent, LatentInfo="LatentInfo"))
  REMOVED (line 414):     void PlayDynamicForceFeedback(float Intensity, float duration, bool bAffectsLeftLarge, bool bAffectsLeftSmall, bool bAffectsRightLarge, bool bAffectsRightSmall, TEnumAsByte<EDynamicForceFeedbackAction::Type> Action, FLatentActionInfo LatentInfo);
  ADDED   (line 414):     void PlayDynamicForceFeedback(float Intensity, float Duration, bool bAffectsLeftLarge, bool bAffectsLeftSmall, bool bAffectsRightLarge, bool bAffectsRightSmall, TEnumAsByte<EDynamicForceFeedbackAction::Type> Action, FLatentActionInfo LatentInfo);
  CONTEXT (line 415): 
  CONTEXT (line 416):     UFUNCTION(BlueprintCallable, Exec)
  CONTEXT (line 417):     void Pause();

SECTION starting at line 540 (left) / 540 (right):
----------------------------------------
  CONTEXT (line 540):     void ClientUnmutePlayers(const TArray<FUniqueNetIdRepl>& PlayerIds);
  CONTEXT (line 541): 
  CONTEXT (line 542):     UFUNCTION(BlueprintCallable, Client, Reliable)
  REMOVED (line 543):     void ClientUnmutePlayer(FUniqueNetIdRepl PlayerID);
  ADDED   (line 543):     void ClientUnmutePlayer(FUniqueNetIdRepl PlayerId);
  CONTEXT (line 544): 
  CONTEXT (line 545):     UFUNCTION(BlueprintCallable, Client, Reliable)
  CONTEXT (line 546):     void ClientTravelInternal(const FString& URL, TEnumAsByte<ETravelType> TravelType, bool bSeamless, FGuid MapPackageGuid);

SECTION starting at line 644 (left) / 644 (right):
----------------------------------------
  CONTEXT (line 644): 
  CONTEXT (line 645): public:
  CONTEXT (line 646):     UFUNCTION(BlueprintCallable, Client, Reliable)
  REMOVED (line 647):     void ClientMutePlayer(FUniqueNetIdRepl PlayerID);
  ADDED   (line 647):     void ClientMutePlayer(FUniqueNetIdRepl PlayerId);
  CONTEXT (line 648): 
  CONTEXT (line 649):     UFUNCTION(BlueprintCallable, Client, Reliable)
  CONTEXT (line 650):     void ClientMessage(const FString& S, FName Type, float MsgLifeTime);

SECTION starting at line 691 (left) / 691 (right):
----------------------------------------
  CONTEXT (line 691):     void ClientCancelPendingMapChange();
  CONTEXT (line 692): 
  CONTEXT (line 693):     UFUNCTION(BlueprintCallable, Client, Reliable)
  REMOVED (line 694):     void ClientAddTextureStreamingLoc(FVector InLoc, float duration, bool bOverrideLocation);
  ADDED   (line 694):     void ClientAddTextureStreamingLoc(FVector InLoc, float Duration, bool bOverrideLocation);
  CONTEXT (line 695): 
  CONTEXT (line 696):     UFUNCTION(BlueprintCallable, Client, Reliable)
  CONTEXT (line 697):     void ClientAckUpdateLevelVisibility(FName PackageName, FNetLevelVisibilityTransactionId TransactionId, bool bClientAckCanMakeVisible);