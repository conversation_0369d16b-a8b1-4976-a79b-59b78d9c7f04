--- Left: AudioMaterialMeter.h
+++ Right: AudioMaterialMeter.h
@@ -16,7 +16,7 @@
     FAudioMaterialMeterStyle WidgetStyle;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    TEnumAsByte<EOrientation> orientation;

+    TEnumAsByte<EOrientation> Orientation;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     FGetMeterChannelInfo MeterChannelInfoDelegate;
