DIFFERENCES IN: Engine\Public\LightComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\LightComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\LightComponent.h
============================================================

SECTION starting at line 159 (left) / 159 (right):
----------------------------------------
  CONTEXT (line 159):     void SetShadowBias(float NewValue);
  CONTEXT (line 160): 
  CONTEXT (line 161):     UFUNCTION(BlueprintCallable)
  REMOVED (line 162):     void SetLightingChannels(bool bChannel0, bool bChannel1);
  ADDED   (line 162):     void SetLightingChannels(bool bChannel0, bool bChannel1, bool bChannel2);
  CONTEXT (line 163): 
  CONTEXT (line 164):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 165):     void SetLightFunctionScale(FVector NewLightFunctionScale);