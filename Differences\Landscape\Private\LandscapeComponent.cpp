DIFFERENCES IN: Landscape\Private\LandscapeComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Landscape\Private\LandscapeComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Landscape\Private\LandscapeComponent.cpp
============================================================

SECTION starting at line 4 (left) / 4 (right):
----------------------------------------
  CONTEXT (line 4):     this->bBoundsChangeTriggersStreamingDataRebuild = true;
  CONTEXT (line 5):     this->Mobility = EComponentMobility::Static;
  CONTEXT (line 6):     this->bAllowCullDistanceVolume = false;
  REMOVED (line 7):     this->bReceivesDecals = true;
  CONTEXT (line 8):     this->bUseAsOccluder = true;
  CONTEXT (line 9):     this->AlwaysLoadOnServer = false;
  CONTEXT (line 10):     this->SectionBaseX = 0;