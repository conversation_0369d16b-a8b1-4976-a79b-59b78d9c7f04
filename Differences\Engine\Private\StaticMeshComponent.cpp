DIFFERENCES IN: Engine\Private\StaticMeshComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\StaticMeshComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\StaticMeshComponent.cpp
============================================================

SECTION starting at line 74 (left) / 74 (right):
----------------------------------------
  CONTEXT (line 74): void UStaticMeshComponent::OnRep_StaticMesh(UStaticMesh* OldStaticMesh) {
  CONTEXT (line 75): }
  CONTEXT (line 76): 
  REMOVED (line 77): void UStaticMeshComponent::GetLocalBounds(FVector& Min, FVector& Max) const {
  ADDED   (line 77): void UStaticMeshComponent::GetLocalBounds(FVector& min, FVector& max) const {
  CONTEXT (line 78): }
  CONTEXT (line 79): 
  CONTEXT (line 80): bool UStaticMeshComponent::GetInitialEvaluateWorldPositionOffset() {