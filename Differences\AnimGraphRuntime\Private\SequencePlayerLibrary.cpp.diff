--- Left: SequencePlayerLibrary.cpp
+++ Right: SequencePlayerLibrary.cpp
@@ -54,7 +54,7 @@
     return FSequencePlayerReference{};

 }

 

-float USequencePlayerLibrary::ComputePlayRateFromDuration(const FSequencePlayerReference& SequencePlayer, float duration) {

+float USequencePlayerLibrary::ComputePlayRateFromDuration(const FSequencePlayerReference& SequencePlayer, float Duration) {

     return 0.0f;

 }

 
