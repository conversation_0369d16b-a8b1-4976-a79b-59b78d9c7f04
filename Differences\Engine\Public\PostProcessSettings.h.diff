--- Left: PostProcessSettings.h
+++ Right: PostProcessSettings.h
@@ -476,24 +476,6 @@
     uint8 bOverride_IndirectLightingIntensity: 1;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bOverride_IndirectLightingColorFar: 1;

-    

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bOverride_IndirectLightingIntensityFar: 1;

-    

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bOverride_IndirectLightingFarBegin: 1;

-    

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bOverride_IndirectLightingFarEnd: 1;

-    

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bOverride_IndirectLightingBrightnessThreshold: 1;

-    

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bOverride_IndirectLightingBrightnessSmoothness: 1;

-    

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bOverride_ColorGradingIntensity: 1;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

@@ -942,24 +924,6 @@
     

     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))

     float IndirectLightingIntensity;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))

-    FLinearColor IndirectLightingColorFar;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))

-    float IndirectLightingIntensityFar;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))

-    float IndirectLightingFarBegin;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))

-    float IndirectLightingFarEnd;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))

-    float IndirectLightingBrightnessThreshold;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))

-    float IndirectLightingBrightnessSmoothness;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))

     ELumenRayLightingModeOverride LumenRayLightingMode;
