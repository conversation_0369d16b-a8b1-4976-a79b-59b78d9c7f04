DIFFERENCES IN: Engine\Private\AudioSettings.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\AudioSettings.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\AudioSettings.cpp
============================================================

SECTION starting at line 9 (left) / 9 (right):
----------------------------------------
  CONTEXT (line 9):     this->GlobalMinPitchScale = 0.40f;
  CONTEXT (line 10):     this->GlobalMaxPitchScale = 2.00f;
  CONTEXT (line 11):     this->QualityLevels.AddDefaulted(1);
  REMOVED (line 12):     this->bAllowPlayWhenSilent = false;
  ADDED   (line 12):     this->bAllowPlayWhenSilent = true;
  CONTEXT (line 13):     this->bDisableMasterEQ = false;
  CONTEXT (line 14):     this->bAllowCenterChannel3DPanning = false;
  CONTEXT (line 15):     this->NumStoppingSources = 8;