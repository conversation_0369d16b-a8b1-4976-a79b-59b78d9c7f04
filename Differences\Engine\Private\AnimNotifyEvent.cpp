DIFFERENCES IN: Engine\Private\AnimNotifyEvent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\AnimNotifyEvent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\AnimNotifyEvent.cpp
============================================================

SECTION starting at line 6 (left) / 6 (right):
----------------------------------------
  CONTEXT (line 6):     this->TriggerWeightThreshold = 0.00f;
  CONTEXT (line 7):     this->Notify = NULL;
  CONTEXT (line 8):     this->NotifyStateClass = NULL;
  REMOVED (line 9):     this->duration = 0.00f;
  ADDED   (line 9):     this->Duration = 0.00f;
  CONTEXT (line 10):     this->bConvertedFromBranchingPoint = false;
  CONTEXT (line 11):     this->MontageTickType = EMontageNotifyTickType::Queued;
  CONTEXT (line 12):     this->NotifyTriggerChance = 0.00f;