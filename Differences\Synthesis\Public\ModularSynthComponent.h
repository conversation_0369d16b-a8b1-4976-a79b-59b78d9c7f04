DIFFERENCES IN: Synthesis\Public\ModularSynthComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Synthesis\Public\ModularSynthComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Synthesis\Public\ModularSynthComponent.h
============================================================

SECTION starting at line 194 (left) / 194 (right):
----------------------------------------
  CONTEXT (line 194):     void SetAttackTime(float AttackTimeMsec);
  CONTEXT (line 195): 
  CONTEXT (line 196):     UFUNCTION(BlueprintCallable)
  REMOVED (line 197):     void NoteOn(const float Note, const int32 Velocity, const float duration);
  ADDED   (line 197):     void NoteOn(const float Note, const int32 Velocity, const float Duration);
  CONTEXT (line 198): 
  CONTEXT (line 199):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 200):     void NoteOff(const float Note, const bool bAllNotesOff, const bool bKillAllNotes);