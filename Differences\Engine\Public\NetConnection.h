DIFFERENCES IN: Engine\Public\NetConnection.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\NetConnection.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\NetConnection.h
============================================================

SECTION starting at line 48 (left) / 48 (right):
----------------------------------------
  CONTEXT (line 48): 
  CONTEXT (line 49): public:
  CONTEXT (line 50):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 51):     FUniqueNetIdRepl PlayerID;
  ADDED   (line 51):     FUniqueNetIdRepl PlayerId;
  CONTEXT (line 52): 
  CONTEXT (line 53):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 54):     double LastReceiveTime;