--- Left: Material.h
+++ Right: Material.h
@@ -124,9 +124,6 @@
     UPROPERTY(AssetRegistrySearchable, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     TEnumAsByte<ETranslucencyLightingMode> TranslucencyLightingMode;

     

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bTranslucencyBackground: 1;

-    

     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bEnableMobileSeparateTranslucency: 1;

     

@@ -259,9 +256,6 @@
     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bFullyRough: 1;

     

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bFullyRoughOnMobile: 1;

-    

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bUseFullPrecision: 1;

     

@@ -293,9 +287,6 @@
     uint8 bNormalCurvatureToRoughness: 1;

     

     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bUseCheckerAsEdgeMaterialId: 1;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 AllowTranslucentCustomDepthWrites: 1;

     

     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

@@ -306,9 +297,6 @@
     

     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     TEnumAsByte<EMaterialShadingRate> ShadingRate;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    TEnumAsByte<EMaterialShadingRate> ShadingRateOnMobile;

     

     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bAllowVariableRateShading: 1;

@@ -345,12 +333,6 @@
     

     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bIsSky: 1;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bXRSoftOcclusions: 1;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float XRSoftOcclusionsDepthBias;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bComputeFogPerPixel: 1;
