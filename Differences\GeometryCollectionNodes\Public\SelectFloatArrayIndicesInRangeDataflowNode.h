DIFFERENCES IN: GeometryCollectionNodes\Public\SelectFloatArrayIndicesInRangeDataflowNode.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCollectionNodes\Public\SelectFloatArrayIndicesInRangeDataflowNode.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCollectionNodes\Public\SelectFloatArrayIndicesInRangeDataflowNode.h
============================================================

SECTION starting at line 12 (left) / 12 (right):
----------------------------------------
  CONTEXT (line 12):     TArray<float> Values;
  CONTEXT (line 13): 
  CONTEXT (line 14):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 15):     float Min;
  ADDED   (line 15):     float min;
  CONTEXT (line 16): 
  CONTEXT (line 17):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 18):     float Max;
  ADDED   (line 18):     float max;
  CONTEXT (line 19): 
  CONTEXT (line 20):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 21):     ERangeSettingEnum RangeSetting;