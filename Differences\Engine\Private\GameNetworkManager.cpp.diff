--- Left: GameNetworkManager.cpp
+++ Right: GameNetworkManager.cpp
@@ -7,9 +7,9 @@
     this->SeverePingThreshold = 500;

     this->AdjustedNetSpeed = 0;

     this->LastNetSpeedUpdateTime = 0.00f;

-    this->TotalNetBandwidth = 64000;

+    this->TotalNetBandwidth = 32000;

     this->MinDynamicBandwidth = 4000;

-    this->MaxDynamicBandwidth = 15000;

+    this->MaxDynamicBandwidth = 7000;

     this->bIsStandbyCheckingEnabled = false;

     this->bHasStandbyCheatTriggered = false;

     this->StandbyRxCheatTime = 0.00f;

@@ -19,7 +19,7 @@
     this->PercentForBadPing = 0.00f;

     this->JoinInProgressStandbyWaitTime = 0.00f;

     this->MoveRepSize = 42.00f;

-    this->MAXPOSITIONERRORSQUARED = 10000.00f;

+    this->MAXPOSITIONERRORSQUARED = 3.00f;

     this->MAXNEARZEROVELOCITYSQUARED = 9.00f;

     this->CLIENTADJUSTUPDATECOST = 180.00f;

     this->MAXCLIENTUPDATEINTERVAL = 0.25f;

@@ -36,7 +36,7 @@
     this->ClientErrorUpdateRateLimit = 0.00f;

     this->ClientNetCamUpdateDeltaTime = 0.02f;

     this->ClientNetCamUpdatePositionLimit = 1000.00f;

-    this->ClientAuthorativePosition = true;

+    this->ClientAuthorativePosition = false;

     this->bMovementTimeDiscrepancyDetection = false;

     this->bMovementTimeDiscrepancyResolution = false;

     this->MovementTimeDiscrepancyMaxTimeMargin = 0.25f;
