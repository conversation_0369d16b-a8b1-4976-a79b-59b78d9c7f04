DIFFERENCES IN: Engine\Private\KismetMathLibrary.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\KismetMathLibrary.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\KismetMathLibrary.cpp
============================================================

SECTION starting at line 1213 (left) / 1213 (right):
----------------------------------------
  CONTEXT (line 1213): void UKismetMathLibrary::MinAreaRectangle(UObject* WorldContextObject, const TArray<FVector>& InPoints, const FVector& SampleSurfaceNormal, FVector& OutRectCenter, FRotator& OutRectRotation, float& OutRectLengthX, float& OutRectLengthY, bool bDebugDraw) {
  CONTEXT (line 1214): }
  CONTEXT (line 1215): 
  REMOVED (line 1216): int32 UKismetMathLibrary::Min(int32 A, int32 B) {
  ADDED   (line 1216): int32 UKismetMathLibrary::min(int32 A, int32 B) {
  CONTEXT (line 1217):     return 0;
  CONTEXT (line 1218): }
  CONTEXT (line 1219): 

SECTION starting at line 1233 (left) / 1233 (right):
----------------------------------------
  CONTEXT (line 1233):     return 0;
  CONTEXT (line 1234): }
  CONTEXT (line 1235): 
  REMOVED (line 1236): int32 UKismetMathLibrary::Max(int32 A, int32 B) {
  ADDED   (line 1236): int32 UKismetMathLibrary::max(int32 A, int32 B) {
  CONTEXT (line 1237):     return 0;
  CONTEXT (line 1238): }
  CONTEXT (line 1239): 