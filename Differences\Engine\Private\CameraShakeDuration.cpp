DIFFERENCES IN: Engine\Private\CameraShakeDuration.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\CameraShakeDuration.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\CameraShakeDuration.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "CameraShakeDuration.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): FCameraShakeDuration::FCameraShakeDuration() {
  REMOVED (line 4):     this->duration = 0.00f;
  ADDED   (line 4):     this->Duration = 0.00f;
  CONTEXT (line 5):     this->Type = ECameraShakeDurationType::Fixed;
  CONTEXT (line 6): }
  CONTEXT (line 7): 