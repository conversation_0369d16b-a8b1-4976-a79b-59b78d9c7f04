DIFFERENCES IN: UMG\Public\ListView.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\UMG\Public\ListView.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\UMG\Public\ListView.h
============================================================

SECTION starting at line 32 (left) / 32 (right):
----------------------------------------
  CONTEXT (line 32):     FScrollBarStyle ScrollBarStyle;
  CONTEXT (line 33): 
  CONTEXT (line 34):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 35):     TEnumAsByte<EOrientation> orientation;
  ADDED   (line 35):     TEnumAsByte<EOrientation> Orientation;
  CONTEXT (line 36): 
  CONTEXT (line 37):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 38):     TEnumAsByte<ESelectionMode::Type> SelectionMode;