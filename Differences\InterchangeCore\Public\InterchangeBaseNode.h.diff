--- Left: InterchangeBaseNode.h
+++ Right: InterchangeBaseNode.h
@@ -41,7 +41,7 @@
     bool GetVector2Attribute(const FString& NodeAttributeKey, FVector2f& OutValue) const;

     

     UFUNCTION(BlueprintCallable, BlueprintPure)

-    FString GetUniqueId() const;

+    FString GetUniqueID() const;

     

     UFUNCTION(BlueprintCallable, BlueprintPure)

     void GetTargetNodeUids(TArray<FString>& OutTargetAssets) const;
