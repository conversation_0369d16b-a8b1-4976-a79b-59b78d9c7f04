DIFFERENCES IN: Engine\Private\ViewportStatsSubsystem.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\ViewportStatsSubsystem.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\ViewportStatsSubsystem.cpp
============================================================

SECTION starting at line 6 (left) / 6 (right):
----------------------------------------
  CONTEXT (line 6): void UViewportStatsSubsystem::RemoveDisplayDelegate(const int32 IndexToRemove) {
  CONTEXT (line 7): }
  CONTEXT (line 8): 
  REMOVED (line 9): void UViewportStatsSubsystem::AddTimedDisplay(FText Text, FLinearColor Color, float duration, const FVector2D& DisplayOffset) {
  ADDED   (line 9): void UViewportStatsSubsystem::AddTimedDisplay(FText Text, FLinearColor Color, float Duration, const FVector2D& DisplayOffset) {
  CONTEXT (line 10): }
  CONTEXT (line 11): 
  CONTEXT (line 12): int32 UViewportStatsSubsystem::AddDisplayDelegate(const FViewportDisplayCallback& Delegate) {