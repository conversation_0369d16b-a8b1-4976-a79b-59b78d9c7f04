--- Left: OpenXRBlueprintFunctionLibrary.cpp
+++ Right: OpenXRBlueprintFunctionLibrary.cpp
@@ -6,16 +6,4 @@
 void UOpenXRBlueprintFunctionLibrary::SetEnvironmentBlendMode(int32 NewBlendMode) {

 }

 

-int32 UOpenXRBlueprintFunctionLibrary::GetPrimarySwapChainWidth() {

-    return 0;

-}

 

-int32 UOpenXRBlueprintFunctionLibrary::GetPrimarySwapChainHeight() {

-    return 0;

-}

-

-float UOpenXRBlueprintFunctionLibrary::GetHMDDisplayRefreshRate() {

-    return 0.0f;

-}

-

-
