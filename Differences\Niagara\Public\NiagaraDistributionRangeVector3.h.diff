--- Left: NiagaraDistributionRangeVector3.h
+++ Right: NiagaraDistributionRangeVector3.h
@@ -9,10 +9,10 @@
     GENERATED_BODY()

 public:

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    FVector3f Min;

+    FVector3f min;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    FVector3f Max;

+    FVector3f max;

     

     NIAGARA_API FNiagaraDistributionRangeVector3();

 };
