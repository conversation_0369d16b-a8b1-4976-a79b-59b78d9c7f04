DIFFERENCES IN: Engine\Public\Engine.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\Engine.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\Engine.h
============================================================

SECTION starting at line 544 (left) / 544 (right):
----------------------------------------
  CONTEXT (line 544):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 545):     UTexture2DArray* GlintTexture2;
  CONTEXT (line 546): 
  REMOVED (line 547):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 548):     UTexture2DArray* EnvironmentMaskShapesTexture;
  REMOVED (line 549): 
  CONTEXT (line 550):     UPROPERTY(BlueprintReadWrite, EditAnywhere, GlobalConfig, meta=(AllowPrivateAccess=true))
  CONTEXT (line 551):     FSoftObjectPath GlintTextureName;
  CONTEXT (line 552): 