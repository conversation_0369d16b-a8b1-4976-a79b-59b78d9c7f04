DIFFERENCES IN: Engine\Public\AnimInstance.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\AnimInstance.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\AnimInstance.h
============================================================

SECTION starting at line 164 (left) / 164 (right):
----------------------------------------
  CONTEXT (line 164):     bool RequestTransitionEvent(const FName EventName, const double RequestTimeout, const ETransitionRequestQueueMode QueueMode, const ETransitionRequestOverwriteMode OverwriteMode);
  CONTEXT (line 165): 
  CONTEXT (line 166):     UFUNCTION(BlueprintCallable)
  REMOVED (line 167):     void RequestSlotGroupInertialization(FName InSlotGroupName, float duration, const UBlendProfile* BlendProfile);
  ADDED   (line 167):     void RequestSlotGroupInertialization(FName InSlotGroupName, float Duration, const UBlendProfile* BlendProfile);
  CONTEXT (line 168): 
  CONTEXT (line 169):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 170):     void RemovePoseSnapshot(FName SnapshotName);