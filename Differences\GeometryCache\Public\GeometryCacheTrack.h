DIFFERENCES IN: GeometryCache\Public\GeometryCacheTrack.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCache\Public\GeometryCacheTrack.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCache\Public\GeometryCacheTrack.h
============================================================

SECTION starting at line 9 (left) / 9 (right):
----------------------------------------
  CONTEXT (line 9): public:
  CONTEXT (line 10): protected:
  CONTEXT (line 11):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 12):     float duration;
  ADDED   (line 12):     float Duration;
  CONTEXT (line 13): 
  CONTEXT (line 14): public:
  CONTEXT (line 15):     UGeometryCacheTrack();