--- Left: KismetAnimationLibrary.h
+++ Right: KismetAnimationLibrary.h
@@ -46,7 +46,7 @@
     static float K2_CalculateVelocityFromSockets(float DeltaSeconds, USkeletalMeshComponent* Component, const FName SocketOrBoneName, const FName ReferenceSocketOrBone, TEnumAsByte<ERelativeTransformSpace> SocketSpace, FVector OffsetInBoneSpace, UPARAM(Ref) FPositionHistory& History, int32 NumberOfSamples, float VelocityMin, float VelocityMax, EEasingFuncType EasingType, const FRuntimeFloatCurve& CustomCurve);

     

     UFUNCTION(BlueprintCallable)

-    static float K2_CalculateVelocityFromPositionHistory(float DeltaSeconds, FVector Position, UPARAM(Ref) FPositionHistory& History, int32 NumberOfSamples, float VelocityMin, float VelocityMax);

+    static float K2_CalculateVelocityFromPositionHistory(float DeltaSeconds, FVector position, UPARAM(Ref) FPositionHistory& History, int32 NumberOfSamples, float VelocityMin, float VelocityMax);

     

     UFUNCTION(BlueprintCallable, BlueprintPure)

     static float CalculateDirection(const FVector& Velocity, const FRotator& BaseRotation);
