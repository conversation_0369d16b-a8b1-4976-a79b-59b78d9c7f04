--- Left: KismetSystemLibrary.h
+++ Right: KismetSystemLibrary.h
@@ -191,7 +191,7 @@
     static void SetBoolPropertyByName(UObject* Object, FName PropertyName, bool Value);

     

     UFUNCTION(BlueprintCallable, meta=(Latent, LatentInfo="LatentInfo", WorldContext="WorldContextObject"))

-    static void RetriggerableDelay(const UObject* WorldContextObject, float duration, FLatentActionInfo LatentInfo);

+    static void RetriggerableDelay(const UObject* WorldContextObject, float Duration, FLatentActionInfo LatentInfo);

     

     UFUNCTION(BlueprintCallable)

     static void ResetGamepadAssignmentToController(int32 ControllerId);

@@ -209,10 +209,10 @@
     static void PrintWarning(const FString& InString);

     

     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void PrintText(const UObject* WorldContextObject, const FText InText, bool bPrintToScreen, bool bPrintToLog, FLinearColor TextColor, float duration, const FName Key);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void PrintString(const UObject* WorldContextObject, const FString& InString, bool bPrintToScreen, bool bPrintToLog, FLinearColor TextColor, float duration, const FName Key);

+    static void PrintText(const UObject* WorldContextObject, const FText InText, bool bPrintToScreen, bool bPrintToLog, FLinearColor TextColor, float Duration, const FName Key);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void PrintString(const UObject* WorldContextObject, const FString& InString, bool bPrintToScreen, bool bPrintToLog, FLinearColor TextColor, float Duration, const FName Key);

     

     UFUNCTION(BlueprintCallable, BlueprintPure)

     static bool ParseParamValue(const FString& InString, const FString& InParam, FString& OutValue);

@@ -683,55 +683,55 @@
     static int32 EndTransaction();

     

     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugString(const UObject* WorldContextObject, const FVector TextLocation, const FString& Text, AActor* TestBaseActor, FLinearColor TextColor, float duration);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugSphere(const UObject* WorldContextObject, const FVector Center, float Radius, int32 Segments, FLinearColor LineColor, float duration, float Thickness);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugPoint(const UObject* WorldContextObject, const FVector Position, float Size, FLinearColor PointColor, float duration);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugPlane(const UObject* WorldContextObject, const FPlane& PlaneCoordinates, const FVector Location, float Size, FLinearColor PlaneColor, float duration);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugLine(const UObject* WorldContextObject, const FVector LineStart, const FVector LineEnd, FLinearColor LineColor, float duration, float Thickness);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugFrustum(const UObject* WorldContextObject, const FTransform& FrustumTransform, FLinearColor FrustumColor, float duration, float Thickness);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugFloatHistoryTransform(const UObject* WorldContextObject, const FDebugFloatHistory& FloatHistory, const FTransform& DrawTransform, FVector2D DrawSize, FLinearColor DrawColor, float duration);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugFloatHistoryLocation(const UObject* WorldContextObject, const FDebugFloatHistory& FloatHistory, FVector DrawLocation, FVector2D DrawSize, FLinearColor DrawColor, float duration);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugCylinder(const UObject* WorldContextObject, const FVector Start, const FVector End, float Radius, int32 Segments, FLinearColor LineColor, float duration, float Thickness);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugCoordinateSystem(const UObject* WorldContextObject, const FVector AxisLoc, const FRotator AxisRot, float Scale, float duration, float Thickness);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugConeInDegrees(const UObject* WorldContextObject, const FVector Origin, const FVector Direction, float Length, float AngleWidth, float AngleHeight, int32 NumSides, FLinearColor LineColor, float duration, float Thickness);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugCone(const UObject* WorldContextObject, const FVector Origin, const FVector Direction, float Length, float AngleWidth, float AngleHeight, int32 NumSides, FLinearColor LineColor, float duration, float Thickness);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugCircle(const UObject* WorldContextObject, FVector Center, float Radius, int32 NumSegments, FLinearColor LineColor, float duration, float Thickness, FVector YAxis, FVector ZAxis, bool bDrawAxis);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugCapsule(const UObject* WorldContextObject, const FVector Center, float HalfHeight, float Radius, const FRotator Rotation, FLinearColor LineColor, float duration, float Thickness);

-    

-    UFUNCTION(BlueprintCallable)

-    static void DrawDebugCamera(const ACameraActor* CameraActor, FLinearColor CameraColor, float duration);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugBox(const UObject* WorldContextObject, const FVector Center, FVector Extent, FLinearColor LineColor, const FRotator Rotation, float duration, float Thickness);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static void DrawDebugArrow(const UObject* WorldContextObject, const FVector LineStart, const FVector LineEnd, float ArrowSize, FLinearColor LineColor, float duration, float Thickness);

+    static void DrawDebugString(const UObject* WorldContextObject, const FVector TextLocation, const FString& Text, AActor* TestBaseActor, FLinearColor TextColor, float Duration);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void DrawDebugSphere(const UObject* WorldContextObject, const FVector Center, float Radius, int32 Segments, FLinearColor LineColor, float Duration, float Thickness);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void DrawDebugPoint(const UObject* WorldContextObject, const FVector position, float Size, FLinearColor PointColor, float Duration);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void DrawDebugPlane(const UObject* WorldContextObject, const FPlane& PlaneCoordinates, const FVector Location, float Size, FLinearColor PlaneColor, float Duration);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void DrawDebugLine(const UObject* WorldContextObject, const FVector LineStart, const FVector LineEnd, FLinearColor LineColor, float Duration, float Thickness);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void DrawDebugFrustum(const UObject* WorldContextObject, const FTransform& FrustumTransform, FLinearColor FrustumColor, float Duration, float Thickness);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void DrawDebugFloatHistoryTransform(const UObject* WorldContextObject, const FDebugFloatHistory& FloatHistory, const FTransform& DrawTransform, FVector2D DrawSize, FLinearColor DrawColor, float Duration);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void DrawDebugFloatHistoryLocation(const UObject* WorldContextObject, const FDebugFloatHistory& FloatHistory, FVector DrawLocation, FVector2D DrawSize, FLinearColor DrawColor, float Duration);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void DrawDebugCylinder(const UObject* WorldContextObject, const FVector Start, const FVector End, float Radius, int32 Segments, FLinearColor LineColor, float Duration, float Thickness);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void DrawDebugCoordinateSystem(const UObject* WorldContextObject, const FVector AxisLoc, const FRotator AxisRot, float Scale, float Duration, float Thickness);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void DrawDebugConeInDegrees(const UObject* WorldContextObject, const FVector Origin, const FVector Direction, float Length, float AngleWidth, float AngleHeight, int32 NumSides, FLinearColor LineColor, float Duration, float Thickness);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void DrawDebugCone(const UObject* WorldContextObject, const FVector Origin, const FVector Direction, float Length, float AngleWidth, float AngleHeight, int32 NumSides, FLinearColor LineColor, float Duration, float Thickness);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void DrawDebugCircle(const UObject* WorldContextObject, FVector Center, float Radius, int32 NumSegments, FLinearColor LineColor, float Duration, float Thickness, FVector YAxis, FVector ZAxis, bool bDrawAxis);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void DrawDebugCapsule(const UObject* WorldContextObject, const FVector Center, float HalfHeight, float Radius, const FRotator Rotation, FLinearColor LineColor, float Duration, float Thickness);

+    

+    UFUNCTION(BlueprintCallable)

+    static void DrawDebugCamera(const ACameraActor* CameraActor, FLinearColor CameraColor, float Duration);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void DrawDebugBox(const UObject* WorldContextObject, const FVector Center, FVector Extent, FLinearColor LineColor, const FRotator Rotation, float Duration, float Thickness);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static void DrawDebugArrow(const UObject* WorldContextObject, const FVector LineStart, const FVector LineEnd, float ArrowSize, FLinearColor LineColor, float Duration, float Thickness);

     

     UFUNCTION(BlueprintCallable, BlueprintPure)

     static bool DoesImplementInterface(const UObject* TestObject, TSubclassOf<UInterface> Interface);

@@ -743,7 +743,7 @@
     static void DelayUntilNextTick(const UObject* WorldContextObject, FLatentActionInfo LatentInfo);

     

     UFUNCTION(BlueprintCallable, meta=(Latent, LatentInfo="LatentInfo", WorldContext="WorldContextObject"))

-    static void Delay(const UObject* WorldContextObject, float duration, FLatentActionInfo LatentInfo);

+    static void Delay(const UObject* WorldContextObject, float Duration, FLatentActionInfo LatentInfo);

     

     UFUNCTION(BlueprintCallable)

     static void CreateCopyForUndoBuffer(UObject* ObjectToModify);

@@ -854,22 +854,22 @@
     static void BreakARFilter(FARFilter InARFilter, TArray<FName>& PackageNames, TArray<FName>& PackagePaths, TArray<FSoftObjectPath>& SoftObjectPaths, TArray<FTopLevelAssetPath>& ClassPaths, TSet<FTopLevelAssetPath>& RecursiveClassPathsExclusionSet, TArray<FName>& ClassNames, TSet<FName>& RecursiveClassesExclusionSet, bool& bRecursivePaths, bool& bRecursiveClasses, bool& bIncludeOnlyOnDiskAssets);

     

     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static bool BoxTraceSingleForObjects(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator orientation, const TArray<TEnumAsByte<EObjectTypeQuery>>& ObjectTypes, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static bool BoxTraceSingleByProfile(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator orientation, FName ProfileName, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static bool BoxTraceSingle(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator orientation, TEnumAsByte<ETraceTypeQuery> TraceChannel, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static bool BoxTraceMultiForObjects(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator orientation, const TArray<TEnumAsByte<EObjectTypeQuery>>& ObjectTypes, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static bool BoxTraceMultiByProfile(const UObject* WorldContextObject, const FVector Start, const FVector End, FVector HalfSize, const FRotator orientation, FName ProfileName, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);

-    

-    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

-    static bool BoxTraceMulti(const UObject* WorldContextObject, const FVector Start, const FVector End, FVector HalfSize, const FRotator orientation, TEnumAsByte<ETraceTypeQuery> TraceChannel, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);

+    static bool BoxTraceSingleForObjects(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator Orientation, const TArray<TEnumAsByte<EObjectTypeQuery>>& ObjectTypes, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static bool BoxTraceSingleByProfile(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator Orientation, FName ProfileName, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static bool BoxTraceSingle(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator Orientation, TEnumAsByte<ETraceTypeQuery> TraceChannel, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static bool BoxTraceMultiForObjects(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator Orientation, const TArray<TEnumAsByte<EObjectTypeQuery>>& ObjectTypes, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static bool BoxTraceMultiByProfile(const UObject* WorldContextObject, const FVector Start, const FVector End, FVector HalfSize, const FRotator Orientation, FName ProfileName, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);

+    

+    UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

+    static bool BoxTraceMulti(const UObject* WorldContextObject, const FVector Start, const FVector End, FVector HalfSize, const FRotator Orientation, TEnumAsByte<ETraceTypeQuery> TraceChannel, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);

     

     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))

     static bool BoxOverlapComponents(const UObject* WorldContextObject, const FVector BoxPos, FVector Extent, const TArray<TEnumAsByte<EObjectTypeQuery>>& ObjectTypes, UClass* ComponentClassFilter, const TArray<AActor*>& ActorsToIgnore, TArray<UPrimitiveComponent*>& OutComponents);
