--- Left: InterchangeAnimationTrackNode.h
+++ Right: InterchangeAnimationTrackNode.h
@@ -19,7 +19,7 @@
     bool SetCustomFrameCount(const int32& AttributeValue);

     

     UFUNCTION(BlueprintCallable)

-    bool SetCustomAnimationPayloadKey(const FString& InUniqueID, const EInterchangeAnimationPayLoadType& InType);

+    bool SetCustomAnimationPayloadKey(const FString& InUniqueId, const EInterchangeAnimationPayLoadType& InType);

     

     UFUNCTION(BlueprintCallable)

     bool SetCustomActorDependencyUid(const FString& DependencyUid);
