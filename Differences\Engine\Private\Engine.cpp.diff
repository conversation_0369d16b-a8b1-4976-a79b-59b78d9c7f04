--- Left: Engine.cpp
+++ Right: Engine.cpp
@@ -90,7 +90,7 @@
     this->StatColorMappings.AddDefaulted(3);

     this->DefaultPhysMaterial = NULL;

     this->DefaultDestructiblePhysMaterial = NULL;

-    this->ActiveClassRedirects.AddDefaulted(3);

+    this->ActiveGameNameRedirects.AddDefaulted(2);

     this->PreIntegratedSkinBRDFTexture = NULL;

     this->BlueNoiseScalarTexture = NULL;

     this->BlueNoiseVec2Texture = NULL;

@@ -103,7 +103,6 @@
     this->DiffuseEnergyTexture = NULL;

     this->GlintTexture = NULL;

     this->GlintTexture2 = NULL;

-    this->EnvironmentMaskShapesTexture = NULL;

     this->SimpleVolumeTexture = NULL;

     this->SimpleVolumeEnvTexture = NULL;

     this->MiniFontTexture = NULL;

@@ -111,11 +110,11 @@
     this->WeightMapArrayPlaceholderTexture = NULL;

     this->LightMapDensityTexture = NULL;

     this->GameViewport = NULL;

-    this->NearClipPlane = 2.00f;

+    this->NearClipPlane = 10.00f;

     this->bSubtitlesEnabled = true;

     this->bSubtitlesForcedOff = false;

-    this->MaximumLoopIterationCount = 5000000;

-    this->bCanBlueprintsTickByDefault = false;

+    this->MaximumLoopIterationCount = 1000000;

+    this->bCanBlueprintsTickByDefault = true;

     this->bOptimizeAnimBlueprintMemberVariableAccess = true;

     this->bAllowMultiThreadedAnimationUpdate = true;

     this->bEnableEditorPSysRealtimeLOD = false;
