DIFFERENCES IN: EnhancedInput\Private\EnhancedInputDeveloperSettings.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\EnhancedInput\Private\EnhancedInputDeveloperSettings.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\EnhancedInput\Private\EnhancedInputDeveloperSettings.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "EnhancedInputDeveloperSettings.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): UEnhancedInputDeveloperSettings::UEnhancedInputDeveloperSettings() {
  ADDED   (line 4):     this->DefaultMappingContexts.AddDefaulted(5);
  CONTEXT (line 4):     this->bSendTriggeredEventsWhenInputIsFlushed = true;
  CONTEXT (line 5):     this->bEnableUserSettings = false;
  CONTEXT (line 6):     this->bEnableDefaultMappingContexts = true;