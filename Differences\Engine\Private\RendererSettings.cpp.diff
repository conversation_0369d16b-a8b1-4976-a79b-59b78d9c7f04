--- Left: RendererSettings.cpp
+++ Right: RendererSettings.cpp
@@ -7,8 +7,6 @@
     this->MobileAntiAliasing = EMobileAntiAliasingMethod::None;

     this->MobileFloatPrecisionMode = EMobileFloatPrecisionMode::Half;

     this->bMobileAllowDitheredLODTransition = false;

-    this->bMobileAllowCustomOcclusionCulling = false;

-    this->bTestDeferredAllowCustomOcclusionCulling = false;

     this->bMobileVirtualTextures = false;

     this->bDiscardUnusedQualityLevels = false;

     this->ShaderCompressionFormat = EShaderCompressionFormat::None;

@@ -132,9 +130,6 @@
     this->bMobileMultiView = false;

     this->bMobileUseHWsRGBEncoding = false;

     this->bRoundRobinOcclusion = false;

-    this->bMobileSupportSpaceWarp = false;

-    this->bSupportsXRSoftOcclusions = false;

-    this->bVulkanUseEmulatedUBs = false;

     this->bMeshStreaming = false;

     this->bEnableHeterogeneousVolumes = false;

     this->bShouldHeterogeneousVolumesCastShadows = false;

@@ -162,8 +157,6 @@
     this->bMobileEnableStaticAndCSMShadowReceivers = false;

     this->bMobileEnableMovableLightCSMShaderCulling = false;

     this->MobileLocalLightSetting = LOCAL_LIGHTS_DISABLED;

-    this->bMobilePackLightGridLightDataToUBO = false;

-    this->bMobileUniformLocalLights = false;

     this->bMobileForwardEnableClusteredReflections = false;

     this->bMobileAllowDistanceFieldShadows = false;

     this->bMobileAllowMovableSpotlightShadows = false;
