DIFFERENCES IN: Engine\Private\AssetManagerSettings.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\AssetManagerSettings.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\AssetManagerSettings.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "AssetManagerSettings.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): UAssetManagerSettings::UAssetManagerSettings() {
  REMOVED (line 4):     this->PrimaryAssetTypesToScan.AddDefaulted(6);
  ADDED   (line 4):     this->PrimaryAssetTypesToScan.AddDefaulted(2);
  CONTEXT (line 5):     this->bOnlyCookProductionAssets = false;
  REMOVED (line 6):     this->bShouldManagerDetermineTypeAndName = true;
  ADDED   (line 6):     this->bShouldManagerDetermineTypeAndName = false;
  CONTEXT (line 7):     this->bShouldGuessTypeAndNameInEditor = true;
  CONTEXT (line 8):     this->bShouldAcquireMissingChunksOnLoad = false;
  CONTEXT (line 9):     this->bShouldWarnAboutInvalidAssets = true;