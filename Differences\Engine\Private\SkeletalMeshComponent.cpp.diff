--- Left: SkeletalMeshComponent.cpp
+++ Right: SkeletalMeshComponent.cpp
@@ -232,7 +232,7 @@
 void USkeletalMeshComponent::Play(bool bLooping) {

 }

 

-void USkeletalMeshComponent::OverrideAnimationData(UAnimationAsset* InAnimToPlay, bool bIsLooping, bool bIsPlaying, float Position, float PlayRate) {

+void USkeletalMeshComponent::OverrideAnimationData(UAnimationAsset* InAnimToPlay, bool bIsLooping, bool bIsPlaying, float position, float PlayRate) {

 }

 

 void USkeletalMeshComponent::LinkAnimGraphByTag(FName InTag, TSubclassOf<UAnimInstance> InClass) {
