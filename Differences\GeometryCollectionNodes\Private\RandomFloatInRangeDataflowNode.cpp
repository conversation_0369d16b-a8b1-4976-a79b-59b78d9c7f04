DIFFERENCES IN: GeometryCollectionNodes\Private\RandomFloatInRangeDataflowNode.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCollectionNodes\Private\RandomFloatInRangeDataflowNode.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCollectionNodes\Private\RandomFloatInRangeDataflowNode.cpp
============================================================

SECTION starting at line 3 (left) / 3 (right):
----------------------------------------
  CONTEXT (line 3): FRandomFloatInRangeDataflowNode::FRandomFloatInRangeDataflowNode() {
  CONTEXT (line 4):     this->bDeterministic = false;
  CONTEXT (line 5):     this->RandomSeed = 0.00f;
  REMOVED (line 6):     this->Min = 0.00f;
  REMOVED (line 7):     this->Max = 0.00f;
  ADDED   (line 6):     this->min = 0.00f;
  ADDED   (line 7):     this->max = 0.00f;
  CONTEXT (line 8):     this->ReturnValue = 0.00f;
  CONTEXT (line 9): }
  CONTEXT (line 10): 