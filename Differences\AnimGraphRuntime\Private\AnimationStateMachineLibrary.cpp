DIFFERENCES IN: AnimGraphRuntime\Private\AnimationStateMachineLibrary.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AnimGraphRuntime\Private\AnimationStateMachineLibrary.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AnimGraphRuntime\Private\AnimationStateMachineLibrary.cpp
============================================================

SECTION starting at line 3 (left) / 3 (right):
----------------------------------------
  CONTEXT (line 3): UAnimationStateMachineLibrary::UAnimationStateMachineLibrary() {
  CONTEXT (line 4): }
  CONTEXT (line 5): 
  REMOVED (line 6): void UAnimationStateMachineLibrary::SetState(const FAnimUpdateContext& UpdateContext, const FAnimationStateMachineReference& Node, FName TargetState, float duration, TEnumAsByte<ETransitionLogicType::Type> BlendType, UBlendProfile* BlendProfile, EAlphaBlendOption AlphaBlendOption, UCurveFloat* CustomBlendCurve) {
  ADDED   (line 6): void UAnimationStateMachineLibrary::SetState(const FAnimUpdateContext& UpdateContext, const FAnimationStateMachineReference& Node, FName TargetState, float Duration, TEnumAsByte<ETransitionLogicType::Type> BlendType, UBlendProfile* BlendProfile, EAlphaBlendOption AlphaBlendOption, UCurveFloat* CustomBlendCurve) {
  CONTEXT (line 7): }
  CONTEXT (line 8): 
  CONTEXT (line 9): bool UAnimationStateMachineLibrary::IsStateBlendingOut(const FAnimUpdateContext& UpdateContext, const FAnimationStateResultReference& Node) {