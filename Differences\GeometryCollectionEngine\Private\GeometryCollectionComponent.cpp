DIFFERENCES IN: GeometryCollectionEngine\Private\GeometryCollectionComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCollectionEngine\Private\GeometryCollectionComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCollectionEngine\Private\GeometryCollectionComponent.cpp
============================================================

SECTION starting at line 225 (left) / 225 (right):
----------------------------------------
  CONTEXT (line 225): void UGeometryCollectionComponent::ApplyLinearVelocity(int32 ItemIndex, const FVector& LinearVelocity) {
  CONTEXT (line 226): }
  CONTEXT (line 227): 
  REMOVED (line 228): void UGeometryCollectionComponent::ApplyKinematicField(float Radius, FVector Position) {
  ADDED   (line 228): void UGeometryCollectionComponent::ApplyKinematicField(float Radius, FVector position) {
  CONTEXT (line 229): }
  CONTEXT (line 230): 
  CONTEXT (line 231): void UGeometryCollectionComponent::ApplyInternalStrain(int32 ItemIndex, const FVector& Location, float Radius, int32 PropagationDepth, float PropagationFactor, float Strain) {