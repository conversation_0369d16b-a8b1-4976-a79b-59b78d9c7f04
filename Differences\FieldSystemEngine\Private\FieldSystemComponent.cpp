DIFFERENCES IN: FieldSystemEngine\Private\FieldSystemComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\FieldSystemEngine\Private\FieldSystemComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\FieldSystemEngine\Private\FieldSystemComponent.cpp
============================================================

SECTION starting at line 12 (left) / 12 (right):
----------------------------------------
  CONTEXT (line 12): void UFieldSystemComponent::RemovePersistentFields() {
  CONTEXT (line 13): }
  CONTEXT (line 14): 
  REMOVED (line 15): void UFieldSystemComponent::ApplyUniformVectorFalloffForce(bool Enabled, FVector Position, FVector Direction, float Radius, float Magnitude) {
  ADDED   (line 15): void UFieldSystemComponent::ApplyUniformVectorFalloffForce(bool Enabled, FVector position, FVector Direction, float Radius, float Magnitude) {
  CONTEXT (line 16): }
  CONTEXT (line 17): 
  REMOVED (line 18): void UFieldSystemComponent::ApplyStrainField(bool Enabled, FVector Position, float Radius, float Magnitude, int32 Iterations) {
  ADDED   (line 18): void UFieldSystemComponent::ApplyStrainField(bool Enabled, FVector position, float Radius, float Magnitude, int32 Iterations) {
  CONTEXT (line 19): }
  CONTEXT (line 20): 
  REMOVED (line 21): void UFieldSystemComponent::ApplyStayDynamicField(bool Enabled, FVector Position, float Radius) {
  ADDED   (line 21): void UFieldSystemComponent::ApplyStayDynamicField(bool Enabled, FVector position, float Radius) {
  CONTEXT (line 22): }
  CONTEXT (line 23): 
  REMOVED (line 24): void UFieldSystemComponent::ApplyRadialVectorFalloffForce(bool Enabled, FVector Position, float Radius, float Magnitude) {
  ADDED   (line 24): void UFieldSystemComponent::ApplyRadialVectorFalloffForce(bool Enabled, FVector position, float Radius, float Magnitude) {
  CONTEXT (line 25): }
  CONTEXT (line 26): 
  REMOVED (line 27): void UFieldSystemComponent::ApplyRadialForce(bool Enabled, FVector Position, float Magnitude) {
  ADDED   (line 27): void UFieldSystemComponent::ApplyRadialForce(bool Enabled, FVector position, float Magnitude) {
  CONTEXT (line 28): }
  CONTEXT (line 29): 
  CONTEXT (line 30): void UFieldSystemComponent::ApplyPhysicsField(bool Enabled, TEnumAsByte<EFieldPhysicsType> Target, UFieldSystemMetaData* MetaData, UFieldNodeBase* Field) {