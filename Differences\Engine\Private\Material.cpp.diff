--- Left: Material.cpp
+++ Right: Material.cpp
@@ -32,7 +32,6 @@
     this->bEnableDisplacementFade = false;

     this->TranslucencyPass = MTP_AfterDOF;

     this->TranslucencyLightingMode = TLM_VolumetricNonDirectional;

-    this->bTranslucencyBackground = false;

     this->bEnableMobileSeparateTranslucency = false;

     this->NumCustomizedUVs = 0;

     this->TranslucencyDirectionalLightingIntensity = 1.00f;

@@ -76,7 +75,6 @@
     this->bForceCompatibleWithLightFunctionAtlas = false;

     this->bAutomaticallySetUsageInEditor = true;

     this->bFullyRough = false;

-    this->bFullyRoughOnMobile = false;

     this->bUseFullPrecision = false;

     this->FloatPrecisionMode = MFPM_Default;

     this->bUseLightmapDirectionality = true;

@@ -87,12 +85,10 @@
     this->bForwardBlendsSkyLightCubemaps = false;

     this->bUsePlanarForwardReflections = false;

     this->bNormalCurvatureToRoughness = false;

-    this->bUseCheckerAsEdgeMaterialId = false;

     this->AllowTranslucentCustomDepthWrites = false;

     this->bAllowFrontLayerTranslucency = true;

     this->Wireframe = false;

     this->ShadingRate = MSR_1x1;

-    this->ShadingRateOnMobile = MSR_1x1;

     this->bAllowVariableRateShading = true;

     this->bCanMaskedBeAssumedOpaque = false;

     this->bIsMasked = false;

@@ -105,8 +101,6 @@
     this->bUseTranslucencyVertexFog = true;

     this->bApplyCloudFogging = false;

     this->bIsSky = false;

-    this->bXRSoftOcclusions = true;

-    this->XRSoftOcclusionsDepthBias = 0.00f;

     this->bComputeFogPerPixel = false;

     this->bOutputTranslucentVelocity = false;

     this->bAllowDevelopmentShaderCompile = true;
