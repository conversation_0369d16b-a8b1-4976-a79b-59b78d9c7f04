--- Left: PrimitiveComponent.h
+++ Right: PrimitiveComponent.h
@@ -167,9 +167,6 @@
     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bHasPerInstanceHitProxies: 1;

     

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bShouldBeExcludedFromLightmaps: 1;

-    

     UPROPERTY(BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))

     uint8 CastShadow: 1;

     

@@ -511,7 +508,7 @@
     void SetLinearDamping(float InDamping);

     

     UFUNCTION(BlueprintCallable)

-    void SetLightingChannels(bool bChannel0, bool bChannel1);

+    void SetLightingChannels(bool bChannel0, bool bChannel1, bool bChannel2);

     

     UFUNCTION(BlueprintCallable)

     void SetLightAttachmentsAsGroup(bool bInLightAttachmentsAsGroup);
