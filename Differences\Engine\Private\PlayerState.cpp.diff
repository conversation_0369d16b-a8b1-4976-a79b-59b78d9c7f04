--- Left: PlayerState.cpp
+++ Right: PlayerState.cpp
@@ -9,7 +9,7 @@
     const FProperty* p_RemoteRole = GetClass()->FindPropertyByName("RemoteRole");

     (*p_RemoteRole->ContainerPtrToValuePtr<TEnumAsByte<ENetRole>>(this)) = ROLE_SimulatedProxy;

     this->Score = 0.00f;

-    this->PlayerID = 0;

+    this->PlayerId = 0;

     this->CompressedPing = 0;

     this->bShouldUpdateReplicatedPing = true;

     this->bIsSpectator = false;

@@ -90,7 +90,7 @@
     Super::GetLifetimeReplicatedProps(OutLifetimeProps);

     

     DOREPLIFETIME(APlayerState, Score);

-    DOREPLIFETIME(APlayerState, PlayerID);

+    DOREPLIFETIME(APlayerState, PlayerId);

     DOREPLIFETIME(APlayerState, CompressedPing);

     DOREPLIFETIME(APlayerState, bIsSpectator);

     DOREPLIFETIME(APlayerState, bOnlySpectator);
