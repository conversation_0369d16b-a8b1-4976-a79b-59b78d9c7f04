DIFFERENCES IN: Engine\Private\HUD.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\HUD.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\HUD.cpp
============================================================

SECTION starting at line 91 (left) / 91 (right):
----------------------------------------
  CONTEXT (line 91): void AHUD::Deproject(float ScreenX, float ScreenY, FVector& WorldPosition, FVector& WorldDirection) const {
  CONTEXT (line 92): }
  CONTEXT (line 93): 
  REMOVED (line 94): void AHUD::AddHitBox(FVector2D Position, FVector2D Size, FName InName, bool bConsumesInput, int32 Priority) {
  ADDED   (line 94): void AHUD::AddHitBox(FVector2D position, FVector2D Size, FName InName, bool bConsumesInput, int32 Priority) {
  CONTEXT (line 95): }
  CONTEXT (line 96): 
  REMOVED (line 97): void AHUD::AddDebugText_Implementation(const FString& DebugText, AActor* SrcActor, float duration, FVector Offset, FVector DesiredOffset, FColor TextColor, bool bSkipOverwriteCheck, bool bAbsoluteLocation, bool bKeepAttachedToActor, UFont* InFont, float FontScale, bool bDrawShadow) {
  ADDED   (line 97): void AHUD::AddDebugText_Implementation(const FString& DebugText, AActor* SrcActor, float Duration, FVector Offset, FVector DesiredOffset, FColor TextColor, bool bSkipOverwriteCheck, bool bAbsoluteLocation, bool bKeepAttachedToActor, UFont* InFont, float FontScale, bool bDrawShadow) {
  CONTEXT (line 98): }
  CONTEXT (line 99): 
  CONTEXT (line 100): 