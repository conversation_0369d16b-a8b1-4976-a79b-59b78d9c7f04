DIFFERENCES IN: MovieScene\Private\MovieSceneSequencePlayer.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MovieScene\Private\MovieSceneSequencePlayer.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MovieScene\Private\MovieSceneSequencePlayer.cpp
============================================================

SECTION starting at line 21 (left) / 21 (right):
----------------------------------------
  CONTEXT (line 21): void UMovieSceneSequencePlayer::SetWeight(double InWeight) {
  CONTEXT (line 22): }
  CONTEXT (line 23): 
  REMOVED (line 24): void UMovieSceneSequencePlayer::SetTimeRange(float NewStartTime, float duration) {
  ADDED   (line 24): void UMovieSceneSequencePlayer::SetTimeRange(float NewStartTime, float Duration) {
  CONTEXT (line 25): }
  CONTEXT (line 26): 
  CONTEXT (line 27): void UMovieSceneSequencePlayer::SetPlayRate(float PlayRate) {

SECTION starting at line 36 (left) / 36 (right):
----------------------------------------
  CONTEXT (line 36): void UMovieSceneSequencePlayer::SetFrameRate(FFrameRate FrameRate) {
  CONTEXT (line 37): }
  CONTEXT (line 38): 
  REMOVED (line 39): void UMovieSceneSequencePlayer::SetFrameRange(int32 StartFrame, int32 duration, float SubFrames) {
  ADDED   (line 39): void UMovieSceneSequencePlayer::SetFrameRange(int32 StartFrame, int32 Duration, float SubFrames) {
  CONTEXT (line 40): }
  CONTEXT (line 41): 
  CONTEXT (line 42): void UMovieSceneSequencePlayer::SetDisableCameraCuts(bool bInDisableCameraCuts) {