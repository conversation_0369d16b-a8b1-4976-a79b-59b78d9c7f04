--- Left: ListView.h
+++ Right: ListView.h
@@ -32,7 +32,7 @@
     FScrollBarStyle ScrollBarStyle;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    TEnumAsByte<EOrientation> orientation;

+    TEnumAsByte<EOrientation> Orientation;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     TEnumAsByte<ESelectionMode::Type> SelectionMode;
