DIFFERENCES IN: StateTreeModule\Private\StateTreeRandomTimeDuration.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\StateTreeModule\Private\StateTreeRandomTimeDuration.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\StateTreeModule\Private\StateTreeRandomTimeDuration.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "StateTreeRandomTimeDuration.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): FStateTreeRandomTimeDuration::FStateTreeRandomTimeDuration() {
  REMOVED (line 4):     this->duration = 0;
  ADDED   (line 4):     this->Duration = 0;
  CONTEXT (line 5):     this->RandomVariance = 0;
  CONTEXT (line 6): }
  CONTEXT (line 7): 