DIFFERENCES IN: Synthesis\Public\GranularSynth.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Synthesis\Public\GranularSynth.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Synthesis\Public\GranularSynth.h
============================================================

SECTION starting at line 65 (left) / 65 (right):
----------------------------------------
  CONTEXT (line 65):     void SetAttackTime(const float AttackTimeMsec);
  CONTEXT (line 66): 
  CONTEXT (line 67):     UFUNCTION(BlueprintCallable)
  REMOVED (line 68):     void NoteOn(const float Note, const int32 Velocity, const float duration);
  ADDED   (line 68):     void NoteOn(const float Note, const int32 Velocity, const float Duration);
  CONTEXT (line 69): 
  CONTEXT (line 70):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 71):     void NoteOff(const float Note, const bool bKill);