DIFFERENCES IN: Engine\Public\Material.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\Material.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\Material.h
============================================================

SECTION starting at line 124 (left) / 124 (right):
----------------------------------------
  CONTEXT (line 124):     UPROPERTY(AssetRegistrySearchable, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 125):     TEnumAsByte<ETranslucencyLightingMode> TranslucencyLightingMode;
  CONTEXT (line 126): 
  REMOVED (line 127):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 128):     uint8 bTranslucencyBackground: 1;
  REMOVED (line 129): 
  CONTEXT (line 130):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 131):     uint8 bEnableMobileSeparateTranslucency: 1;
  CONTEXT (line 132): 

SECTION starting at line 259 (left) / 256 (right):
----------------------------------------
  CONTEXT (line 259):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 260):     uint8 bFullyRough: 1;
  CONTEXT (line 261): 
  REMOVED (line 262):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 263):     uint8 bFullyRoughOnMobile: 1;
  REMOVED (line 264): 
  CONTEXT (line 265):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 266):     uint8 bUseFullPrecision: 1;
  CONTEXT (line 267): 

SECTION starting at line 293 (left) / 287 (right):
----------------------------------------
  CONTEXT (line 293):     uint8 bNormalCurvatureToRoughness: 1;
  CONTEXT (line 294): 
  CONTEXT (line 295):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 296):     uint8 bUseCheckerAsEdgeMaterialId: 1;
  REMOVED (line 297): 
  REMOVED (line 298):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 299):     uint8 AllowTranslucentCustomDepthWrites: 1;
  CONTEXT (line 300): 
  CONTEXT (line 301):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

SECTION starting at line 306 (left) / 297 (right):
----------------------------------------
  CONTEXT (line 306): 
  CONTEXT (line 307):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 308):     TEnumAsByte<EMaterialShadingRate> ShadingRate;
  REMOVED (line 309): 
  REMOVED (line 310):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 311):     TEnumAsByte<EMaterialShadingRate> ShadingRateOnMobile;
  CONTEXT (line 312): 
  CONTEXT (line 313):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 314):     uint8 bAllowVariableRateShading: 1;

SECTION starting at line 345 (left) / 333 (right):
----------------------------------------
  CONTEXT (line 345): 
  CONTEXT (line 346):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 347):     uint8 bIsSky: 1;
  REMOVED (line 348): 
  REMOVED (line 349):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 350):     uint8 bXRSoftOcclusions: 1;
  REMOVED (line 351): 
  REMOVED (line 352):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 353):     float XRSoftOcclusionsDepthBias;
  CONTEXT (line 354): 
  CONTEXT (line 355):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 356):     uint8 bComputeFogPerPixel: 1;