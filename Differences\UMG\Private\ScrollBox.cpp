DIFFERENCES IN: UMG\Private\ScrollBox.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\UMG\Private\ScrollBox.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\UMG\Private\ScrollBox.cpp
============================================================

SECTION starting at line 6 (left) / 6 (right):
----------------------------------------
  CONTEXT (line 6):     this->Clipping = EWidgetClipping::ClipToBounds;
  CONTEXT (line 7):     this->ScrollAnimationInterpolationSpeed = 15.00f;
  CONTEXT (line 8):     this->bEnableTouchScrolling = true;
  REMOVED (line 9):     this->orientation = Orient_Vertical;
  ADDED   (line 9):     this->Orientation = Orient_Vertical;
  CONTEXT (line 10):     this->ScrollBarVisibility = ESlateVisibility::Visible;
  CONTEXT (line 11):     this->ConsumeMouseWheel = EConsumeMouseWheel::WhenScrollingPossible;
  CONTEXT (line 12):     this->AlwaysShowScrollbar = false;