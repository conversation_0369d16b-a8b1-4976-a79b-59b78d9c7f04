DIFFERENCES IN: FieldSystemEngine\Public\PlaneFalloff.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\FieldSystemEngine\Public\PlaneFalloff.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\FieldSystemEngine\Public\PlaneFalloff.h
============================================================

SECTION starting at line 27 (left) / 27 (right):
----------------------------------------
  CONTEXT (line 27):     float Distance;
  CONTEXT (line 28): 
  CONTEXT (line 29):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 30):     FVector Position;
  ADDED   (line 30):     FVector position;
  CONTEXT (line 31): 
  CONTEXT (line 32):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 33):     FVector Normal;