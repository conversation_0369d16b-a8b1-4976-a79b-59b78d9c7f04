DIFFERENCES IN: Engine\Public\SceneComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\SceneComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\SceneComponent.h
============================================================

SECTION starting at line 86 (left) / 86 (right):
----------------------------------------
  CONTEXT (line 86):     UPROPERTY(BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))
  CONTEXT (line 87):     uint8 bHiddenInGame: 1;
  CONTEXT (line 88): 
  REMOVED (line 89):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 90):     uint8 bIsCookedForMobile: 1;
  REMOVED (line 91): 
  REMOVED (line 92):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 93):     uint8 bIsCookedForDesktop: 1;
  REMOVED (line 94): 
  CONTEXT (line 95):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 96):     uint8 bBoundsChangeTriggersStreamingDataRebuild: 1;
  CONTEXT (line 97): 