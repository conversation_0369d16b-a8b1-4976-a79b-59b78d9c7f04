DIFFERENCES IN: AnimGraphRuntime\Private\SequencePlayerLibrary.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AnimGraphRuntime\Private\SequencePlayerLibrary.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AnimGraphRuntime\Private\SequencePlayerLibrary.cpp
============================================================

SECTION starting at line 54 (left) / 54 (right):
----------------------------------------
  CONTEXT (line 54):     return FSequencePlayerReference{};
  CONTEXT (line 55): }
  CONTEXT (line 56): 
  REMOVED (line 57): float USequencePlayerLibrary::ComputePlayRateFromDuration(const FSequencePlayerReference& SequencePlayer, float duration) {
  ADDED   (line 57): float USequencePlayerLibrary::ComputePlayRateFromDuration(const FSequencePlayerReference& SequencePlayer, float Duration) {
  CONTEXT (line 58):     return 0.0f;
  CONTEXT (line 59): }
  CONTEXT (line 60): 