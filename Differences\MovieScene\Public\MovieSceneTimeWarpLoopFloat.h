DIFFERENCES IN: MovieScene\Public\MovieSceneTimeWarpLoopFloat.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MovieScene\Public\MovieSceneTimeWarpLoopFloat.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MovieScene\Public\MovieSceneTimeWarpLoopFloat.h
============================================================

SECTION starting at line 7 (left) / 7 (right):
----------------------------------------
  CONTEXT (line 7):     GENERATED_BODY()
  CONTEXT (line 8): public:
  CONTEXT (line 9):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 10):     float duration;
  ADDED   (line 10):     float Duration;
  CONTEXT (line 11): 
  CONTEXT (line 12):     MOVIESCENE_API FMovieSceneTimeWarpLoopFloat();
  CONTEXT (line 13): };