DIFFERENCES IN: AudioWidgets\Private\AudioMaterialMeter.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AudioWidgets\Private\AudioMaterialMeter.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AudioWidgets\Private\AudioMaterialMeter.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "AudioMaterialMeter.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): UAudioMaterialMeter::UAudioMaterialMeter() {
  REMOVED (line 4):     this->orientation = Orient_Vertical;
  ADDED   (line 4):     this->Orientation = Orient_Vertical;
  CONTEXT (line 5):     this->MeterChannelInfo.AddDefaulted(1);
  CONTEXT (line 6): }
  CONTEXT (line 7): 