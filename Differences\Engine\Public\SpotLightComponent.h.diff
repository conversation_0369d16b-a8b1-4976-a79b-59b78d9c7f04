--- Left: SpotLightComponent.h
+++ Right: SpotLightComponent.h
@@ -2,8 +2,6 @@
 #include "CoreMinimal.h"

 #include "PointLightComponent.h"

 #include "SpotLightComponent.generated.h"

-

-class UTexture2D;

 

 UCLASS(Blueprintable, EditInlineNew, MinimalAPI, ClassGroup=Custom, meta=(BlueprintSpawnableComponent))

 class USpotLightComponent : public UPointLightComponent {

@@ -15,12 +13,6 @@
     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     float OuterConeAngle;

     

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float NearClipPlane;

-    

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    UTexture2D* CookieTexture;

-    

     USpotLightComponent(const FObjectInitializer& ObjectInitializer);

 

     UFUNCTION(BlueprintCallable)
