DIFFERENCES IN: Engine\Public\MaterialExpressionClamp.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\MaterialExpressionClamp.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\MaterialExpressionClamp.h
============================================================

SECTION starting at line 13 (left) / 13 (right):
----------------------------------------
  CONTEXT (line 13):     FExpressionInput Input;
  CONTEXT (line 14): 
  CONTEXT (line 15):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 16):     FExpressionInput Min;
  ADDED   (line 16):     FExpressionInput min;
  CONTEXT (line 17): 
  CONTEXT (line 18):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 19):     FExpressionInput Max;
  ADDED   (line 19):     FExpressionInput max;
  CONTEXT (line 20): 
  CONTEXT (line 21):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 22):     TEnumAsByte<EClampMode> ClampMode;