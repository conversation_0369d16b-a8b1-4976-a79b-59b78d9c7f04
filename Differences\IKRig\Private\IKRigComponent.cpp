DIFFERENCES IN: IKRig\Private\IKRigComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\IKRig\Private\IKRigComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\IKRig\Private\IKRigComponent.cpp
============================================================

SECTION starting at line 6 (left) / 6 (right):
----------------------------------------
  CONTEXT (line 6): void UIKRigComponent::SetIKRigGoalTransform(const FName GoalName, const FTransform Transform, const float PositionAlpha, const float RotationAlpha) {
  CONTEXT (line 7): }
  CONTEXT (line 8): 
  REMOVED (line 9): void UIKRigComponent::SetIKRigGoalPositionAndRotation(const FName GoalName, const FVector Position, const FQuat Rotation, const float PositionAlpha, const float RotationAlpha) {
  ADDED   (line 9): void UIKRigComponent::SetIKRigGoalPositionAndRotation(const FName GoalName, const FVector position, const FQuat Rotation, const float PositionAlpha, const float RotationAlpha) {
  CONTEXT (line 10): }
  CONTEXT (line 11): 
  CONTEXT (line 12): void UIKRigComponent::SetIKRigGoal(const FIKRigGoal& Goal) {