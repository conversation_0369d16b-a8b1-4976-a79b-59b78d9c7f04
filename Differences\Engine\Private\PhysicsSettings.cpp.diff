--- Left: PhysicsSettings.cpp
+++ Right: PhysicsSettings.cpp
@@ -13,16 +13,15 @@
     this->bSimulateAnimPhysicsAfterReset = false;

     this->MinPhysicsDeltaTime = 0.00f;

     this->MaxPhysicsDeltaTime = 0.03f;

-    this->bSubstepping = true;

+    this->bSubstepping = false;

     this->bSubsteppingAsync = false;

     this->bTickPhysicsAsync = false;

     this->AsyncFixedTimeStepSize = 0.03f;

     this->MaxSubstepDeltaTime = 0.02f;

-    this->MaxSubsteps = 3;

+    this->MaxSubsteps = 6;

     this->SyncSceneSmoothingFactor = 0.00f;

     this->InitialAverageFrameRate = 0.02f;

     this->PhysXTreeRebuildRate = 10;

-    this->PhysicalSurfaces.AddDefaulted(13);

     this->MinDeltaVelocityForHitEvents = 0.00f;

 }

 
