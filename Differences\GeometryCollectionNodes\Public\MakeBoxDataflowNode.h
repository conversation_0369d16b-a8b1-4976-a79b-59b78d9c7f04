DIFFERENCES IN: GeometryCollectionNodes\Public\MakeBoxDataflowNode.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCollectionNodes\Public\MakeBoxDataflowNode.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCollectionNodes\Public\MakeBoxDataflowNode.h
============================================================

SECTION starting at line 14 (left) / 14 (right):
----------------------------------------
  CONTEXT (line 14):     EMakeBoxDataTypeEnum DataType;
  CONTEXT (line 15): 
  CONTEXT (line 16):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 17):     FVector Min;
  ADDED   (line 17):     FVector min;
  CONTEXT (line 18): 
  CONTEXT (line 19):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 20):     FVector Max;
  ADDED   (line 20):     FVector max;
  CONTEXT (line 21): 
  CONTEXT (line 22):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 23):     FVector Center;