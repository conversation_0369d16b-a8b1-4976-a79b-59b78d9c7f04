--- Left: EnhancedInputDeveloperSettings.cpp
+++ Right: EnhancedInputDeveloperSettings.cpp
@@ -1,6 +1,7 @@
 #include "EnhancedInputDeveloperSettings.h"

 

 UEnhancedInputDeveloperSettings::UEnhancedInputDeveloperSettings() {

+    this->DefaultMappingContexts.AddDefaulted(5);

     this->bSendTriggeredEventsWhenInputIsFlushed = true;

     this->bEnableUserSettings = false;

     this->bEnableDefaultMappingContexts = true;
