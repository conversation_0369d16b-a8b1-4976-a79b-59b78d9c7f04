DIFFERENCES IN: InterchangeNodes\Public\InterchangeAnimationTrackNode.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\InterchangeNodes\Public\InterchangeAnimationTrackNode.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\InterchangeNodes\Public\InterchangeAnimationTrackNode.h
============================================================

SECTION starting at line 19 (left) / 19 (right):
----------------------------------------
  CONTEXT (line 19):     bool SetCustomFrameCount(const int32& AttributeValue);
  CONTEXT (line 20): 
  CONTEXT (line 21):     UFUNCTION(BlueprintCallable)
  REMOVED (line 22):     bool SetCustomAnimationPayloadKey(const FString& InUniqueID, const EInterchangeAnimationPayLoadType& InType);
  ADDED   (line 22):     bool SetCustomAnimationPayloadKey(const FString& InUniqueId, const EInterchangeAnimationPayLoadType& InType);
  CONTEXT (line 23): 
  CONTEXT (line 24):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 25):     bool SetCustomActorDependencyUid(const FString& DependencyUid);