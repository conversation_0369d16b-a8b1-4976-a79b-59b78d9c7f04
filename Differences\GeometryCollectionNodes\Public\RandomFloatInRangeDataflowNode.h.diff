--- Left: RandomFloatInRangeDataflowNode.h
+++ Right: RandomFloatInRangeDataflowNode.h
@@ -14,10 +14,10 @@
     float RandomSeed;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float Min;

+    float min;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float Max;

+    float max;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     float ReturnValue;
