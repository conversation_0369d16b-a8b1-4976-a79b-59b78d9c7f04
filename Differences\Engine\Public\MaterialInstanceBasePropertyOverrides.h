DIFFERENCES IN: Engine\Public\MaterialInstanceBasePropertyOverrides.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\MaterialInstanceBasePropertyOverrides.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\MaterialInstanceBasePropertyOverrides.h
============================================================

SECTION starting at line 41 (left) / 41 (right):
----------------------------------------
  CONTEXT (line 41):     uint8 bOverride_bEnableTessellation: 1;
  CONTEXT (line 42): 
  CONTEXT (line 43):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 44):     uint8 bOverride_bFullyRough: 1;
  REMOVED (line 45): 
  REMOVED (line 46):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 47):     uint8 bOverride_bFullyRoughOnMobile: 1;
  REMOVED (line 48): 
  REMOVED (line 49):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 50):     uint8 bOverride_DisplacementScaling: 1;
  CONTEXT (line 51): 
  CONTEXT (line 52):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

SECTION starting at line 80 (left) / 74 (right):
----------------------------------------
  CONTEXT (line 80):     uint8 bEnableTessellation: 1;
  CONTEXT (line 81): 
  CONTEXT (line 82):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 83):     uint8 bFullyRough: 1;
  REMOVED (line 84): 
  REMOVED (line 85):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 86):     uint8 bFullyRoughOnMobile: 1;
  REMOVED (line 87): 
  REMOVED (line 88):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 89):     uint8 bEnableDisplacementFade: 1;
  CONTEXT (line 90): 
  CONTEXT (line 91):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))