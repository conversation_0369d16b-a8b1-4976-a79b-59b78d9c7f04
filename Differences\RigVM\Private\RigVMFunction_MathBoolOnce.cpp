DIFFERENCES IN: RigVM\Private\RigVMFunction_MathBoolOnce.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\RigVM\Private\RigVMFunction_MathBoolOnce.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\RigVM\Private\RigVMFunction_MathBoolOnce.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "RigVMFunction_MathBoolOnce.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): FRigVMFunction_MathBoolOnce::FRigVMFunction_MathBoolOnce() {
  REMOVED (line 4):     this->duration = 0.00f;
  ADDED   (line 4):     this->Duration = 0.00f;
  CONTEXT (line 5):     this->Result = false;
  CONTEXT (line 6):     this->LastValue = false;
  CONTEXT (line 7):     this->TimeLeft = 0.00f;