--- Left: PrimitiveComponent.cpp
+++ Right: PrimitiveComponent.cpp
@@ -27,7 +27,7 @@
     this->bVisibleInRayTracing = true;

     this->bRenderInMainPass = true;

     this->bRenderInDepthPass = true;

-    this->bReceivesDecals = false;

+    this->bReceivesDecals = true;

     this->bHoldout = false;

     this->bOwnerNoSee = false;

     this->bOnlyOwnerSee = false;

@@ -37,7 +37,6 @@
     this->bWantsEditorEffects = false;

     this->bForceMipStreaming = false;

     this->bHasPerInstanceHitProxies = false;

-    this->bShouldBeExcludedFromLightmaps = false;

     this->CastShadow = false;

     this->bEmissiveLightSource = false;

     this->bAffectDynamicIndirectLighting = true;

@@ -204,7 +203,7 @@
 void UPrimitiveComponent::SetLinearDamping(float InDamping) {

 }

 

-void UPrimitiveComponent::SetLightingChannels(bool bChannel0, bool bChannel1) {

+void UPrimitiveComponent::SetLightingChannels(bool bChannel0, bool bChannel1, bool bChannel2) {

 }

 

 void UPrimitiveComponent::SetLightAttachmentsAsGroup(bool bInLightAttachmentsAsGroup) {
