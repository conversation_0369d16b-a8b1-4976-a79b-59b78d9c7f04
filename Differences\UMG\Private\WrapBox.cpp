DIFFERENCES IN: UMG\Private\WrapBox.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\UMG\Private\WrapBox.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\UMG\Private\WrapBox.cpp
============================================================

SECTION starting at line 7 (left) / 7 (right):
----------------------------------------
  CONTEXT (line 7):     this->WrapSize = 500.00f;
  CONTEXT (line 8):     this->bExplicitWrapSize = false;
  CONTEXT (line 9):     this->HorizontalAlignment = HAlign_Left;
  REMOVED (line 10):     this->orientation = Orient_Horizontal;
  ADDED   (line 10):     this->Orientation = Orient_Horizontal;
  CONTEXT (line 11): }
  CONTEXT (line 12): 
  CONTEXT (line 13): void UWrapBox::SetInnerSlotPadding(FVector2D InPadding) {