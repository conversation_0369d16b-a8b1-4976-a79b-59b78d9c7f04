DIFFERENCES IN: Niagara\Public\NiagaraDistributionRangeInt.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Niagara\Public\NiagaraDistributionRangeInt.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Niagara\Public\NiagaraDistributionRangeInt.h
============================================================

SECTION starting at line 15 (left) / 15 (right):
----------------------------------------
  CONTEXT (line 15):     FNiagaraVariableBase ParameterBinding;
  CONTEXT (line 16): 
  CONTEXT (line 17):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 18):     int32 Min;
  ADDED   (line 18):     int32 min;
  CONTEXT (line 19): 
  CONTEXT (line 20):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 21):     int32 Max;
  ADDED   (line 21):     int32 max;
  CONTEXT (line 22): 
  CONTEXT (line 23):     NIAGARA_API FNiagaraDistributionRangeInt();
  CONTEXT (line 24): };