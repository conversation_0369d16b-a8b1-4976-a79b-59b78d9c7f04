DIFFERENCES IN: Engine\Public\CurveLinearColorAtlas.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\CurveLinearColorAtlas.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\CurveLinearColorAtlas.h
============================================================

SECTION starting at line 24 (left) / 24 (right):
----------------------------------------
  CONTEXT (line 24):     UCurveLinearColorAtlas();
  CONTEXT (line 25): 
  CONTEXT (line 26):     UFUNCTION(BlueprintCallable)
  REMOVED (line 27):     bool GetCurvePosition(UCurveLinearColor* InCurve, float& Position);
  ADDED   (line 27):     bool GetCurvePosition(UCurveLinearColor* InCurve, float& position);
  CONTEXT (line 28): 
  CONTEXT (line 29): };
  CONTEXT (line 30): 