--- Left: DirectionalLightComponent.cpp
+++ Right: DirectionalLightComponent.cpp
@@ -22,11 +22,6 @@
     this->ForwardShadingPriority = 0;

     this->LightSourceAngle = 0.54f;

     this->LightSourceSoftAngle = 0.00f;

-    this->LightDistanceAttenuationBegin = 1000.00f;

-    this->LightDistanceAttenuationEnd = 2000.00f;

-    this->LightDistanceAttenuationMinIntensity = 1.00f;

-    this->DistantLightMinThreshold = 0.00f;

-    this->DistantLightMaxThreshold = 1.00f;

     this->ShadowSourceAngleFactor = 1.00f;

     this->TraceDistance = 10000.00f;

     this->bUsedAsAtmosphereSunLight = false;

@@ -74,15 +69,6 @@
 void UDirectionalLightComponent::SetLightShaftOverrideDirection(FVector NewValue) {

 }

 

-void UDirectionalLightComponent::SetLightDistanceAttenuationMinIntensity(float NewValue) {

-}

-

-void UDirectionalLightComponent::SetLightDistanceAttenuationEnd(float NewValue) {

-}

-

-void UDirectionalLightComponent::SetLightDistanceAttenuationBegin(float NewValue) {

-}

-

 void UDirectionalLightComponent::SetForwardShadingPriority(int32 NewValue) {

 }

 

@@ -96,12 +82,6 @@
 }

 

 void UDirectionalLightComponent::SetDynamicShadowCascades(int32 NewValue) {

-}

-

-void UDirectionalLightComponent::SetDistantLightMinThreshold(float NewValue) {

-}

-

-void UDirectionalLightComponent::SetDistantLightMaxThreshold(float NewValue) {

 }

 

 void UDirectionalLightComponent::SetCascadeTransitionFraction(float NewValue) {
