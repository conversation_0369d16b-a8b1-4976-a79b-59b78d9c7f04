DIFFERENCES IN: ControlRig\Public\RigUnit_Clamp_Float.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\ControlRig\Public\RigUnit_Clamp_Float.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\ControlRig\Public\RigUnit_Clamp_Float.h
============================================================

SECTION starting at line 11 (left) / 11 (right):
----------------------------------------
  CONTEXT (line 11):     float Value;
  CONTEXT (line 12): 
  CONTEXT (line 13):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 14):     float Min;
  ADDED   (line 14):     float min;
  CONTEXT (line 15): 
  CONTEXT (line 16):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 17):     float Max;
  ADDED   (line 17):     float max;
  CONTEXT (line 18): 
  CONTEXT (line 19):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 20):     float Result;