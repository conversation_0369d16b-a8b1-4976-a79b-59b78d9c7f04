DIFFERENCES IN: StateTreeModule\Public\StateTreeDelayTaskInstanceData.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\StateTreeModule\Public\StateTreeDelayTaskInstanceData.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\StateTreeModule\Public\StateTreeDelayTaskInstanceData.h
============================================================

SECTION starting at line 7 (left) / 7 (right):
----------------------------------------
  CONTEXT (line 7):     GENERATED_BODY()
  CONTEXT (line 8): public:
  CONTEXT (line 9):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 10):     float duration;
  ADDED   (line 10):     float Duration;
  CONTEXT (line 11): 
  CONTEXT (line 12):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 13):     float RandomDeviation;