DIFFERENCES IN: Synthesis\Private\ModularSynthComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Synthesis\Private\ModularSynthComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Synthesis\Private\ModularSynthComponent.cpp
============================================================

SECTION starting at line 173 (left) / 173 (right):
----------------------------------------
  CONTEXT (line 173): void UModularSynthComponent::SetAttackTime(float AttackTimeMsec) {
  CONTEXT (line 174): }
  CONTEXT (line 175): 
  REMOVED (line 176): void UModularSynthComponent::NoteOn(const float Note, const int32 Velocity, const float duration) {
  ADDED   (line 176): void UModularSynthComponent::NoteOn(const float Note, const int32 Velocity, const float Duration) {
  CONTEXT (line 177): }
  CONTEXT (line 178): 
  CONTEXT (line 179): void UModularSynthComponent::NoteOff(const float Note, const bool bAllNotesOff, const bool bKillAllNotes) {