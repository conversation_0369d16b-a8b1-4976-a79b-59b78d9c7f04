--- Left: PlayerCameraManager.cpp
+++ Right: PlayerCameraManager.cpp
@@ -60,7 +60,7 @@
     return NULL;

 }

 

-void APlayerCameraManager::StartCameraFade(float FromAlpha, float ToAlpha, float duration, FLinearColor Color, bool bShouldFadeAudio, bool bHoldWhenFinished) {

+void APlayerCameraManager::StartCameraFade(float FromAlpha, float ToAlpha, float Duration, FLinearColor Color, bool bShouldFadeAudio, bool bHoldWhenFinished) {

 }

 

 void APlayerCameraManager::SetManualCameraFade(float InFadeAmount, FLinearColor Color, bool bInFadeAudio) {
