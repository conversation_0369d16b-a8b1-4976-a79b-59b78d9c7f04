--- Left: WaveScalarFieldDataflowNode.h
+++ Right: WaveScalarFieldDataflowNode.h
@@ -22,13 +22,13 @@
     float Magnitude;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    FVector Position;

+    FVector position;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     FVector Translation;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float WaveLength;

+    float Wavelength;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     float Period;
