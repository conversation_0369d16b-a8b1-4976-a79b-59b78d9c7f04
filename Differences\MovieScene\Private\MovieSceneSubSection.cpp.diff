--- Left: MovieSceneSubSection.cpp
+++ Right: MovieSceneSubSection.cpp
@@ -2,7 +2,7 @@
 

 UMovieSceneSubSection::UMovieSceneSubSection() {

     this->StartOffset = -340282346638528859811704183484516925440.00f;

-    this->timescale = -340282346638528859811704183484516925440.00f;

+    this->TimeScale = -340282346638528859811704183484516925440.00f;

     const FProperty* p_PrerollTime = GetClass()->FindPropertyByName("PrerollTime");

     (*p_PrerollTime->ContainerPtrToValuePtr<float>(this)) = -340282346638528859811704183484516925440.00f;

     this->NetworkMask = 3;
