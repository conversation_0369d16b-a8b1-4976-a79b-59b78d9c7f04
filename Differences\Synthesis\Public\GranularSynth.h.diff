--- Left: GranularSynth.h
+++ Right: GranularSynth.h
@@ -65,7 +65,7 @@
     void SetAttackTime(const float AttackTimeMsec);

     

     UFUNCTION(BlueprintCallable)

-    void NoteOn(const float Note, const int32 Velocity, const float duration);

+    void NoteOn(const float Note, const int32 Velocity, const float Duration);

     

     UFUNCTION(BlueprintCallable)

     void NoteOff(const float Note, const bool bKill);
