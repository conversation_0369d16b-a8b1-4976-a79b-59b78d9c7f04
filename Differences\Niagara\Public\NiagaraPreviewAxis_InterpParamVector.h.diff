--- Left: NiagaraPreviewAxis_InterpParamVector.h
+++ Right: NiagaraPreviewAxis_InterpParamVector.h
@@ -10,10 +10,10 @@
 public:

 private:

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    FVector Min;

+    FVector min;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    FVector Max;

+    FVector max;

     

 public:

     UNiagaraPreviewAxis_InterpParamVector();
