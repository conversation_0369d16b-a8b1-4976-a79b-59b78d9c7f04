DIFFERENCES IN: GeometryCollectionNodes\Public\WaveScalarFieldDataflowNode.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCollectionNodes\Public\WaveScalarFieldDataflowNode.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCollectionNodes\Public\WaveScalarFieldDataflowNode.h
============================================================

SECTION starting at line 22 (left) / 22 (right):
----------------------------------------
  CONTEXT (line 22):     float Magnitude;
  CONTEXT (line 23): 
  CONTEXT (line 24):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 25):     FVector Position;
  ADDED   (line 25):     FVector position;
  CONTEXT (line 26): 
  CONTEXT (line 27):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 28):     FVector Translation;
  CONTEXT (line 29): 
  CONTEXT (line 30):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 31):     float WaveLength;
  ADDED   (line 31):     float Wavelength;
  CONTEXT (line 32): 
  CONTEXT (line 33):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 34):     float Period;