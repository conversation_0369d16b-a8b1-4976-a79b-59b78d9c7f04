DIFFERENCES IN: UMG\Private\StackBox.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\UMG\Private\StackBox.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\UMG\Private\StackBox.cpp
============================================================

SECTION starting at line 4 (left) / 4 (right):
----------------------------------------
  CONTEXT (line 4): UStackBox::UStackBox() {
  CONTEXT (line 5):     this->bIsVariable = false;
  CONTEXT (line 6):     this->Visibility = ESlateVisibility::SelfHitTestInvisible;
  REMOVED (line 7):     this->orientation = Orient_Horizontal;
  ADDED   (line 7):     this->Orientation = Orient_Horizontal;
  CONTEXT (line 8): }
  CONTEXT (line 9): 
  CONTEXT (line 10): bool UStackBox::ReplaceStackBoxChildAt(int32 Index, UWidget* Content) {