DIFFERENCES IN: Engine\Public\MaterialExpressionVectorNoise.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\MaterialExpressionVectorNoise.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\MaterialExpressionVectorNoise.h
============================================================

SECTION starting at line 11 (left) / 11 (right):
----------------------------------------
  CONTEXT (line 11):     GENERATED_BODY()
  CONTEXT (line 12): public:
  CONTEXT (line 13):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 14):     FExpressionInput Position;
  ADDED   (line 14):     FExpressionInput position;
  CONTEXT (line 15): 
  CONTEXT (line 16):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 17):     EPositionOrigin WorldPositionOriginType;