DIFFERENCES IN: NavigationSystem\Private\NavCollision.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\NavigationSystem\Private\NavCollision.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\NavigationSystem\Private\NavCollision.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): UNavCollision::UNavCollision() {
  CONTEXT (line 4):     this->AreaClass = NULL;
  REMOVED (line 5):     this->bGatherConvexGeometry = false;
  ADDED   (line 5):     this->bGatherConvexGeometry = true;
  CONTEXT (line 6):     this->bCreateOnClient = true;
  CONTEXT (line 7): }
  CONTEXT (line 8): 