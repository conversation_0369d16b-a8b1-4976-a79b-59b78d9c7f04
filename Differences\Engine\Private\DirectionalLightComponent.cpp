DIFFERENCES IN: Engine\Private\DirectionalLightComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\DirectionalLightComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\DirectionalLightComponent.cpp
============================================================

SECTION starting at line 22 (left) / 22 (right):
----------------------------------------
  CONTEXT (line 22):     this->ForwardShadingPriority = 0;
  CONTEXT (line 23):     this->LightSourceAngle = 0.54f;
  CONTEXT (line 24):     this->LightSourceSoftAngle = 0.00f;
  REMOVED (line 25):     this->LightDistanceAttenuationBegin = 1000.00f;
  REMOVED (line 26):     this->LightDistanceAttenuationEnd = 2000.00f;
  REMOVED (line 27):     this->LightDistanceAttenuationMinIntensity = 1.00f;
  REMOVED (line 28):     this->DistantLightMinThreshold = 0.00f;
  REMOVED (line 29):     this->DistantLightMaxThreshold = 1.00f;
  CONTEXT (line 30):     this->ShadowSourceAngleFactor = 1.00f;
  CONTEXT (line 31):     this->TraceDistance = 10000.00f;
  CONTEXT (line 32):     this->bUsedAsAtmosphereSunLight = false;

SECTION starting at line 74 (left) / 69 (right):
----------------------------------------
  CONTEXT (line 74): void UDirectionalLightComponent::SetLightShaftOverrideDirection(FVector NewValue) {
  CONTEXT (line 75): }
  CONTEXT (line 76): 
  REMOVED (line 77): void UDirectionalLightComponent::SetLightDistanceAttenuationMinIntensity(float NewValue) {
  REMOVED (line 78): }
  REMOVED (line 79): 
  REMOVED (line 80): void UDirectionalLightComponent::SetLightDistanceAttenuationEnd(float NewValue) {
  REMOVED (line 81): }
  REMOVED (line 82): 
  REMOVED (line 83): void UDirectionalLightComponent::SetLightDistanceAttenuationBegin(float NewValue) {
  REMOVED (line 84): }
  REMOVED (line 85): 
  CONTEXT (line 86): void UDirectionalLightComponent::SetForwardShadingPriority(int32 NewValue) {
  CONTEXT (line 87): }
  CONTEXT (line 88): 

SECTION starting at line 96 (left) / 82 (right):
----------------------------------------
  CONTEXT (line 96): }
  CONTEXT (line 97): 
  CONTEXT (line 98): void UDirectionalLightComponent::SetDynamicShadowCascades(int32 NewValue) {
  REMOVED (line 99): }
  REMOVED (line 100): 
  REMOVED (line 101): void UDirectionalLightComponent::SetDistantLightMinThreshold(float NewValue) {
  REMOVED (line 102): }
  REMOVED (line 103): 
  REMOVED (line 104): void UDirectionalLightComponent::SetDistantLightMaxThreshold(float NewValue) {
  CONTEXT (line 105): }
  CONTEXT (line 106): 
  CONTEXT (line 107): void UDirectionalLightComponent::SetCascadeTransitionFraction(float NewValue) {