DIFFERENCES IN: LevelSequence\Private\LevelSequenceDirector.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\LevelSequence\Private\LevelSequenceDirector.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\LevelSequence\Private\LevelSequenceDirector.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): ULevelSequenceDirector::ULevelSequenceDirector() {
  CONTEXT (line 4):     this->SubSequenceID = 0;
  REMOVED (line 5):     this->InstanceID = 65535;
  ADDED   (line 5):     this->InstanceId = 65535;
  CONTEXT (line 6):     this->InstanceSerial = 0;
  CONTEXT (line 7):     this->Player = NULL;
  CONTEXT (line 8):     this->MovieScenePlayerIndex = 0;