DIFFERENCES IN: Engine\Private\Console.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\Console.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\Console.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): UConsole::UConsole() {
  CONTEXT (line 4):     this->ConsoleTargetPlayer = NULL;
  REMOVED (line 5):     this->HistoryBuffer.AddDefaulted(18);
  CONTEXT (line 6): }
  CONTEXT (line 7): 
  CONTEXT (line 8): 