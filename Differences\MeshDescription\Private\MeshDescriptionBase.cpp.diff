--- Left: MeshDescriptionBase.cpp
+++ Right: MeshDescriptionBase.cpp
@@ -3,7 +3,7 @@
 UMeshDescriptionBase::UMeshDescriptionBase() {

 }

 

-void UMeshDescriptionBase::SetVertexPosition(FVertexID VertexID, const FVector& Position) {

+void UMeshDescriptionBase::SetVertexPosition(FVertexID VertexID, const FVector& position) {

 }

 

 void UMeshDescriptionBase::SetPolygonVertexInstances(FPolygonID PolygonID, const TArray<FVertexInstanceID>& VertexInstanceIDs) {
