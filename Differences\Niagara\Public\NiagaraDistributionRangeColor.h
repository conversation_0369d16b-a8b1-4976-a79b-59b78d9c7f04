DIFFERENCES IN: Niagara\Public\NiagaraDistributionRangeColor.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Niagara\Public\NiagaraDistributionRangeColor.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Niagara\Public\NiagaraDistributionRangeColor.h
============================================================

SECTION starting at line 9 (left) / 9 (right):
----------------------------------------
  CONTEXT (line 9):     GENERATED_BODY()
  CONTEXT (line 10): public:
  CONTEXT (line 11):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 12):     FLinearColor Min;
  ADDED   (line 12):     FLinearColor min;
  CONTEXT (line 13): 
  CONTEXT (line 14):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 15):     FLinearColor Max;
  ADDED   (line 15):     FLinearColor max;
  CONTEXT (line 16): 
  CONTEXT (line 17):     NIAGARA_API FNiagaraDistributionRangeColor();
  CONTEXT (line 18): };