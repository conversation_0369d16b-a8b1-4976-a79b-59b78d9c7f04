DIFFERENCES IN: MovieScene\Public\MovieSceneSubSection.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MovieScene\Public\MovieSceneSubSection.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MovieScene\Public\MovieSceneSubSection.h
============================================================

SECTION starting at line 21 (left) / 21 (right):
----------------------------------------
  CONTEXT (line 21):     float StartOffset;
  CONTEXT (line 22): 
  CONTEXT (line 23):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 24):     float timescale;
  ADDED   (line 24):     float TimeScale;
  CONTEXT (line 25): 
  CONTEXT (line 26):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 27):     float PrerollTime;