DIFFERENCES IN: Engine\Private\PostProcessSettings.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\PostProcessSettings.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\PostProcessSettings.cpp
============================================================

SECTION starting at line 151 (left) / 151 (right):
----------------------------------------
  CONTEXT (line 151):     this->bOverride_LPVDirectionalOcclusionFadeRange = false;
  CONTEXT (line 152):     this->bOverride_IndirectLightingColor = false;
  CONTEXT (line 153):     this->bOverride_IndirectLightingIntensity = false;
  REMOVED (line 154):     this->bOverride_IndirectLightingColorFar = false;
  REMOVED (line 155):     this->bOverride_IndirectLightingIntensityFar = false;
  REMOVED (line 156):     this->bOverride_IndirectLightingFarBegin = false;
  REMOVED (line 157):     this->bOverride_IndirectLightingFarEnd = false;
  REMOVED (line 158):     this->bOverride_IndirectLightingBrightnessThreshold = false;
  REMOVED (line 159):     this->bOverride_IndirectLightingBrightnessSmoothness = false;
  CONTEXT (line 160):     this->bOverride_ColorGradingIntensity = false;
  CONTEXT (line 161):     this->bOverride_ColorGradingLUT = false;
  CONTEXT (line 162):     this->bOverride_DepthOfFieldFocalDistance = false;

SECTION starting at line 277 (left) / 271 (right):
----------------------------------------
  CONTEXT (line 277):     this->BloomDirtMaskIntensity = 0.00f;
  CONTEXT (line 278):     this->DynamicGlobalIlluminationMethod = EDynamicGlobalIlluminationMethod::None;
  CONTEXT (line 279):     this->IndirectLightingIntensity = 0.00f;
  REMOVED (line 280):     this->IndirectLightingIntensityFar = 0.00f;
  REMOVED (line 281):     this->IndirectLightingFarBegin = 0.00f;
  REMOVED (line 282):     this->IndirectLightingFarEnd = 0.00f;
  REMOVED (line 283):     this->IndirectLightingBrightnessThreshold = 0.00f;
  REMOVED (line 284):     this->IndirectLightingBrightnessSmoothness = 0.00f;
  CONTEXT (line 285):     this->LumenRayLightingMode = ELumenRayLightingModeOverride::Default;
  CONTEXT (line 286):     this->LumenSceneLightingQuality = 0.00f;
  CONTEXT (line 287):     this->LumenSceneDetail = 0.00f;