--- Left: SkeletalMeshComponent.h
+++ Right: SkeletalMeshComponent.h
@@ -424,7 +424,7 @@
     void Play(bool bLooping);

     

     UFUNCTION(BlueprintCallable)

-    void OverrideAnimationData(UAnimationAsset* InAnimToPlay, bool bIsLooping, bool bIsPlaying, float Position, float PlayRate);

+    void OverrideAnimationData(UAnimationAsset* InAnimToPlay, bool bIsLooping, bool bIsPlaying, float position, float PlayRate);

     

     UFUNCTION(BlueprintCallable)

     void LinkAnimGraphByTag(FName InTag, TSubclassOf<UAnimInstance> InClass);
