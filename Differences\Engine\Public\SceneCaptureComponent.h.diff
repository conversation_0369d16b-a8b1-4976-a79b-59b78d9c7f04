--- Left: SceneCaptureComponent.h
+++ Right: SceneCaptureComponent.h
@@ -25,9 +25,6 @@
     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bCaptureOnMovement: 1;

     

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bShowEditorWidget: 1;

-    

     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, DuplicateTransient, EditAnywhere, NonTransactional, SkipSerialization, Transient, meta=(AllowPrivateAccess=true))

     uint8 bCaptureGpuNextRender: 1;

     

@@ -51,9 +48,6 @@
     

     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     float LODDistanceFactor;

-    

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float ViewDistanceScaleOverride;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     float MaxViewDistanceOverride;
