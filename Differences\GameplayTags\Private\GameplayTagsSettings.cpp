DIFFERENCES IN: GameplayTags\Private\GameplayTagsSettings.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GameplayTags\Private\GameplayTagsSettings.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GameplayTags\Private\GameplayTagsSettings.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "GameplayTagsSettings.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): UGameplayTagsSettings::UGameplayTagsSettings() {
  REMOVED (line 4):     this->ConfigFileName = TEXT("../../../IntoTheRadius2/Config/DefaultGameplayTags.ini");
  REMOVED (line 5):     this->GameplayTagList.AddDefaulted(2185);
  ADDED   (line 4):     this->ConfigFileName = TEXT("../../../CV_5_5_4/Config/DefaultGameplayTags.ini");
  CONTEXT (line 6):     this->ImportTagsFromConfig = true;
  CONTEXT (line 7):     this->WarnOnInvalidTags = true;
  CONTEXT (line 8):     this->ClearInvalidTags = false;
  CONTEXT (line 9):     this->AllowEditorTagUnloading = true;
  CONTEXT (line 10):     this->AllowGameTagUnloading = false;
  REMOVED (line 11):     this->FastReplication = true;
  ADDED   (line 10):     this->FastReplication = false;
  CONTEXT (line 12):     this->bDynamicReplication = false;
  CONTEXT (line 13):     this->InvalidTagCharacters = TEXT("\"',");
  REMOVED (line 14):     this->GameplayTagTableList.AddDefaulted(4);
  REMOVED (line 15):     this->GameplayTagRedirects.AddDefaulted(329);
  CONTEXT (line 16):     this->NumBitsForContainerSize = 6;
  CONTEXT (line 17):     this->NetIndexFirstBitSegment = 16;
  CONTEXT (line 18): }