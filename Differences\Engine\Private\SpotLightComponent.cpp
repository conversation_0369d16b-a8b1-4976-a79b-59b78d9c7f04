DIFFERENCES IN: Engine\Private\SpotLightComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\SpotLightComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\SpotLightComponent.cpp
============================================================

SECTION starting at line 3 (left) / 3 (right):
----------------------------------------
  CONTEXT (line 3): USpotLightComponent::USpotLightComponent(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {
  CONTEXT (line 4):     this->InnerConeAngle = 0.00f;
  CONTEXT (line 5):     this->OuterConeAngle = 44.00f;
  REMOVED (line 6):     this->NearClipPlane = 0.00f;
  REMOVED (line 7):     this->CookieTexture = NULL;
  CONTEXT (line 8): }
  CONTEXT (line 9): 
  CONTEXT (line 10): void USpotLightComponent::SetOuterConeAngle(float NewOuterConeAngle) {