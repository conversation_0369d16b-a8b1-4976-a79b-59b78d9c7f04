DIFFERENCES IN: UMG\Public\WrapBox.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\UMG\Public\WrapBox.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\UMG\Public\WrapBox.h
============================================================

SECTION starting at line 26 (left) / 26 (right):
----------------------------------------
  CONTEXT (line 26):     TEnumAsByte<EHorizontalAlignment> HorizontalAlignment;
  CONTEXT (line 27): 
  CONTEXT (line 28):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 29):     TEnumAsByte<EOrientation> orientation;
  ADDED   (line 29):     TEnumAsByte<EOrientation> Orientation;
  CONTEXT (line 30): 
  CONTEXT (line 31):     UWrapBox();
  CONTEXT (line 32): 