--- Left: ScrollBox.cpp
+++ Right: ScrollBox.cpp
@@ -6,7 +6,7 @@
     this->Clipping = EWidgetClipping::ClipToBounds;

     this->ScrollAnimationInterpolationSpeed = 15.00f;

     this->bEnableTouchScrolling = true;

-    this->orientation = Orient_Vertical;

+    this->Orientation = Orient_Vertical;

     this->ScrollBarVisibility = ESlateVisibility::Visible;

     this->ConsumeMouseWheel = EConsumeMouseWheel::WhenScrollingPossible;

     this->AlwaysShowScrollbar = false;
