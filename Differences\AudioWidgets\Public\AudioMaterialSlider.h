DIFFERENCES IN: AudioWidgets\Public\AudioMaterialSlider.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AudioWidgets\Public\AudioMaterialSlider.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AudioWidgets\Public\AudioMaterialSlider.h
============================================================

SECTION starting at line 21 (left) / 21 (right):
----------------------------------------
  CONTEXT (line 21):     float Value;
  CONTEXT (line 22): 
  CONTEXT (line 23):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 24):     TEnumAsByte<EOrientation> orientation;
  ADDED   (line 24):     TEnumAsByte<EOrientation> Orientation;
  CONTEXT (line 25): 
  CONTEXT (line 26):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 27):     float TuneSpeed;