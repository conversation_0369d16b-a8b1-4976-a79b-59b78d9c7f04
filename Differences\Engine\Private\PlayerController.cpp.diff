--- Left: PlayerController.cpp
+++ Right: PlayerController.cpp
@@ -173,15 +173,15 @@
     return true;

 }

 

-void APlayerController::ServerUnmutePlayer_Implementation(FUniqueNetIdRepl PlayerID) {

-}

-bool APlayerController::ServerUnmutePlayer_Validate(FUniqueNetIdRepl PlayerID) {

-    return true;

-}

-

-void APlayerController::ServerUnblockPlayer_Implementation(FUniqueNetIdRepl PlayerID) {

-}

-bool APlayerController::ServerUnblockPlayer_Validate(FUniqueNetIdRepl PlayerID) {

+void APlayerController::ServerUnmutePlayer_Implementation(FUniqueNetIdRepl PlayerId) {

+}

+bool APlayerController::ServerUnmutePlayer_Validate(FUniqueNetIdRepl PlayerId) {

+    return true;

+}

+

+void APlayerController::ServerUnblockPlayer_Implementation(FUniqueNetIdRepl PlayerId) {

+}

+bool APlayerController::ServerUnblockPlayer_Validate(FUniqueNetIdRepl PlayerId) {

     return true;

 }

 

@@ -233,9 +233,9 @@
     return true;

 }

 

-void APlayerController::ServerMutePlayer_Implementation(FUniqueNetIdRepl PlayerID) {

-}

-bool APlayerController::ServerMutePlayer_Validate(FUniqueNetIdRepl PlayerID) {

+void APlayerController::ServerMutePlayer_Implementation(FUniqueNetIdRepl PlayerId) {

+}

+bool APlayerController::ServerMutePlayer_Validate(FUniqueNetIdRepl PlayerId) {

     return true;

 }

 

@@ -272,9 +272,9 @@
     return true;

 }

 

-void APlayerController::ServerBlockPlayer_Implementation(FUniqueNetIdRepl PlayerID) {

-}

-bool APlayerController::ServerBlockPlayer_Validate(FUniqueNetIdRepl PlayerID) {

+void APlayerController::ServerBlockPlayer_Implementation(FUniqueNetIdRepl PlayerId) {

+}

+bool APlayerController::ServerBlockPlayer_Validate(FUniqueNetIdRepl PlayerId) {

     return true;

 }

 

@@ -303,7 +303,7 @@
 void APlayerController::PlayHapticEffect(UHapticFeedbackEffect_Base* HapticEffect, EControllerHand Hand, float Scale, bool bLoop) {

 }

 

-void APlayerController::PlayDynamicForceFeedback(float Intensity, float duration, bool bAffectsLeftLarge, bool bAffectsLeftSmall, bool bAffectsRightLarge, bool bAffectsRightSmall, TEnumAsByte<EDynamicForceFeedbackAction::Type> Action, FLatentActionInfo LatentInfo) {

+void APlayerController::PlayDynamicForceFeedback(float Intensity, float Duration, bool bAffectsLeftLarge, bool bAffectsLeftSmall, bool bAffectsRightLarge, bool bAffectsRightSmall, TEnumAsByte<EDynamicForceFeedbackAction::Type> Action, FLatentActionInfo LatentInfo) {

 }

 

 void APlayerController::Pause() {

@@ -455,7 +455,7 @@
 void APlayerController::ClientUnmutePlayers_Implementation(const TArray<FUniqueNetIdRepl>& PlayerIds) {

 }

 

-void APlayerController::ClientUnmutePlayer_Implementation(FUniqueNetIdRepl PlayerID) {

+void APlayerController::ClientUnmutePlayer_Implementation(FUniqueNetIdRepl PlayerId) {

 }

 

 void APlayerController::ClientTravelInternal_Implementation(const FString& URL, TEnumAsByte<ETravelType> TravelType, bool bSeamless, FGuid MapPackageGuid) {

@@ -557,7 +557,7 @@
 void APlayerController::ClientPlayForceFeedback_Internal_Implementation(UForceFeedbackEffect* ForceFeedbackEffect, FForceFeedbackParameters Params) {

 }

 

-void APlayerController::ClientMutePlayer_Implementation(FUniqueNetIdRepl PlayerID) {

+void APlayerController::ClientMutePlayer_Implementation(FUniqueNetIdRepl PlayerId) {

 }

 

 void APlayerController::ClientMessage_Implementation(const FString& S, FName Type, float MsgLifeTime) {

@@ -602,7 +602,7 @@
 void APlayerController::ClientCancelPendingMapChange_Implementation() {

 }

 

-void APlayerController::ClientAddTextureStreamingLoc_Implementation(FVector InLoc, float duration, bool bOverrideLocation) {

+void APlayerController::ClientAddTextureStreamingLoc_Implementation(FVector InLoc, float Duration, bool bOverrideLocation) {

 }

 

 void APlayerController::ClientAckUpdateLevelVisibility_Implementation(FName PackageName, FNetLevelVisibilityTransactionId TransactionId, bool bClientAckCanMakeVisible) {
