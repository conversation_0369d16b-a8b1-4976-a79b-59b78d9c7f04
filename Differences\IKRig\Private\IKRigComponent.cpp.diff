--- Left: IKRigComponent.cpp
+++ Right: IKRigComponent.cpp
@@ -6,7 +6,7 @@
 void UIKRigComponent::SetIKRigGoalTransform(const FName GoalName, const FTransform Transform, const float PositionAlpha, const float RotationAlpha) {

 }

 

-void UIKRigComponent::SetIKRigGoalPositionAndRotation(const FName GoalName, const FVector Position, const FQuat Rotation, const float PositionAlpha, const float RotationAlpha) {

+void UIKRigComponent::SetIKRigGoalPositionAndRotation(const FName GoalName, const FVector position, const FQuat Rotation, const float PositionAlpha, const float RotationAlpha) {

 }

 

 void UIKRigComponent::SetIKRigGoal(const FIKRigGoal& Goal) {
