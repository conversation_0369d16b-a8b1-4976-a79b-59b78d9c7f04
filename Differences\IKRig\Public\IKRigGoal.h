DIFFERENCES IN: IKRig\Public\IKRigGoal.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\IKRig\Public\IKRigGoal.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\IKRig\Public\IKRigGoal.h
============================================================

SECTION starting at line 22 (left) / 22 (right):
----------------------------------------
  CONTEXT (line 22):     FBoneReference SourceBone;
  CONTEXT (line 23): 
  CONTEXT (line 24):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 25):     FVector Position;
  ADDED   (line 25):     FVector position;
  CONTEXT (line 26): 
  CONTEXT (line 27):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 28):     FRotator Rotation;