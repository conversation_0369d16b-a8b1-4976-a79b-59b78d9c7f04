DIFFERENCES IN: Engine\Public\DecalComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\DecalComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\DecalComponent.h
============================================================

SECTION starting at line 53 (left) / 53 (right):
----------------------------------------
  CONTEXT (line 53):     void SetFadeScreenSize(float NewFadeScreenSize);
  CONTEXT (line 54): 
  CONTEXT (line 55):     UFUNCTION(BlueprintCallable)
  REMOVED (line 56):     void SetFadeOut(float StartDelay, float duration, bool DestroyOwnerAfterFade);
  ADDED   (line 56):     void SetFadeOut(float StartDelay, float Duration, bool DestroyOwnerAfterFade);
  CONTEXT (line 57): 
  CONTEXT (line 58):     UFUNCTION(BlueprintCallable)
  REMOVED (line 59):     void SetFadeIn(float StartDelay, float duration);
  ADDED   (line 59):     void SetFadeIn(float StartDelay, float Duration);
  CONTEXT (line 60): 
  CONTEXT (line 61):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 62):     void SetDecalMaterial(UMaterialInterface* NewDecalMaterial);