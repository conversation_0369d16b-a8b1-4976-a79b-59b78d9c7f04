--- Left: AudioSliderBase.h
+++ Right: AudioSliderBase.h
@@ -63,7 +63,7 @@
     UWidget::FGetLinearColor WidgetBackgroundColorDelegate;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    TEnumAsByte<EOrientation> orientation;

+    TEnumAsByte<EOrientation> Orientation;

     

     UPROPERTY(BlueprintAssignable, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     FOnFloatValueChangedEvent OnValueChanged;
