DIFFERENCES IN: Engine\Public\SplineComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\SplineComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\SplineComponent.h
============================================================

SECTION starting at line 22 (left) / 22 (right):
----------------------------------------
  CONTEXT (line 22):     int32 ReparamStepsPerSegment;
  CONTEXT (line 23): 
  CONTEXT (line 24):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 25):     float duration;
  ADDED   (line 25):     float Duration;
  CONTEXT (line 26): 
  CONTEXT (line 27):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Replicated, meta=(AllowPrivateAccess=true))
  CONTEXT (line 28):     bool bStationaryEndpoints;

SECTION starting at line 373 (left) / 373 (right):
----------------------------------------
  CONTEXT (line 373):     void ClearSplinePoints(bool bUpdateSpline);
  CONTEXT (line 374): 
  CONTEXT (line 375):     UFUNCTION(BlueprintCallable)
  REMOVED (line 376):     void AddSplineWorldPoint(const FVector& Position);
  REMOVED (line 377): 
  REMOVED (line 378):     UFUNCTION(BlueprintCallable)
  REMOVED (line 379):     void AddSplinePointAtIndex(const FVector& Position, int32 Index, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline);
  REMOVED (line 380): 
  REMOVED (line 381):     UFUNCTION(BlueprintCallable)
  REMOVED (line 382):     void AddSplinePoint(const FVector& Position, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline);
  REMOVED (line 383): 
  REMOVED (line 384):     UFUNCTION(BlueprintCallable)
  REMOVED (line 385):     void AddSplineLocalPoint(const FVector& Position);
  ADDED   (line 376):     void AddSplineWorldPoint(const FVector& position);
  ADDED   (line 377): 
  ADDED   (line 378):     UFUNCTION(BlueprintCallable)
  ADDED   (line 379):     void AddSplinePointAtIndex(const FVector& position, int32 Index, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline);
  ADDED   (line 380): 
  ADDED   (line 381):     UFUNCTION(BlueprintCallable)
  ADDED   (line 382):     void AddSplinePoint(const FVector& position, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline);
  ADDED   (line 383): 
  ADDED   (line 384):     UFUNCTION(BlueprintCallable)
  ADDED   (line 385):     void AddSplineLocalPoint(const FVector& position);
  CONTEXT (line 386): 
  CONTEXT (line 387):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 388):     void AddPoints(const TArray<FSplinePoint>& Points, bool bUpdateSpline);