DIFFERENCES IN: Engine\Public\RootMotionSource.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\RootMotionSource.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\RootMotionSource.h
============================================================

SECTION starting at line 30 (left) / 30 (right):
----------------------------------------
  CONTEXT (line 30):     float PreviousTime;
  CONTEXT (line 31): 
  CONTEXT (line 32):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 33):     float duration;
  ADDED   (line 33):     float Duration;
  CONTEXT (line 34): 
  CONTEXT (line 35):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 36):     FRootMotionSourceStatus Status;