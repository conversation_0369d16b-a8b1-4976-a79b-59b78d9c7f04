DIFFERENCES IN: CoreUObject\Public\Box2D.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\CoreUObject\Public\Box2D.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\CoreUObject\Public\Box2D.h
============================================================

SECTION starting at line 8 (left) / 8 (right):
----------------------------------------
  CONTEXT (line 8):     GENERATED_BODY()
  CONTEXT (line 9): public:
  CONTEXT (line 10):     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))
  REMOVED (line 11):     FVector2D Min;
  ADDED   (line 11):     FVector2D min;
  CONTEXT (line 12): 
  CONTEXT (line 13):     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))
  REMOVED (line 14):     FVector2D Max;
  ADDED   (line 14):     FVector2D max;
  CONTEXT (line 15): 
  CONTEXT (line 16):     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))
  CONTEXT (line 17):     bool bIsValid;