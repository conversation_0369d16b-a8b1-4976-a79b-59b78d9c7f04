DIFFERENCES IN: Engine\Private\MaterialInstanceBasePropertyOverrides.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\MaterialInstanceBasePropertyOverrides.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\MaterialInstanceBasePropertyOverrides.cpp
============================================================

SECTION starting at line 11 (left) / 11 (right):
----------------------------------------
  CONTEXT (line 11):     this->bOverride_OutputTranslucentVelocity = false;
  CONTEXT (line 12):     this->bOverride_bHasPixelAnimation = false;
  CONTEXT (line 13):     this->bOverride_bEnableTessellation = false;
  REMOVED (line 14):     this->bOverride_bFullyRough = false;
  REMOVED (line 15):     this->bOverride_bFullyRoughOnMobile = false;
  CONTEXT (line 16):     this->bOverride_DisplacementScaling = false;
  CONTEXT (line 17):     this->bOverride_bEnableDisplacementFade = false;
  CONTEXT (line 18):     this->bOverride_DisplacementFadeRange = false;

SECTION starting at line 24 (left) / 22 (right):
----------------------------------------
  CONTEXT (line 24):     this->bOutputTranslucentVelocity = false;
  CONTEXT (line 25):     this->bHasPixelAnimation = false;
  CONTEXT (line 26):     this->bEnableTessellation = false;
  REMOVED (line 27):     this->bFullyRough = false;
  REMOVED (line 28):     this->bFullyRoughOnMobile = false;
  CONTEXT (line 29):     this->bEnableDisplacementFade = false;
  CONTEXT (line 30):     this->BlendMode = BLEND_Opaque;
  CONTEXT (line 31):     this->ShadingModel = MSM_Unlit;