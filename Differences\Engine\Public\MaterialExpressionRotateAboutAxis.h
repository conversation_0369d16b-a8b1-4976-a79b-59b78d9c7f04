DIFFERENCES IN: Engine\Public\MaterialExpressionRotateAboutAxis.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\MaterialExpressionRotateAboutAxis.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\MaterialExpressionRotateAboutAxis.h
============================================================

SECTION starting at line 18 (left) / 18 (right):
----------------------------------------
  CONTEXT (line 18):     FExpressionInput PivotPoint;
  CONTEXT (line 19): 
  CONTEXT (line 20):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 21):     FExpressionInput Position;
  ADDED   (line 21):     FExpressionInput position;
  CONTEXT (line 22): 
  CONTEXT (line 23):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 24):     float Period;