--- Left: WorldSettings.cpp
+++ Right: WorldSettings.cpp
@@ -12,7 +12,6 @@
     (*p_RemoteRole->ContainerPtrToValuePtr<TEnumAsByte<ENetRole>>(this)) = ROLE_SimulatedProxy;

     this->VisibilityCellSize = 200;

     this->VisibilityAggressiveness = VIS_LeastAggressive;

-    this->VisibilityMaxDrawDistanceScale = 1.00f;

     this->bPrecomputeVisibility = false;

     this->bPlaceCellsOnlyAlongCameraTracks = false;

     this->bEnableWorldBoundsChecks = true;
