DIFFERENCES IN: InterchangeNodes\Private\InterchangeAnimationTrackNode.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\InterchangeNodes\Private\InterchangeAnimationTrackNode.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\InterchangeNodes\Private\InterchangeAnimationTrackNode.cpp
============================================================

SECTION starting at line 11 (left) / 11 (right):
----------------------------------------
  CONTEXT (line 11):     return false;
  CONTEXT (line 12): }
  CONTEXT (line 13): 
  REMOVED (line 14): bool UInterchangeAnimationTrackNode::SetCustomAnimationPayloadKey(const FString& InUniqueID, const EInterchangeAnimationPayLoadType& InType) {
  ADDED   (line 14): bool UInterchangeAnimationTrackNode::SetCustomAnimationPayloadKey(const FString& InUniqueId, const EInterchangeAnimationPayLoadType& InType) {
  CONTEXT (line 15):     return false;
  CONTEXT (line 16): }
  CONTEXT (line 17): 