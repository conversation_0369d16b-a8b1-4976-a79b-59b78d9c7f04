DIFFERENCES IN: Engine\Public\ParticleModuleAttractorPointGravity.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\ParticleModuleAttractorPointGravity.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\ParticleModuleAttractorPointGravity.h
============================================================

SECTION starting at line 12 (left) / 12 (right):
----------------------------------------
  CONTEXT (line 12):     GENERATED_BODY()
  CONTEXT (line 13): public:
  CONTEXT (line 14):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 15):     FVector Position;
  ADDED   (line 15):     FVector position;
  CONTEXT (line 16): 
  CONTEXT (line 17):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 18):     float Radius;