--- Left: MovieSceneSequenceExtensions.cpp
+++ Right: MovieSceneSequenceExtensions.cpp
@@ -72,11 +72,11 @@
 void UMovieSceneSequenceExtensions::RemoveRootFolderFromSequence(UMovieSceneSequence* Sequence, UMovieSceneFolder* Folder) {

 }

 

-FSequencerScriptingRange UMovieSceneSequenceExtensions::MakeRangeSeconds(UMovieSceneSequence* Sequence, float StartTime, float duration) {

+FSequencerScriptingRange UMovieSceneSequenceExtensions::MakeRangeSeconds(UMovieSceneSequence* Sequence, float StartTime, float Duration) {

     return FSequencerScriptingRange{};

 }

 

-FSequencerScriptingRange UMovieSceneSequenceExtensions::MakeRange(UMovieSceneSequence* Sequence, int32 StartFrame, int32 duration) {

+FSequencerScriptingRange UMovieSceneSequenceExtensions::MakeRange(UMovieSceneSequence* Sequence, int32 StartFrame, int32 Duration) {

     return FSequencerScriptingRange{};

 }

 
