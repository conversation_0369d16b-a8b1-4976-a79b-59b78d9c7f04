DIFFERENCES IN: FieldSystemEngine\Public\FieldSystemComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\FieldSystemEngine\Public\FieldSystemComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\FieldSystemEngine\Public\FieldSystemComponent.h
============================================================

SECTION starting at line 42 (left) / 42 (right):
----------------------------------------
  CONTEXT (line 42):     void RemovePersistentFields();
  CONTEXT (line 43): 
  CONTEXT (line 44):     UFUNCTION(BlueprintCallable)
  REMOVED (line 45):     void ApplyUniformVectorFalloffForce(bool Enabled, FVector Position, FVector Direction, float Radius, float Magnitude);
  ADDED   (line 45):     void ApplyUniformVectorFalloffForce(bool Enabled, FVector position, FVector Direction, float Radius, float Magnitude);
  CONTEXT (line 46): 
  CONTEXT (line 47):     UFUNCTION(BlueprintCallable)
  REMOVED (line 48):     void ApplyStrainField(bool Enabled, FVector Position, float Radius, float Magnitude, int32 Iterations);
  ADDED   (line 48):     void ApplyStrainField(bool Enabled, FVector position, float Radius, float Magnitude, int32 Iterations);
  CONTEXT (line 49): 
  CONTEXT (line 50):     UFUNCTION(BlueprintCallable)
  REMOVED (line 51):     void ApplyStayDynamicField(bool Enabled, FVector Position, float Radius);
  ADDED   (line 51):     void ApplyStayDynamicField(bool Enabled, FVector position, float Radius);
  CONTEXT (line 52): 
  CONTEXT (line 53):     UFUNCTION(BlueprintCallable)
  REMOVED (line 54):     void ApplyRadialVectorFalloffForce(bool Enabled, FVector Position, float Radius, float Magnitude);
  ADDED   (line 54):     void ApplyRadialVectorFalloffForce(bool Enabled, FVector position, float Radius, float Magnitude);
  CONTEXT (line 55): 
  CONTEXT (line 56):     UFUNCTION(BlueprintCallable)
  REMOVED (line 57):     void ApplyRadialForce(bool Enabled, FVector Position, float Magnitude);
  ADDED   (line 57):     void ApplyRadialForce(bool Enabled, FVector position, float Magnitude);
  CONTEXT (line 58): 
  CONTEXT (line 59):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 60):     void ApplyPhysicsField(bool Enabled, TEnumAsByte<EFieldPhysicsType> Target, UFieldSystemMetaData* MetaData, UFieldNodeBase* Field);