DIFFERENCES IN: Engine\Private\Engine.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\Engine.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\Engine.cpp
============================================================

SECTION starting at line 90 (left) / 90 (right):
----------------------------------------
  CONTEXT (line 90):     this->StatColorMappings.AddDefaulted(3);
  CONTEXT (line 91):     this->DefaultPhysMaterial = NULL;
  CONTEXT (line 92):     this->DefaultDestructiblePhysMaterial = NULL;
  REMOVED (line 93):     this->ActiveClassRedirects.AddDefaulted(3);
  ADDED   (line 93):     this->ActiveGameNameRedirects.AddDefaulted(2);
  CONTEXT (line 94):     this->PreIntegratedSkinBRDFTexture = NULL;
  CONTEXT (line 95):     this->BlueNoiseScalarTexture = NULL;
  CONTEXT (line 96):     this->BlueNoiseVec2Texture = NULL;

SECTION starting at line 103 (left) / 103 (right):
----------------------------------------
  CONTEXT (line 103):     this->DiffuseEnergyTexture = NULL;
  CONTEXT (line 104):     this->GlintTexture = NULL;
  CONTEXT (line 105):     this->GlintTexture2 = NULL;
  REMOVED (line 106):     this->EnvironmentMaskShapesTexture = NULL;
  CONTEXT (line 107):     this->SimpleVolumeTexture = NULL;
  CONTEXT (line 108):     this->SimpleVolumeEnvTexture = NULL;
  CONTEXT (line 109):     this->MiniFontTexture = NULL;

SECTION starting at line 111 (left) / 110 (right):
----------------------------------------
  CONTEXT (line 111):     this->WeightMapArrayPlaceholderTexture = NULL;
  CONTEXT (line 112):     this->LightMapDensityTexture = NULL;
  CONTEXT (line 113):     this->GameViewport = NULL;
  REMOVED (line 114):     this->NearClipPlane = 2.00f;
  ADDED   (line 113):     this->NearClipPlane = 10.00f;
  CONTEXT (line 115):     this->bSubtitlesEnabled = true;
  CONTEXT (line 116):     this->bSubtitlesForcedOff = false;
  REMOVED (line 117):     this->MaximumLoopIterationCount = 5000000;
  REMOVED (line 118):     this->bCanBlueprintsTickByDefault = false;
  ADDED   (line 116):     this->MaximumLoopIterationCount = 1000000;
  ADDED   (line 117):     this->bCanBlueprintsTickByDefault = true;
  CONTEXT (line 119):     this->bOptimizeAnimBlueprintMemberVariableAccess = true;
  CONTEXT (line 120):     this->bAllowMultiThreadedAnimationUpdate = true;
  CONTEXT (line 121):     this->bEnableEditorPSysRealtimeLOD = false;