--- Left: ModularSynthComponent.h
+++ Right: ModularSynthComponent.h
@@ -194,7 +194,7 @@
     void SetAttackTime(float AttackTimeMsec);

     

     UFUNCTION(BlueprintCallable)

-    void NoteOn(const float Note, const int32 Velocity, const float duration);

+    void NoteOn(const float Note, const int32 Velocity, const float Duration);

     

     UFUNCTION(BlueprintCallable)

     void NoteOff(const float Note, const bool bAllNotesOff, const bool bKillAllNotes);
