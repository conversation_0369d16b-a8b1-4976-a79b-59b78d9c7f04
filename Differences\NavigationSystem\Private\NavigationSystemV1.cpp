DIFFERENCES IN: NavigationSystem\Private\NavigationSystemV1.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\NavigationSystem\Private\NavigationSystemV1.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\NavigationSystem\Private\NavigationSystemV1.cpp
============================================================

SECTION starting at line 4 (left) / 4 (right):
----------------------------------------
  CONTEXT (line 4): UNavigationSystemV1::UNavigationSystemV1() {
  CONTEXT (line 5):     this->MainNavData = NULL;
  CONTEXT (line 6):     this->AbstractNavData = NULL;
  REMOVED (line 7):     this->bAutoCreateNavigationData = false;
  REMOVED (line 8):     this->bSpawnNavDataInNavBoundsLevel = true;
  REMOVED (line 9):     this->bAllowClientSideNavigation = false;
  REMOVED (line 10):     this->bShouldDiscardSubLevelNavData = false;
  ADDED   (line 7):     this->bAutoCreateNavigationData = true;
  ADDED   (line 8):     this->bSpawnNavDataInNavBoundsLevel = false;
  ADDED   (line 9):     this->bAllowClientSideNavigation = true;
  ADDED   (line 10):     this->bShouldDiscardSubLevelNavData = true;
  CONTEXT (line 11):     this->bTickWhilePaused = false;
  CONTEXT (line 12):     this->bSupportRebuilding = false;
  CONTEXT (line 13):     this->bInitialBuildingLocked = false;

SECTION starting at line 16 (left) / 16 (right):
----------------------------------------
  CONTEXT (line 16):     this->bGenerateNavigationOnlyAroundNavigationInvokers = false;
  CONTEXT (line 17):     this->ActiveTilesUpdateInterval = 1.00f;
  CONTEXT (line 18):     this->InvokersMaximumDistanceFromSeed = -1.00f;
  REMOVED (line 19):     this->DataGatheringMode = ENavDataGatheringModeConfig::Lazy;
  ADDED   (line 19):     this->DataGatheringMode = ENavDataGatheringModeConfig::Instant;
  CONTEXT (line 20):     this->DirtyAreaWarningSizeThreshold = -1.00f;
  CONTEXT (line 21):     this->GatheringNavModifiersWarningLimitTime = -1.00f;
  REMOVED (line 22):     this->SupportedAgents.AddDefaulted(2);
  CONTEXT (line 23):     this->OperationMode = FNavigationSystemRunMode::InvalidMode;
  CONTEXT (line 24):     this->Repository = NULL;
  CONTEXT (line 25): }