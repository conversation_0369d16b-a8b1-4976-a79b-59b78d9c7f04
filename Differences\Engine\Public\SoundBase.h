DIFFERENCES IN: Engine\Public\SoundBase.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\SoundBase.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\SoundBase.h
============================================================

SECTION starting at line 57 (left) / 57 (right):
----------------------------------------
  CONTEXT (line 57):     FSoundConcurrencySettings ConcurrencyOverrides;
  CONTEXT (line 58): 
  CONTEXT (line 59):     UPROPERTY(AssetRegistrySearchable, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 60):     float duration;
  ADDED   (line 60):     float Duration;
  CONTEXT (line 61): 
  CONTEXT (line 62):     UPROPERTY(AssetRegistrySearchable, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 63):     float MaxDistance;