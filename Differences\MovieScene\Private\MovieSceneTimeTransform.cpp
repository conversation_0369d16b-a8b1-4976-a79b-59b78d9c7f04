DIFFERENCES IN: MovieScene\Private\MovieSceneTimeTransform.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MovieScene\Private\MovieSceneTimeTransform.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MovieScene\Private\MovieSceneTimeTransform.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "MovieSceneTimeTransform.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): FMovieSceneTimeTransform::FMovieSceneTimeTransform() {
  REMOVED (line 4):     this->timescale = 0.00f;
  ADDED   (line 4):     this->TimeScale = 0.00f;
  CONTEXT (line 5): }
  CONTEXT (line 6): 