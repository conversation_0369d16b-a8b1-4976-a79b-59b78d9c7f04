DIFFERENCES IN: AudioWidgets\Private\AudioSliderBase.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AudioWidgets\Private\AudioSliderBase.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AudioWidgets\Private\AudioSliderBase.cpp
============================================================

SECTION starting at line 7 (left) / 7 (right):
----------------------------------------
  CONTEXT (line 7):     this->ShowUnitsText = true;
  CONTEXT (line 8):     this->IsUnitsTextReadOnly = true;
  CONTEXT (line 9):     this->IsValueTextReadOnly = false;
  REMOVED (line 10):     this->orientation = Orient_Vertical;
  ADDED   (line 10):     this->Orientation = Orient_Vertical;
  CONTEXT (line 11): }
  CONTEXT (line 12): 
  CONTEXT (line 13): void UAudioSliderBase::SetWidgetBackgroundColor(FLinearColor InValue) {