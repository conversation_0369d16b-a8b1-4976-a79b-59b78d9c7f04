--- Left: MovieSceneSequenceExtensions.h
+++ Right: MovieSceneSequenceExtensions.h
@@ -93,10 +93,10 @@
     static void RemoveRootFolderFromSequence(UMovieSceneSequence* Sequence, UMovieSceneFolder* Folder);

     

     UFUNCTION(BlueprintCallable)

-    static FSequencerScriptingRange MakeRangeSeconds(UMovieSceneSequence* Sequence, float StartTime, float duration);

-    

-    UFUNCTION(BlueprintCallable)

-    static FSequencerScriptingRange MakeRange(UMovieSceneSequence* Sequence, int32 StartFrame, int32 duration);

+    static FSequencerScriptingRange MakeRangeSeconds(UMovieSceneSequence* Sequence, float StartTime, float Duration);

+    

+    UFUNCTION(BlueprintCallable)

+    static FSequencerScriptingRange MakeRange(UMovieSceneSequence* Sequence, int32 StartFrame, int32 Duration);

     

     UFUNCTION(BlueprintCallable)

     static TArray<UObject*> LocateBoundObjects(UMovieSceneSequence* Sequence, const FMovieSceneBindingProxy& InBinding, UObject* Context);
