#!/usr/bin/env python3
"""
Folder Comparison Tool

A comprehensive Python application that recursively compares two folders
and identifies files that are different, missing, or added.

Features:
- Recursive folder comparison
- Multiple comparison modes (content, size, timestamp)
- Detailed reporting with statistics
- Export results to various formats (JSON, CSV, HTML)
- Command-line interface with flexible options
- Progress tracking for large directories
"""

import os
import sys
import argparse
import hashlib
import json
import csv
import time
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class FileInfo:
    """Information about a file"""
    path: str
    size: int
    modified_time: float
    hash_md5: Optional[str] = None
    exists: bool = True


@dataclass
class ComparisonResult:
    """Result of comparing two files or directories"""
    relative_path: str
    status: str  # 'identical', 'different', 'only_in_left', 'only_in_right', 'error'
    left_file: Optional[FileInfo] = None
    right_file: Optional[FileInfo] = None
    difference_type: Optional[str] = None  # 'content', 'size', 'timestamp'
    error_message: Optional[str] = None


class FolderComparator:
    """Main class for comparing folders"""
    
    def __init__(self, left_folder: str, right_folder: str, 
                 compare_content: bool = True, 
                 compare_timestamps: bool = False,
                 ignore_patterns: List[str] = None,
                 progress_callback=None):
        """
        Initialize the folder comparator
        
        Args:
            left_folder: Path to the first folder
            right_folder: Path to the second folder
            compare_content: Whether to compare file contents (slower but more accurate)
            compare_timestamps: Whether to consider timestamp differences
            ignore_patterns: List of patterns to ignore (e.g., ['*.tmp', '__pycache__'])
            progress_callback: Function to call for progress updates
        """
        self.left_folder = Path(left_folder).resolve()
        self.right_folder = Path(right_folder).resolve()
        self.compare_content = compare_content
        self.compare_timestamps = compare_timestamps
        self.ignore_patterns = ignore_patterns or []
        self.progress_callback = progress_callback
        
        # Validate folders exist
        if not self.left_folder.exists():
            raise ValueError(f"Left folder does not exist: {self.left_folder}")
        if not self.right_folder.exists():
            raise ValueError(f"Right folder does not exist: {self.right_folder}")
    
    def _should_ignore(self, path: Path) -> bool:
        """Check if a path should be ignored based on patterns"""
        import fnmatch
        path_str = str(path)
        for pattern in self.ignore_patterns:
            if fnmatch.fnmatch(path_str, pattern) or fnmatch.fnmatch(path.name, pattern):
                return True
        return False
    
    def _get_file_info(self, file_path: Path) -> FileInfo:
        """Get information about a file"""
        try:
            stat = file_path.stat()
            file_info = FileInfo(
                path=str(file_path),
                size=stat.st_size,
                modified_time=stat.st_mtime
            )
            
            # Calculate hash if content comparison is enabled
            if self.compare_content and file_path.is_file():
                file_info.hash_md5 = self._calculate_file_hash(file_path)
            
            return file_info
        except Exception as e:
            return FileInfo(
                path=str(file_path),
                size=0,
                modified_time=0,
                exists=False
            )
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate MD5 hash of a file"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    def _get_all_files(self, folder: Path) -> Dict[str, Path]:
        """Get all files in a folder recursively"""
        files = {}
        try:
            for item in folder.rglob("*"):
                if item.is_file() and not self._should_ignore(item):
                    relative_path = item.relative_to(folder)
                    files[str(relative_path)] = item
        except Exception as e:
            print(f"Error scanning folder {folder}: {e}")
        return files
    
    def compare(self) -> List[ComparisonResult]:
        """Compare the two folders and return results"""
        print(f"Comparing folders:")
        print(f"  Left:  {self.left_folder}")
        print(f"  Right: {self.right_folder}")
        print()
        
        # Get all files from both folders
        print("Scanning folders...")
        left_files = self._get_all_files(self.left_folder)
        right_files = self._get_all_files(self.right_folder)
        
        print(f"Found {len(left_files)} files in left folder")
        print(f"Found {len(right_files)} files in right folder")
        print()
        
        # Get all unique file paths
        all_paths = set(left_files.keys()) | set(right_files.keys())
        results = []
        
        total_files = len(all_paths)
        processed = 0
        
        for relative_path in sorted(all_paths):
            processed += 1
            
            if self.progress_callback:
                self.progress_callback(processed, total_files, relative_path)
            elif processed % 100 == 0 or processed == total_files:
                print(f"Progress: {processed}/{total_files} files processed")
            
            left_path = left_files.get(relative_path)
            right_path = right_files.get(relative_path)
            
            result = self._compare_files(relative_path, left_path, right_path)
            results.append(result)
        
        return results
    
    def _compare_files(self, relative_path: str, 
                      left_path: Optional[Path], 
                      right_path: Optional[Path]) -> ComparisonResult:
        """Compare two specific files"""
        
        # Handle missing files
        if left_path is None:
            right_info = self._get_file_info(right_path)
            return ComparisonResult(
                relative_path=relative_path,
                status='only_in_right',
                right_file=right_info
            )
        
        if right_path is None:
            left_info = self._get_file_info(left_path)
            return ComparisonResult(
                relative_path=relative_path,
                status='only_in_left',
                left_file=left_info
            )
        
        # Both files exist, compare them
        left_info = self._get_file_info(left_path)
        right_info = self._get_file_info(right_path)
        
        # Check if either file couldn't be read
        if not left_info.exists or not right_info.exists:
            return ComparisonResult(
                relative_path=relative_path,
                status='error',
                left_file=left_info,
                right_file=right_info,
                error_message="Could not read one or both files"
            )
        
        # Compare files
        differences = []
        
        # Size comparison
        if left_info.size != right_info.size:
            differences.append('size')
        
        # Content comparison (if enabled and sizes match)
        if self.compare_content and left_info.size == right_info.size:
            if left_info.hash_md5 != right_info.hash_md5:
                differences.append('content')
        
        # Timestamp comparison (if enabled)
        if self.compare_timestamps:
            if abs(left_info.modified_time - right_info.modified_time) > 1:  # 1 second tolerance
                differences.append('timestamp')
        
        if differences:
            return ComparisonResult(
                relative_path=relative_path,
                status='different',
                left_file=left_info,
                right_file=right_info,
                difference_type=', '.join(differences)
            )
        else:
            return ComparisonResult(
                relative_path=relative_path,
                status='identical',
                left_file=left_info,
                right_file=right_info
            )


class ResultsReporter:
    """Class for generating reports from comparison results"""
    
    def __init__(self, results: List[ComparisonResult], 
                 left_folder: str, right_folder: str):
        self.results = results
        self.left_folder = left_folder
        self.right_folder = right_folder
    
    def print_summary(self):
        """Print a summary of the comparison results"""
        stats = self._calculate_stats()
        
        print("=== COMPARISON SUMMARY ===")
        print(f"Total files compared: {stats['total']}")
        print(f"Identical files: {stats['identical']}")
        print(f"Different files: {stats['different']}")
        print(f"Only in left folder: {stats['only_left']}")
        print(f"Only in right folder: {stats['only_right']}")
        print(f"Errors: {stats['errors']}")
        print()
        
        if stats['different'] > 0:
            print("=== FILES WITH DIFFERENCES ===")
            for result in self.results:
                if result.status == 'different':
                    print(f"  {result.relative_path} ({result.difference_type})")
        
        if stats['only_left'] > 0:
            print(f"\n=== FILES ONLY IN LEFT FOLDER ({self.left_folder}) ===")
            for result in self.results:
                if result.status == 'only_in_left':
                    print(f"  {result.relative_path}")
        
        if stats['only_right'] > 0:
            print(f"\n=== FILES ONLY IN RIGHT FOLDER ({self.right_folder}) ===")
            for result in self.results:
                if result.status == 'only_in_right':
                    print(f"  {result.relative_path}")
    
    def _calculate_stats(self) -> Dict[str, int]:
        """Calculate statistics from results"""
        stats = {
            'total': len(self.results),
            'identical': 0,
            'different': 0,
            'only_left': 0,
            'only_right': 0,
            'errors': 0
        }
        
        for result in self.results:
            if result.status == 'identical':
                stats['identical'] += 1
            elif result.status == 'different':
                stats['different'] += 1
            elif result.status == 'only_in_left':
                stats['only_left'] += 1
            elif result.status == 'only_in_right':
                stats['only_right'] += 1
            elif result.status == 'error':
                stats['errors'] += 1
        
        return stats
