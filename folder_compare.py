#!/usr/bin/env python3
"""
Folder Comparison Tool - GUI Version

A comprehensive Python application with GUI that recursively compares two folders
and identifies files that are different, missing, or added.

Features:
- User-friendly GUI interface
- Recursive folder comparison
- Multiple comparison modes (content, size, timestamp)
- Real-time progress tracking
- Detailed results display with filtering
- Export results to various formats (JSON, CSV, HTML)
- Configurable ignore patterns
"""

import os
import sys
import hashlib
import json
import csv
import time
import threading
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

# GUI imports
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from tkinter.ttk import Progressbar


@dataclass
class FileInfo:
    """Information about a file"""
    path: str
    size: int
    modified_time: float
    hash_md5: Optional[str] = None
    exists: bool = True


@dataclass
class ComparisonResult:
    """Result of comparing two files or directories"""
    relative_path: str
    status: str  # 'identical', 'different', 'only_in_left', 'only_in_right', 'error'
    left_file: Optional[FileInfo] = None
    right_file: Optional[FileInfo] = None
    difference_type: Optional[str] = None  # 'content', 'size', 'timestamp'
    error_message: Optional[str] = None


class FolderComparator:
    """Main class for comparing folders"""
    
    def __init__(self, left_folder: str, right_folder: str, 
                 compare_content: bool = True, 
                 compare_timestamps: bool = False,
                 ignore_patterns: List[str] = None,
                 progress_callback=None):
        """
        Initialize the folder comparator
        
        Args:
            left_folder: Path to the first folder
            right_folder: Path to the second folder
            compare_content: Whether to compare file contents (slower but more accurate)
            compare_timestamps: Whether to consider timestamp differences
            ignore_patterns: List of patterns to ignore (e.g., ['*.tmp', '__pycache__'])
            progress_callback: Function to call for progress updates
        """
        self.left_folder = Path(left_folder).resolve()
        self.right_folder = Path(right_folder).resolve()
        self.compare_content = compare_content
        self.compare_timestamps = compare_timestamps
        self.ignore_patterns = ignore_patterns or []
        self.progress_callback = progress_callback
        
        # Validate folders exist
        if not self.left_folder.exists():
            raise ValueError(f"Left folder does not exist: {self.left_folder}")
        if not self.right_folder.exists():
            raise ValueError(f"Right folder does not exist: {self.right_folder}")
    
    def _should_ignore(self, path: Path) -> bool:
        """Check if a path should be ignored based on patterns"""
        import fnmatch
        path_str = str(path)
        for pattern in self.ignore_patterns:
            if fnmatch.fnmatch(path_str, pattern) or fnmatch.fnmatch(path.name, pattern):
                return True
        return False
    
    def _get_file_info(self, file_path: Path) -> FileInfo:
        """Get information about a file"""
        try:
            stat = file_path.stat()
            file_info = FileInfo(
                path=str(file_path),
                size=stat.st_size,
                modified_time=stat.st_mtime
            )
            
            # Calculate hash if content comparison is enabled
            if self.compare_content and file_path.is_file():
                file_info.hash_md5 = self._calculate_file_hash(file_path)
            
            return file_info
        except Exception as e:
            return FileInfo(
                path=str(file_path),
                size=0,
                modified_time=0,
                exists=False
            )
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate MD5 hash of a file"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    def _get_all_files(self, folder: Path) -> Dict[str, Path]:
        """Get all files in a folder recursively"""
        files = {}
        try:
            for item in folder.rglob("*"):
                if item.is_file() and not self._should_ignore(item):
                    relative_path = item.relative_to(folder)
                    files[str(relative_path)] = item
        except Exception as e:
            print(f"Error scanning folder {folder}: {e}")
        return files
    
    def compare(self) -> List[ComparisonResult]:
        """Compare the two folders and return results"""
        print(f"Comparing folders:")
        print(f"  Left:  {self.left_folder}")
        print(f"  Right: {self.right_folder}")
        print()
        
        # Get all files from both folders
        print("Scanning folders...")
        left_files = self._get_all_files(self.left_folder)
        right_files = self._get_all_files(self.right_folder)
        
        print(f"Found {len(left_files)} files in left folder")
        print(f"Found {len(right_files)} files in right folder")
        print()
        
        # Get all unique file paths
        all_paths = set(left_files.keys()) | set(right_files.keys())
        results = []
        
        total_files = len(all_paths)
        processed = 0
        
        for relative_path in sorted(all_paths):
            processed += 1
            
            if self.progress_callback:
                self.progress_callback(processed, total_files, relative_path)
            elif processed % 100 == 0 or processed == total_files:
                print(f"Progress: {processed}/{total_files} files processed")
            
            left_path = left_files.get(relative_path)
            right_path = right_files.get(relative_path)
            
            result = self._compare_files(relative_path, left_path, right_path)
            results.append(result)
        
        return results
    
    def _compare_files(self, relative_path: str, 
                      left_path: Optional[Path], 
                      right_path: Optional[Path]) -> ComparisonResult:
        """Compare two specific files"""
        
        # Handle missing files
        if left_path is None:
            right_info = self._get_file_info(right_path)
            return ComparisonResult(
                relative_path=relative_path,
                status='only_in_right',
                right_file=right_info
            )
        
        if right_path is None:
            left_info = self._get_file_info(left_path)
            return ComparisonResult(
                relative_path=relative_path,
                status='only_in_left',
                left_file=left_info
            )
        
        # Both files exist, compare them
        left_info = self._get_file_info(left_path)
        right_info = self._get_file_info(right_path)
        
        # Check if either file couldn't be read
        if not left_info.exists or not right_info.exists:
            return ComparisonResult(
                relative_path=relative_path,
                status='error',
                left_file=left_info,
                right_file=right_info,
                error_message="Could not read one or both files"
            )
        
        # Compare files
        differences = []
        
        # Size comparison
        if left_info.size != right_info.size:
            differences.append('size')
        
        # Content comparison (if enabled and sizes match)
        if self.compare_content and left_info.size == right_info.size:
            if left_info.hash_md5 != right_info.hash_md5:
                differences.append('content')
        
        # Timestamp comparison (if enabled)
        if self.compare_timestamps:
            if abs(left_info.modified_time - right_info.modified_time) > 1:  # 1 second tolerance
                differences.append('timestamp')
        
        if differences:
            return ComparisonResult(
                relative_path=relative_path,
                status='different',
                left_file=left_info,
                right_file=right_info,
                difference_type=', '.join(differences)
            )
        else:
            return ComparisonResult(
                relative_path=relative_path,
                status='identical',
                left_file=left_info,
                right_file=right_info
            )


class ResultsReporter:
    """Class for generating reports from comparison results"""
    
    def __init__(self, results: List[ComparisonResult], 
                 left_folder: str, right_folder: str):
        self.results = results
        self.left_folder = left_folder
        self.right_folder = right_folder
    
    def print_summary(self):
        """Print a summary of the comparison results"""
        stats = self._calculate_stats()
        
        print("=== COMPARISON SUMMARY ===")
        print(f"Total files compared: {stats['total']}")
        print(f"Identical files: {stats['identical']}")
        print(f"Different files: {stats['different']}")
        print(f"Only in left folder: {stats['only_left']}")
        print(f"Only in right folder: {stats['only_right']}")
        print(f"Errors: {stats['errors']}")
        print()
        
        if stats['different'] > 0:
            print("=== FILES WITH DIFFERENCES ===")
            for result in self.results:
                if result.status == 'different':
                    print(f"  {result.relative_path} ({result.difference_type})")
        
        if stats['only_left'] > 0:
            print(f"\n=== FILES ONLY IN LEFT FOLDER ({self.left_folder}) ===")
            for result in self.results:
                if result.status == 'only_in_left':
                    print(f"  {result.relative_path}")
        
        if stats['only_right'] > 0:
            print(f"\n=== FILES ONLY IN RIGHT FOLDER ({self.right_folder}) ===")
            for result in self.results:
                if result.status == 'only_in_right':
                    print(f"  {result.relative_path}")
    
    def _calculate_stats(self) -> Dict[str, int]:
        """Calculate statistics from results"""
        stats = {
            'total': len(self.results),
            'identical': 0,
            'different': 0,
            'only_left': 0,
            'only_right': 0,
            'errors': 0
        }
        
        for result in self.results:
            if result.status == 'identical':
                stats['identical'] += 1
            elif result.status == 'different':
                stats['different'] += 1
            elif result.status == 'only_in_left':
                stats['only_left'] += 1
            elif result.status == 'only_in_right':
                stats['only_right'] += 1
            elif result.status == 'error':
                stats['errors'] += 1
        
        return stats

    def export_to_json(self, filename: str):
        """Export results to JSON file"""
        data = {
            'comparison_info': {
                'left_folder': self.left_folder,
                'right_folder': self.right_folder,
                'timestamp': datetime.now().isoformat(),
                'statistics': self._calculate_stats()
            },
            'results': [asdict(result) for result in self.results]
        }

        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)

    def export_to_csv(self, filename: str):
        """Export results to CSV file"""
        with open(filename, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['File Path', 'Status', 'Difference Type', 'Left Size', 'Right Size', 'Left Modified', 'Right Modified'])

            for result in self.results:
                left_size = result.left_file.size if result.left_file else ''
                right_size = result.right_file.size if result.right_file else ''
                left_modified = datetime.fromtimestamp(result.left_file.modified_time).isoformat() if result.left_file else ''
                right_modified = datetime.fromtimestamp(result.right_file.modified_time).isoformat() if result.right_file else ''

                writer.writerow([
                    result.relative_path,
                    result.status,
                    result.difference_type or '',
                    left_size,
                    right_size,
                    left_modified,
                    right_modified
                ])


class FolderCompareGUI:
    """GUI application for folder comparison"""

    def __init__(self, root):
        self.root = root
        self.root.title("Folder Comparison Tool")
        self.root.geometry("1000x700")

        # Variables
        self.left_folder_var = tk.StringVar()
        self.right_folder_var = tk.StringVar()
        self.compare_content_var = tk.BooleanVar(value=True)
        self.compare_timestamps_var = tk.BooleanVar(value=False)
        self.ignore_patterns_var = tk.StringVar(value="*.tmp,__pycache__,*.pyc,.git")

        # Results
        self.comparison_results = []
        self.comparator = None
        self.is_comparing = False

        self.setup_ui()

    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Folder selection section
        self.setup_folder_selection(main_frame)

        # Options section
        self.setup_options(main_frame)

        # Control buttons
        self.setup_controls(main_frame)

        # Progress section
        self.setup_progress(main_frame)

        # Results section
        self.setup_results(main_frame)

    def setup_folder_selection(self, parent):
        """Setup folder selection widgets"""
        # Left folder
        ttk.Label(parent, text="Left Folder:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(parent, textvariable=self.left_folder_var, width=60).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(parent, text="Browse", command=self.browse_left_folder).grid(row=0, column=2, padx=(0, 5), pady=2)

        # Right folder
        ttk.Label(parent, text="Right Folder:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(parent, textvariable=self.right_folder_var, width=60).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=2)
        ttk.Button(parent, text="Browse", command=self.browse_right_folder).grid(row=1, column=2, padx=(0, 5), pady=2)

    def setup_options(self, parent):
        """Setup options section"""
        options_frame = ttk.LabelFrame(parent, text="Comparison Options", padding="5")
        options_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        options_frame.columnconfigure(1, weight=1)

        # Checkboxes
        ttk.Checkbutton(options_frame, text="Compare file content (slower but more accurate)",
                       variable=self.compare_content_var).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=2)

        ttk.Checkbutton(options_frame, text="Compare timestamps",
                       variable=self.compare_timestamps_var).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=2)

        # Ignore patterns
        ttk.Label(options_frame, text="Ignore patterns:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(options_frame, textvariable=self.ignore_patterns_var, width=50).grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(5, 0), pady=2)

    def setup_controls(self, parent):
        """Setup control buttons"""
        controls_frame = ttk.Frame(parent)
        controls_frame.grid(row=3, column=0, columnspan=3, pady=10)

        self.compare_button = ttk.Button(controls_frame, text="Start Comparison", command=self.start_comparison)
        self.compare_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(controls_frame, text="Stop", command=self.stop_comparison, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        ttk.Button(controls_frame, text="Export to JSON", command=self.export_json).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="Export to CSV", command=self.export_csv).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="Clear Results", command=self.clear_results).pack(side=tk.LEFT, padx=5)

    def setup_progress(self, parent):
        """Setup progress section"""
        progress_frame = ttk.LabelFrame(parent, text="Progress", padding="5")
        progress_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        progress_frame.columnconfigure(0, weight=1)

        self.progress_var = tk.StringVar(value="Ready to compare folders")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=0, sticky=tk.W)

        self.progress_bar = Progressbar(progress_frame, mode='determinate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)

    def setup_results(self, parent):
        """Setup results section"""
        results_frame = ttk.LabelFrame(parent, text="Results", padding="5")
        results_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(1, weight=1)
        parent.rowconfigure(5, weight=1)

        # Filter frame
        filter_frame = ttk.Frame(results_frame)
        filter_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        ttk.Label(filter_frame, text="Filter:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar()
        filter_combo = ttk.Combobox(filter_frame, textvariable=self.filter_var,
                                   values=['All', 'Different', 'Only in Left', 'Only in Right', 'Identical', 'Errors'])
        filter_combo.set('All')
        filter_combo.pack(side=tk.LEFT, padx=5)

        # Results tree
        columns = ('Path', 'Status', 'Difference', 'Left Size', 'Right Size')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)

        # Configure columns
        self.results_tree.heading('Path', text='File Path')
        self.results_tree.heading('Status', text='Status')
        self.results_tree.heading('Difference', text='Difference Type')
        self.results_tree.heading('Left Size', text='Left Size')
        self.results_tree.heading('Right Size', text='Right Size')

        self.results_tree.column('Path', width=400)
        self.results_tree.column('Status', width=100)
        self.results_tree.column('Difference', width=120)
        self.results_tree.column('Left Size', width=80)
        self.results_tree.column('Right Size', width=80)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        h_scrollbar = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid layout for tree and scrollbars
        self.results_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=2, column=0, sticky=(tk.W, tk.E))

        # Set up filter callback after tree is created
        self.filter_var.trace('w', self.filter_results)

    def browse_left_folder(self):
        """Browse for left folder"""
        folder = filedialog.askdirectory(title="Select Left Folder")
        if folder:
            self.left_folder_var.set(folder)

    def browse_right_folder(self):
        """Browse for right folder"""
        folder = filedialog.askdirectory(title="Select Right Folder")
        if folder:
            self.right_folder_var.set(folder)

    def start_comparison(self):
        """Start the folder comparison in a separate thread"""
        left_folder = self.left_folder_var.get().strip()
        right_folder = self.right_folder_var.get().strip()

        if not left_folder or not right_folder:
            messagebox.showerror("Error", "Please select both folders to compare")
            return

        if not os.path.exists(left_folder):
            messagebox.showerror("Error", f"Left folder does not exist: {left_folder}")
            return

        if not os.path.exists(right_folder):
            messagebox.showerror("Error", f"Right folder does not exist: {right_folder}")
            return

        # Clear previous results
        self.clear_results()

        # Prepare ignore patterns
        ignore_patterns = [p.strip() for p in self.ignore_patterns_var.get().split(',') if p.strip()]

        # Create comparator
        try:
            self.comparator = FolderComparator(
                left_folder=left_folder,
                right_folder=right_folder,
                compare_content=self.compare_content_var.get(),
                compare_timestamps=self.compare_timestamps_var.get(),
                ignore_patterns=ignore_patterns,
                progress_callback=self.update_progress
            )
        except Exception as e:
            messagebox.showerror("Error", f"Failed to initialize comparison: {str(e)}")
            return

        # Update UI state
        self.is_comparing = True
        self.compare_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_var.set("Starting comparison...")
        self.progress_bar['value'] = 0

        # Start comparison in separate thread
        self.comparison_thread = threading.Thread(target=self.run_comparison)
        self.comparison_thread.daemon = True
        self.comparison_thread.start()

    def run_comparison(self):
        """Run the comparison (called in separate thread)"""
        try:
            self.comparison_results = self.comparator.compare()

            # Update UI in main thread
            self.root.after(0, self.comparison_completed)

        except Exception as e:
            # Handle errors in main thread
            self.root.after(0, lambda: self.comparison_error(str(e)))

    def comparison_completed(self):
        """Called when comparison is completed successfully"""
        self.is_comparing = False
        self.compare_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)

        # Update progress
        self.progress_var.set(f"Comparison completed. Found {len(self.comparison_results)} files.")
        self.progress_bar['value'] = 100

        # Populate results
        self.populate_results()

        # Show summary
        self.show_summary()

    def comparison_error(self, error_message):
        """Called when comparison encounters an error"""
        self.is_comparing = False
        self.compare_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_var.set("Comparison failed")
        messagebox.showerror("Comparison Error", f"An error occurred during comparison:\n{error_message}")

    def stop_comparison(self):
        """Stop the ongoing comparison"""
        self.is_comparing = False
        self.compare_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_var.set("Comparison stopped by user")

    def update_progress(self, current, total, current_file):
        """Update progress (called from comparison thread)"""
        if not self.is_comparing:
            return

        progress_percent = (current / total) * 100 if total > 0 else 0

        # Update UI in main thread
        self.root.after(0, lambda: self._update_progress_ui(progress_percent, current, total, current_file))

    def _update_progress_ui(self, progress_percent, current, total, current_file):
        """Update progress UI elements"""
        self.progress_bar['value'] = progress_percent
        filename = os.path.basename(current_file) if current_file else ""
        self.progress_var.set(f"Processing {current}/{total}: {filename}")

    def populate_results(self):
        """Populate the results tree with comparison data"""
        # Clear existing items
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # Add results
        for result in self.comparison_results:
            left_size = self.format_size(result.left_file.size) if result.left_file else ""
            right_size = self.format_size(result.right_file.size) if result.right_file else ""

            # Color coding based on status
            tags = []
            if result.status == 'different':
                tags = ['different']
            elif result.status == 'only_in_left':
                tags = ['only_left']
            elif result.status == 'only_in_right':
                tags = ['only_right']
            elif result.status == 'error':
                tags = ['error']

            self.results_tree.insert('', 'end', values=(
                result.relative_path,
                result.status.replace('_', ' ').title(),
                result.difference_type or '',
                left_size,
                right_size
            ), tags=tags)

        # Configure tags for color coding
        self.results_tree.tag_configure('different', background='#ffeeee')
        self.results_tree.tag_configure('only_left', background='#eeeeff')
        self.results_tree.tag_configure('only_right', background='#eeffee')
        self.results_tree.tag_configure('error', background='#ffcccc')

    def filter_results(self, *args):
        """Filter results based on selected filter"""
        filter_value = self.filter_var.get()

        # Clear current items
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # Filter and add items
        for result in self.comparison_results:
            show_item = False

            if filter_value == 'All':
                show_item = True
            elif filter_value == 'Different' and result.status == 'different':
                show_item = True
            elif filter_value == 'Only in Left' and result.status == 'only_in_left':
                show_item = True
            elif filter_value == 'Only in Right' and result.status == 'only_in_right':
                show_item = True
            elif filter_value == 'Identical' and result.status == 'identical':
                show_item = True
            elif filter_value == 'Errors' and result.status == 'error':
                show_item = True

            if show_item:
                left_size = self.format_size(result.left_file.size) if result.left_file else ""
                right_size = self.format_size(result.right_file.size) if result.right_file else ""

                tags = []
                if result.status == 'different':
                    tags = ['different']
                elif result.status == 'only_in_left':
                    tags = ['only_left']
                elif result.status == 'only_in_right':
                    tags = ['only_right']
                elif result.status == 'error':
                    tags = ['error']

                self.results_tree.insert('', 'end', values=(
                    result.relative_path,
                    result.status.replace('_', ' ').title(),
                    result.difference_type or '',
                    left_size,
                    right_size
                ), tags=tags)

    def format_size(self, size_bytes):
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"

    def show_summary(self):
        """Show comparison summary in a popup"""
        if not self.comparison_results:
            return

        reporter = ResultsReporter(
            self.comparison_results,
            self.left_folder_var.get(),
            self.right_folder_var.get()
        )
        stats = reporter._calculate_stats()

        summary_text = f"""Comparison Summary:

Total files: {stats['total']}
Identical files: {stats['identical']}
Different files: {stats['different']}
Only in left folder: {stats['only_left']}
Only in right folder: {stats['only_right']}
Errors: {stats['errors']}

Left folder: {self.left_folder_var.get()}
Right folder: {self.right_folder_var.get()}"""

        messagebox.showinfo("Comparison Summary", summary_text)

    def export_json(self):
        """Export results to JSON file"""
        if not self.comparison_results:
            messagebox.showwarning("No Data", "No comparison results to export")
            return

        filename = filedialog.asksaveasfilename(
            title="Export to JSON",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                reporter = ResultsReporter(
                    self.comparison_results,
                    self.left_folder_var.get(),
                    self.right_folder_var.get()
                )
                reporter.export_to_json(filename)
                messagebox.showinfo("Export Successful", f"Results exported to {filename}")
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export: {str(e)}")

    def export_csv(self):
        """Export results to CSV file"""
        if not self.comparison_results:
            messagebox.showwarning("No Data", "No comparison results to export")
            return

        filename = filedialog.asksaveasfilename(
            title="Export to CSV",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if filename:
            try:
                reporter = ResultsReporter(
                    self.comparison_results,
                    self.left_folder_var.get(),
                    self.right_folder_var.get()
                )
                reporter.export_to_csv(filename)
                messagebox.showinfo("Export Successful", f"Results exported to {filename}")
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export: {str(e)}")

    def clear_results(self):
        """Clear all results"""
        self.comparison_results = []
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self.progress_var.set("Ready to compare folders")
        self.progress_bar['value'] = 0


def main():
    """Main function to run the GUI application"""
    root = tk.Tk()
    app = FolderCompareGUI(root)

    # Center the window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    root.mainloop()


if __name__ == "__main__":
    main()
