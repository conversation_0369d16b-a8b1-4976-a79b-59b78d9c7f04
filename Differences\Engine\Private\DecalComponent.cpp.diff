--- Left: DecalComponent.cpp
+++ Right: DecalComponent.cpp
@@ -17,10 +17,10 @@
 void UDecalComponent::SetFadeScreenSize(float NewFadeScreenSize) {

 }

 

-void UDecalComponent::SetFadeOut(float StartDelay, float duration, bool DestroyOwnerAfterFade) {

+void UDecalComponent::SetFadeOut(float StartDelay, float Duration, bool DestroyOwnerAfterFade) {

 }

 

-void UDecalComponent::SetFadeIn(float StartDelay, float duration) {

+void UDecalComponent::SetFadeIn(float StartDelay, float Duration) {

 }

 

 void UDecalComponent::SetDecalMaterial(UMaterialInterface* NewDecalMaterial) {
