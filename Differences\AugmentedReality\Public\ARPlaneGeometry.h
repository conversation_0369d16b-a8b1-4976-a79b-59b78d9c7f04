DIFFERENCES IN: AugmentedReality\Public\ARPlaneGeometry.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AugmentedReality\Public\ARPlaneGeometry.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AugmentedReality\Public\ARPlaneGeometry.h
============================================================

SECTION starting at line 13 (left) / 13 (right):
----------------------------------------
  CONTEXT (line 13): public:
  CONTEXT (line 14): private:
  CONTEXT (line 15):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 16):     EARPlaneOrientation orientation;
  ADDED   (line 16):     EARPlaneOrientation Orientation;
  CONTEXT (line 17): 
  CONTEXT (line 18):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 19):     FVector Center;