DIFFERENCES IN: Engine\Private\RendererSettings.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\RendererSettings.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\RendererSettings.cpp
============================================================

SECTION starting at line 7 (left) / 7 (right):
----------------------------------------
  CONTEXT (line 7):     this->MobileAntiAliasing = EMobileAntiAliasingMethod::None;
  CONTEXT (line 8):     this->MobileFloatPrecisionMode = EMobileFloatPrecisionMode::Half;
  CONTEXT (line 9):     this->bMobileAllowDitheredLODTransition = false;
  REMOVED (line 10):     this->bMobileAllowCustomOcclusionCulling = false;
  REMOVED (line 11):     this->bTestDeferredAllowCustomOcclusionCulling = false;
  CONTEXT (line 12):     this->bMobileVirtualTextures = false;
  CONTEXT (line 13):     this->bDiscardUnusedQualityLevels = false;
  CONTEXT (line 14):     this->ShaderCompressionFormat = EShaderCompressionFormat::None;

SECTION starting at line 132 (left) / 130 (right):
----------------------------------------
  CONTEXT (line 132):     this->bMobileMultiView = false;
  CONTEXT (line 133):     this->bMobileUseHWsRGBEncoding = false;
  CONTEXT (line 134):     this->bRoundRobinOcclusion = false;
  REMOVED (line 135):     this->bMobileSupportSpaceWarp = false;
  REMOVED (line 136):     this->bSupportsXRSoftOcclusions = false;
  REMOVED (line 137):     this->bVulkanUseEmulatedUBs = false;
  CONTEXT (line 138):     this->bMeshStreaming = false;
  CONTEXT (line 139):     this->bEnableHeterogeneousVolumes = false;
  CONTEXT (line 140):     this->bShouldHeterogeneousVolumesCastShadows = false;

SECTION starting at line 162 (left) / 157 (right):
----------------------------------------
  CONTEXT (line 162):     this->bMobileEnableStaticAndCSMShadowReceivers = false;
  CONTEXT (line 163):     this->bMobileEnableMovableLightCSMShaderCulling = false;
  CONTEXT (line 164):     this->MobileLocalLightSetting = LOCAL_LIGHTS_DISABLED;
  REMOVED (line 165):     this->bMobilePackLightGridLightDataToUBO = false;
  REMOVED (line 166):     this->bMobileUniformLocalLights = false;
  CONTEXT (line 167):     this->bMobileForwardEnableClusteredReflections = false;
  CONTEXT (line 168):     this->bMobileAllowDistanceFieldShadows = false;
  CONTEXT (line 169):     this->bMobileAllowMovableSpotlightShadows = false;