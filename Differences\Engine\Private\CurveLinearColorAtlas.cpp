DIFFERENCES IN: Engine\Private\CurveLinearColorAtlas.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\CurveLinearColorAtlas.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\CurveLinearColorAtlas.cpp
============================================================

SECTION starting at line 11 (left) / 11 (right):
----------------------------------------
  CONTEXT (line 11):     this->TextureHeight = 256;
  CONTEXT (line 12): }
  CONTEXT (line 13): 
  REMOVED (line 14): bool UCurveLinearColorAtlas::GetCurvePosition(UCurveLinearColor* InCurve, float& Position) {
  ADDED   (line 14): bool UCurveLinearColorAtlas::GetCurvePosition(UCurveLinearColor* InCurve, float& position) {
  CONTEXT (line 15):     return false;
  CONTEXT (line 16): }
  CONTEXT (line 17): 