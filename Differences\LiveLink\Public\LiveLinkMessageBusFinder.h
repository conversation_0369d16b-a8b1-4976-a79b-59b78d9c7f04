DIFFERENCES IN: LiveLink\Public\LiveLinkMessageBusFinder.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\LiveLink\Public\LiveLinkMessageBusFinder.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\LiveLink\Public\LiveLinkMessageBusFinder.h
============================================================

SECTION starting at line 15 (left) / 15 (right):
----------------------------------------
  CONTEXT (line 15):     ULiveLinkMessageBusFinder();
  CONTEXT (line 16): 
  CONTEXT (line 17):     UFUNCTION(BlueprintCallable, meta=(Latent, LatentInfo="LatentInfo", WorldContext="WorldContextObject"))
  REMOVED (line 18):     void GetAvailableProviders(UObject* WorldContextObject, FLatentActionInfo LatentInfo, float duration, TArray<FProviderPollResult>& AvailableProviders);
  ADDED   (line 18):     void GetAvailableProviders(UObject* WorldContextObject, FLatentActionInfo LatentInfo, float Duration, TArray<FProviderPollResult>& AvailableProviders);
  CONTEXT (line 19): 
  CONTEXT (line 20):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 21):     static ULiveLinkMessageBusFinder* ConstructMessageBusFinder();