--- Left: CollectionTransformSelectionByFloatAttrDataflowNode.h
+++ Right: CollectionTransformSelectionByFloatAttrDataflowNode.h
@@ -20,10 +20,10 @@
     FString AttrName;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float Min;

+    float min;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float Max;

+    float max;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     ERangeSettingEnum RangeSetting;
