--- Left: InputDeviceProperty.cpp
+++ Right: InputDeviceProperty.cpp
@@ -7,7 +7,7 @@
 void UInputDeviceProperty::ResetDeviceProperty_Implementation(const FPlatformUserId PlatformUser, const FInputDeviceId DeviceID, bool bForceReset) {

 }

 

-void UInputDeviceProperty::EvaluateDeviceProperty_Implementation(const FPlatformUserId PlatformUser, const FInputDeviceId DeviceID, const float DeltaTime, const float duration) {

+void UInputDeviceProperty::EvaluateDeviceProperty_Implementation(const FPlatformUserId PlatformUser, const FInputDeviceId DeviceID, const float DeltaTime, const float Duration) {

 }

 

 void UInputDeviceProperty::ApplyDeviceProperty(const FPlatformUserId UserId, const FInputDeviceId DeviceID) {
