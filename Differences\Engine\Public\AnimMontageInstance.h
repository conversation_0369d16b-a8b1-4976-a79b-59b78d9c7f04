DIFFERENCES IN: Engine\Public\AnimMontageInstance.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\AnimMontageInstance.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\AnimMontageInstance.h
============================================================

SECTION starting at line 30 (left) / 30 (right):
----------------------------------------
  CONTEXT (line 30):     TArray<FAnimNotifyEvent> ActiveStateBranchingPoints;
  CONTEXT (line 31): 
  CONTEXT (line 32):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 33):     float Position;
  ADDED   (line 33):     float position;
  CONTEXT (line 34): 
  CONTEXT (line 35):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 36):     float PlayRate;