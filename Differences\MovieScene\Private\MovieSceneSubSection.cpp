DIFFERENCES IN: MovieScene\Private\MovieSceneSubSection.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MovieScene\Private\MovieSceneSubSection.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MovieScene\Private\MovieSceneSubSection.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): UMovieSceneSubSection::UMovieSceneSubSection() {
  CONTEXT (line 4):     this->StartOffset = -340282346638528859811704183484516925440.00f;
  REMOVED (line 5):     this->timescale = -340282346638528859811704183484516925440.00f;
  ADDED   (line 5):     this->TimeScale = -340282346638528859811704183484516925440.00f;
  CONTEXT (line 6):     const FProperty* p_PrerollTime = GetClass()->FindPropertyByName("PrerollTime");
  CONTEXT (line 7):     (*p_PrerollTime->ContainerPtrToValuePtr<float>(this)) = -340282346638528859811704183484516925440.00f;
  CONTEXT (line 8):     this->NetworkMask = 3;