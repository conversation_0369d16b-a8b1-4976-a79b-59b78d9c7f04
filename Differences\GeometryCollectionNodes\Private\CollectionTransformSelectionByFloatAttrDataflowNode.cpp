DIFFERENCES IN: GeometryCollectionNodes\Private\CollectionTransformSelectionByFloatAttrDataflowNode.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCollectionNodes\Private\CollectionTransformSelectionByFloatAttrDataflowNode.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCollectionNodes\Private\CollectionTransformSelectionByFloatAttrDataflowNode.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "CollectionTransformSelectionByFloatAttrDataflowNode.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): FCollectionTransformSelectionByFloatAttrDataflowNode::FCollectionTransformSelectionByFloatAttrDataflowNode() {
  REMOVED (line 4):     this->Min = 0.00f;
  REMOVED (line 5):     this->Max = 0.00f;
  ADDED   (line 4):     this->min = 0.00f;
  ADDED   (line 5):     this->max = 0.00f;
  CONTEXT (line 6):     this->RangeSetting = ERangeSettingEnum::Dataflow_RangeSetting_InsideRange;
  CONTEXT (line 7):     this->bInclusive = false;
  CONTEXT (line 8): }