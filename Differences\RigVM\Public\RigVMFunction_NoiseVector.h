DIFFERENCES IN: RigVM\Public\RigVMFunction_NoiseVector.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\RigVM\Public\RigVMFunction_NoiseVector.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\RigVM\Public\RigVMFunction_NoiseVector.h
============================================================

SECTION starting at line 9 (left) / 9 (right):
----------------------------------------
  CONTEXT (line 9):     GENERATED_BODY()
  CONTEXT (line 10): public:
  CONTEXT (line 11):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 12):     FVector Position;
  ADDED   (line 12):     FVector position;
  CONTEXT (line 13): 
  CONTEXT (line 14):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 15):     FVector Speed;