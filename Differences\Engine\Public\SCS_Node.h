DIFFERENCES IN: Engine\Public\SCS_Node.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\SCS_Node.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\SCS_Node.h
============================================================

SECTION starting at line 41 (left) / 41 (right):
----------------------------------------
  CONTEXT (line 41):     TArray<FBPVariableMetaDataEntry> MetaDataArray;
  CONTEXT (line 42): 
  CONTEXT (line 43):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 44):     FGuid VariableGUID;
  ADDED   (line 44):     FGuid VariableGuid;
  CONTEXT (line 45): 
  CONTEXT (line 46): private:
  CONTEXT (line 47):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))