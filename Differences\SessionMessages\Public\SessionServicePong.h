DIFFERENCES IN: SessionMessages\Public\SessionServicePong.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\SessionMessages\Public\SessionServicePong.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\SessionMessages\Public\SessionServicePong.h
============================================================

SECTION starting at line 17 (left) / 17 (right):
----------------------------------------
  CONTEXT (line 17):     FString DeviceName;
  CONTEXT (line 18): 
  CONTEXT (line 19):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 20):     FGuid InstanceID;
  ADDED   (line 20):     FGuid InstanceId;
  CONTEXT (line 21): 
  CONTEXT (line 22):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 23):     FString InstanceName;