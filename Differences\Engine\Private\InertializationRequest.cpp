DIFFERENCES IN: Engine\Private\InertializationRequest.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\InertializationRequest.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\InertializationRequest.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "InertializationRequest.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): FInertializationRequest::FInertializationRequest() {
  REMOVED (line 4):     this->duration = 0.00f;
  ADDED   (line 4):     this->Duration = 0.00f;
  CONTEXT (line 5):     this->BlendProfile = NULL;
  CONTEXT (line 6):     this->bUseBlendMode = false;
  CONTEXT (line 7):     this->BlendMode = EAlphaBlendOption::Linear;