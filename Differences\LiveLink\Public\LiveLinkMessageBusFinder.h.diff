--- Left: LiveLinkMessageBusFinder.h
+++ Right: LiveLinkMessageBusFinder.h
@@ -15,7 +15,7 @@
     ULiveLinkMessageBusFinder();

 

     UFUNCTION(BlueprintCallable, meta=(Latent, LatentInfo="LatentInfo", WorldContext="WorldContextObject"))

-    void GetAvailableProviders(UObject* WorldContextObject, FLatentActionInfo LatentInfo, float duration, TArray<FProviderPollResult>& AvailableProviders);

+    void GetAvailableProviders(UObject* WorldContextObject, FLatentActionInfo LatentInfo, float Duration, TArray<FProviderPollResult>& AvailableProviders);

     

     UFUNCTION(BlueprintCallable)

     static ULiveLinkMessageBusFinder* ConstructMessageBusFinder();
