DIFFERENCES IN: UMG\Private\UserWidget.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\UMG\Private\UserWidget.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\UMG\Private\UserWidget.cpp
============================================================

SECTION starting at line 47 (left) / 47 (right):
----------------------------------------
  CONTEXT (line 47): void UUserWidget::StopAllAnimations() {
  CONTEXT (line 48): }
  CONTEXT (line 49): 
  REMOVED (line 50): void UUserWidget::SetPositionInViewport(FVector2D Position, bool bRemoveDPIScale) {
  ADDED   (line 50): void UUserWidget::SetPositionInViewport(FVector2D position, bool bRemoveDPIScale) {
  CONTEXT (line 51): }
  CONTEXT (line 52): 
  CONTEXT (line 53): void UUserWidget::SetPlaybackSpeed(const UWidgetAnimation* InAnimation, float PlaybackSpeed) {