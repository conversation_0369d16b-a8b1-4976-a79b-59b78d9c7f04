DIFFERENCES IN: Engine\Private\Material.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\Material.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\Material.cpp
============================================================

SECTION starting at line 32 (left) / 32 (right):
----------------------------------------
  CONTEXT (line 32):     this->bEnableDisplacementFade = false;
  CONTEXT (line 33):     this->TranslucencyPass = MTP_AfterDOF;
  CONTEXT (line 34):     this->TranslucencyLightingMode = TLM_VolumetricNonDirectional;
  REMOVED (line 35):     this->bTranslucencyBackground = false;
  CONTEXT (line 36):     this->bEnableMobileSeparateTranslucency = false;
  CONTEXT (line 37):     this->NumCustomizedUVs = 0;
  CONTEXT (line 38):     this->TranslucencyDirectionalLightingIntensity = 1.00f;

SECTION starting at line 76 (left) / 75 (right):
----------------------------------------
  CONTEXT (line 76):     this->bForceCompatibleWithLightFunctionAtlas = false;
  CONTEXT (line 77):     this->bAutomaticallySetUsageInEditor = true;
  CONTEXT (line 78):     this->bFullyRough = false;
  REMOVED (line 79):     this->bFullyRoughOnMobile = false;
  CONTEXT (line 80):     this->bUseFullPrecision = false;
  CONTEXT (line 81):     this->FloatPrecisionMode = MFPM_Default;
  CONTEXT (line 82):     this->bUseLightmapDirectionality = true;

SECTION starting at line 87 (left) / 85 (right):
----------------------------------------
  CONTEXT (line 87):     this->bForwardBlendsSkyLightCubemaps = false;
  CONTEXT (line 88):     this->bUsePlanarForwardReflections = false;
  CONTEXT (line 89):     this->bNormalCurvatureToRoughness = false;
  REMOVED (line 90):     this->bUseCheckerAsEdgeMaterialId = false;
  CONTEXT (line 91):     this->AllowTranslucentCustomDepthWrites = false;
  CONTEXT (line 92):     this->bAllowFrontLayerTranslucency = true;
  CONTEXT (line 93):     this->Wireframe = false;
  CONTEXT (line 94):     this->ShadingRate = MSR_1x1;
  REMOVED (line 95):     this->ShadingRateOnMobile = MSR_1x1;
  CONTEXT (line 96):     this->bAllowVariableRateShading = true;
  CONTEXT (line 97):     this->bCanMaskedBeAssumedOpaque = false;
  CONTEXT (line 98):     this->bIsMasked = false;

SECTION starting at line 105 (left) / 101 (right):
----------------------------------------
  CONTEXT (line 105):     this->bUseTranslucencyVertexFog = true;
  CONTEXT (line 106):     this->bApplyCloudFogging = false;
  CONTEXT (line 107):     this->bIsSky = false;
  REMOVED (line 108):     this->bXRSoftOcclusions = true;
  REMOVED (line 109):     this->XRSoftOcclusionsDepthBias = 0.00f;
  CONTEXT (line 110):     this->bComputeFogPerPixel = false;
  CONTEXT (line 111):     this->bOutputTranslucentVelocity = false;
  CONTEXT (line 112):     this->bAllowDevelopmentShaderCompile = true;