DIFFERENCES IN: Engine\Public\InputRange.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\InputRange.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\InputRange.h
============================================================

SECTION starting at line 7 (left) / 7 (right):
----------------------------------------
  CONTEXT (line 7):     GENERATED_BODY()
  CONTEXT (line 8): public:
  CONTEXT (line 9):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 10):     float Min;
  ADDED   (line 10):     float min;
  CONTEXT (line 11): 
  CONTEXT (line 12):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 13):     float Max;
  ADDED   (line 13):     float max;
  CONTEXT (line 14): 
  CONTEXT (line 15):     ENGINE_API FInputRange();
  CONTEXT (line 16): };