DIFFERENCES IN: MovieScene\Public\MovieSceneSequencePlayer.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MovieScene\Public\MovieSceneSequencePlayer.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MovieScene\Public\MovieSceneSequencePlayer.h
============================================================

SECTION starting at line 102 (left) / 102 (right):
----------------------------------------
  CONTEXT (line 102):     void SetWeight(double InWeight);
  CONTEXT (line 103): 
  CONTEXT (line 104):     UFUNCTION(BlueprintCallable)
  REMOVED (line 105):     void SetTimeRange(float NewStartTime, float duration);
  ADDED   (line 105):     void SetTimeRange(float NewStartTime, float Duration);
  CONTEXT (line 106): 
  CONTEXT (line 107):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 108):     void SetPlayRate(float PlayRate);

SECTION starting at line 117 (left) / 117 (right):
----------------------------------------
  CONTEXT (line 117):     void SetFrameRate(FFrameRate FrameRate);
  CONTEXT (line 118): 
  CONTEXT (line 119):     UFUNCTION(BlueprintCallable)
  REMOVED (line 120):     void SetFrameRange(int32 StartFrame, int32 duration, float SubFrames);
  ADDED   (line 120):     void SetFrameRange(int32 StartFrame, int32 Duration, float SubFrames);
  CONTEXT (line 121): 
  CONTEXT (line 122):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 123):     void SetDisableCameraCuts(bool bInDisableCameraCuts);