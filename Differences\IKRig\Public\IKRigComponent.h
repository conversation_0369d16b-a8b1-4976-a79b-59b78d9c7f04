DIFFERENCES IN: IKRig\Public\IKRigComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\IKRig\Public\IKRigComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\IKRig\Public\IKRigComponent.h
============================================================

SECTION starting at line 18 (left) / 18 (right):
----------------------------------------
  CONTEXT (line 18):     void SetIKRigGoalTransform(const FName GoalName, const FTransform Transform, const float PositionAlpha, const float RotationAlpha);
  CONTEXT (line 19): 
  CONTEXT (line 20):     UFUNCTION(BlueprintCallable)
  REMOVED (line 21):     void SetIKRigGoalPositionAndRotation(const FName GoalName, const FVector Position, const FQuat Rotation, const float PositionAlpha, const float RotationAlpha);
  ADDED   (line 21):     void SetIKRigGoalPositionAndRotation(const FName GoalName, const FVector position, const FQuat Rotation, const float PositionAlpha, const float RotationAlpha);
  CONTEXT (line 22): 
  CONTEXT (line 23):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 24):     void SetIKRigGoal(const FIKRigGoal& Goal);