--- Left: ScrollBox.h
+++ Right: ScrollBox.h
@@ -35,7 +35,7 @@
     FScrollBarStyle WidgetBarStyle;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    TEnumAsByte<EOrientation> orientation;

+    TEnumAsByte<EOrientation> Orientation;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     ESlateVisibility ScrollBarVisibility;
