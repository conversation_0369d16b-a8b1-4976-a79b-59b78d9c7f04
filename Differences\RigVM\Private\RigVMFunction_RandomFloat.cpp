DIFFERENCES IN: RigVM\Private\RigVMFunction_RandomFloat.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\RigVM\Private\RigVMFunction_RandomFloat.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\RigVM\Private\RigVMFunction_RandomFloat.cpp
============================================================

SECTION starting at line 4 (left) / 4 (right):
----------------------------------------
  CONTEXT (line 4):     this->Seed = 0;
  CONTEXT (line 5):     this->Minimum = 0.00f;
  CONTEXT (line 6):     this->Maximum = 0.00f;
  REMOVED (line 7):     this->duration = 0.00f;
  ADDED   (line 7):     this->Duration = 0.00f;
  CONTEXT (line 8):     this->Result = 0.00f;
  CONTEXT (line 9):     this->LastResult = 0.00f;
  CONTEXT (line 10):     this->LastSeed = 0;