--- Left: HUD.h
+++ Right: HUD.h
@@ -155,10 +155,10 @@
     void Deproject(float ScreenX, float ScreenY, FVector& WorldPosition, FVector& WorldDirection) const;

     

     UFUNCTION(BlueprintCallable)

-    void AddHitBox(FVector2D Position, FVector2D Size, FName InName, bool bConsumesInput, int32 Priority);

+    void AddHitBox(FVector2D position, FVector2D Size, FName InName, bool bConsumesInput, int32 Priority);

     

     UFUNCTION(BlueprintCallable, Client, Reliable)

-    void AddDebugText(const FString& DebugText, AActor* SrcActor, float duration, FVector Offset, FVector DesiredOffset, FColor TextColor, bool bSkipOverwriteCheck, bool bAbsoluteLocation, bool bKeepAttachedToActor, UFont* InFont, float FontScale, bool bDrawShadow);

+    void AddDebugText(const FString& DebugText, AActor* SrcActor, float Duration, FVector Offset, FVector DesiredOffset, FColor TextColor, bool bSkipOverwriteCheck, bool bAbsoluteLocation, bool bKeepAttachedToActor, UFont* InFont, float FontScale, bool bDrawShadow);

     

 };

 
