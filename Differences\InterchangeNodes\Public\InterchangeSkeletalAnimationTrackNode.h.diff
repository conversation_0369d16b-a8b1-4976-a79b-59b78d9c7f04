--- Left: InterchangeSkeletalAnimationTrackNode.h
+++ Right: InterchangeSkeletalAnimationTrackNode.h
@@ -23,10 +23,10 @@
     bool SetCustomAnimationSampleRate(const double& SampleRate);

     

     UFUNCTION(BlueprintCallable)

-    bool SetAnimationPayloadKeyForSceneNodeUid(const FString& SceneNodeUid, const FString& InUniqueID, const EInterchangeAnimationPayLoadType& InType);

+    bool SetAnimationPayloadKeyForSceneNodeUid(const FString& SceneNodeUid, const FString& InUniqueId, const EInterchangeAnimationPayLoadType& InType);

     

     UFUNCTION(BlueprintCallable)

-    bool SetAnimationPayloadKeyForMorphTargetNodeUid(const FString& MorphTargetNodeUid, const FString& InUniqueID, const EInterchangeAnimationPayLoadType& InType);

+    bool SetAnimationPayloadKeyForMorphTargetNodeUid(const FString& MorphTargetNodeUid, const FString& InUniqueId, const EInterchangeAnimationPayLoadType& InType);

     

     UFUNCTION(BlueprintCallable, BlueprintPure)

     bool IsNodeAnimatedWithBakedCurve(const FString& SceneNodeUid) const;
