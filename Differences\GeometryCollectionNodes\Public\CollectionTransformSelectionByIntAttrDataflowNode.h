DIFFERENCES IN: GeometryCollectionNodes\Public\CollectionTransformSelectionByIntAttrDataflowNode.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCollectionNodes\Public\CollectionTransformSelectionByIntAttrDataflowNode.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCollectionNodes\Public\CollectionTransformSelectionByIntAttrDataflowNode.h
============================================================

SECTION starting at line 20 (left) / 20 (right):
----------------------------------------
  CONTEXT (line 20):     FString AttrName;
  CONTEXT (line 21): 
  CONTEXT (line 22):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 23):     int32 Min;
  ADDED   (line 23):     int32 min;
  CONTEXT (line 24): 
  CONTEXT (line 25):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 26):     int32 Max;
  ADDED   (line 26):     int32 max;
  CONTEXT (line 27): 
  CONTEXT (line 28):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 29):     ERangeSettingEnum RangeSetting;