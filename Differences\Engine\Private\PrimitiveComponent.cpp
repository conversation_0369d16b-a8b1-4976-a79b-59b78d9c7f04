DIFFERENCES IN: Engine\Private\PrimitiveComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\PrimitiveComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\PrimitiveComponent.cpp
============================================================

SECTION starting at line 27 (left) / 27 (right):
----------------------------------------
  CONTEXT (line 27):     this->bVisibleInRayTracing = true;
  CONTEXT (line 28):     this->bRenderInMainPass = true;
  CONTEXT (line 29):     this->bRenderInDepthPass = true;
  REMOVED (line 30):     this->bReceivesDecals = false;
  ADDED   (line 30):     this->bReceivesDecals = true;
  CONTEXT (line 31):     this->bHoldout = false;
  CONTEXT (line 32):     this->bOwnerNoSee = false;
  CONTEXT (line 33):     this->bOnlyOwnerSee = false;

SECTION starting at line 37 (left) / 37 (right):
----------------------------------------
  CONTEXT (line 37):     this->bWantsEditorEffects = false;
  CONTEXT (line 38):     this->bForceMipStreaming = false;
  CONTEXT (line 39):     this->bHasPerInstanceHitProxies = false;
  REMOVED (line 40):     this->bShouldBeExcludedFromLightmaps = false;
  CONTEXT (line 41):     this->CastShadow = false;
  CONTEXT (line 42):     this->bEmissiveLightSource = false;
  CONTEXT (line 43):     this->bAffectDynamicIndirectLighting = true;

SECTION starting at line 204 (left) / 203 (right):
----------------------------------------
  CONTEXT (line 204): void UPrimitiveComponent::SetLinearDamping(float InDamping) {
  CONTEXT (line 205): }
  CONTEXT (line 206): 
  REMOVED (line 207): void UPrimitiveComponent::SetLightingChannels(bool bChannel0, bool bChannel1) {
  ADDED   (line 206): void UPrimitiveComponent::SetLightingChannels(bool bChannel0, bool bChannel1, bool bChannel2) {
  CONTEXT (line 208): }
  CONTEXT (line 209): 
  CONTEXT (line 210): void UPrimitiveComponent::SetLightAttachmentsAsGroup(bool bInLightAttachmentsAsGroup) {