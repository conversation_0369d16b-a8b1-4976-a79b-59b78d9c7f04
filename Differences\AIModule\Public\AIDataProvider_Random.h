DIFFERENCES IN: AIModule\Public\AIDataProvider_Random.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AIModule\Public\AIDataProvider_Random.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AIModule\Public\AIDataProvider_Random.h
============================================================

SECTION starting at line 9 (left) / 9 (right):
----------------------------------------
  CONTEXT (line 9): public:
  CONTEXT (line 10): protected:
  CONTEXT (line 11):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 12):     float Min;
  ADDED   (line 12):     float min;
  CONTEXT (line 13): 
  CONTEXT (line 14):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 15):     float Max;
  ADDED   (line 15):     float max;
  CONTEXT (line 16): 
  CONTEXT (line 17):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 18):     uint8 bInteger: 1;