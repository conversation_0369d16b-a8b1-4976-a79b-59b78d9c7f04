DIFFERENCES IN: XRBase\Public\HeadMountedDisplayFunctionLibrary.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\XRBase\Public\HeadMountedDisplayFunctionLibrary.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\XRBase\Public\HeadMountedDisplayFunctionLibrary.h
============================================================

SECTION starting at line 141 (left) / 141 (right):
----------------------------------------
  CONTEXT (line 141):     static void GetHandTrackingState(UObject* WorldContext, const EXRSpaceType XRSpaceType, const EControllerHand Hand, FXRHandTrackingState& HandTrackingState);
  CONTEXT (line 142): 
  CONTEXT (line 143):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContext"))
  REMOVED (line 144):     static void GetDeviceWorldPose(UObject* WorldContext, const FXRDeviceId& XRDeviceId, bool& bIsTracked, FRotator& orientation, bool& bHasPositionalTracking, FVector& Position);
  ADDED   (line 144):     static void GetDeviceWorldPose(UObject* WorldContext, const FXRDeviceId& XRDeviceId, bool& bIsTracked, FRotator& Orientation, bool& bHasPositionalTracking, FVector& position);
  CONTEXT (line 145): 
  CONTEXT (line 146):     UFUNCTION(BlueprintCallable)
  REMOVED (line 147):     static void GetDevicePose(const FXRDeviceId& XRDeviceId, bool& bIsTracked, FRotator& orientation, bool& bHasPositionalTracking, FVector& Position);
  ADDED   (line 147):     static void GetDevicePose(const FXRDeviceId& XRDeviceId, bool& bIsTracked, FRotator& Orientation, bool& bHasPositionalTracking, FVector& position);
  CONTEXT (line 148): 
  CONTEXT (line 149):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 150):     static bool GetCurrentInteractionProfile(const EControllerHand Hand, FString& InteractionProfile);
  CONTEXT (line 151): 
  CONTEXT (line 152):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContext"))
  REMOVED (line 153):     static bool GetControllerTransformForTime2(UObject* WorldContext, const int32 ControllerIndex, const FName MotionSource, FTimespan Time, bool& bTimeWasUsed, FRotator& orientation, FVector& Position, bool& bProvidedLinearVelocity, FVector& LinearVelocity, bool& bProvidedAngularVelocity, FRotator& AngularVelocity, bool& bProvidedLinearAcceleration, FVector& LinearAcceleration);
  ADDED   (line 153):     static bool GetControllerTransformForTime2(UObject* WorldContext, const int32 ControllerIndex, const FName MotionSource, FTimespan Time, bool& bTimeWasUsed, FRotator& Orientation, FVector& position, bool& bProvidedLinearVelocity, FVector& LinearVelocity, bool& bProvidedAngularVelocity, FRotator& AngularVelocity, bool& bProvidedLinearAcceleration, FVector& LinearAcceleration);
  CONTEXT (line 154): 
  CONTEXT (line 155):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 156):     static TArray<FXRDeviceId> EnumerateTrackedDevices(const FName SystemId, EXRTrackedDeviceType DeviceType);