--- Left: GameplayTagsSettings.cpp
+++ Right: GameplayTagsSettings.cpp
@@ -1,18 +1,15 @@
 #include "GameplayTagsSettings.h"

 

 UGameplayTagsSettings::UGameplayTagsSettings() {

-    this->ConfigFileName = TEXT("../../../IntoTheRadius2/Config/DefaultGameplayTags.ini");

-    this->GameplayTagList.AddDefaulted(2185);

+    this->ConfigFileName = TEXT("../../../CV_5_5_4/Config/DefaultGameplayTags.ini");

     this->ImportTagsFromConfig = true;

     this->WarnOnInvalidTags = true;

     this->ClearInvalidTags = false;

     this->AllowEditorTagUnloading = true;

     this->AllowGameTagUnloading = false;

-    this->FastReplication = true;

+    this->FastReplication = false;

     this->bDynamicReplication = false;

     this->InvalidTagCharacters = TEXT("\"',");

-    this->GameplayTagTableList.AddDefaulted(4);

-    this->GameplayTagRedirects.AddDefaulted(329);

     this->NumBitsForContainerSize = 6;

     this->NetIndexFirstBitSegment = 16;

 }
