DIFFERENCES IN: Engine\Private\SceneCaptureComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\SceneCaptureComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\SceneCaptureComponent.cpp
============================================================

SECTION starting at line 5 (left) / 5 (right):
----------------------------------------
  CONTEXT (line 5):     this->CaptureSource = SCS_SceneColorHDR;
  CONTEXT (line 6):     this->bCaptureEveryFrame = true;
  CONTEXT (line 7):     this->bCaptureOnMovement = true;
  REMOVED (line 8):     this->bShowEditorWidget = true;
  CONTEXT (line 9):     this->bCaptureGpuNextRender = false;
  CONTEXT (line 10):     this->bDumpGpuNextRender = false;
  CONTEXT (line 11):     this->bAlwaysPersistRenderingState = false;
  CONTEXT (line 12):     this->LODDistanceFactor = 1.00f;
  REMOVED (line 13):     this->ViewDistanceScaleOverride = -1.00f;
  CONTEXT (line 14):     this->MaxViewDistanceOverride = -1.00f;
  CONTEXT (line 15):     this->CaptureSortPriority = 0;
  CONTEXT (line 16):     this->bUseRayTracingIfEnabled = false;