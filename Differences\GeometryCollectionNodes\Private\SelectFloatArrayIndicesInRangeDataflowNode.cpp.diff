--- Left: SelectFloatArrayIndicesInRangeDataflowNode.cpp
+++ Right: SelectFloatArrayIndicesInRangeDataflowNode.cpp
@@ -1,8 +1,8 @@
 #include "SelectFloatArrayIndicesInRangeDataflowNode.h"

 

 FSelectFloatArrayIndicesInRangeDataflowNode::FSelectFloatArrayIndicesInRangeDataflowNode() {

-    this->Min = 0.00f;

-    this->Max = 0.00f;

+    this->min = 0.00f;

+    this->max = 0.00f;

     this->RangeSetting = ERangeSettingEnum::Dataflow_RangeSetting_InsideRange;

     this->bInclusive = false;

 }
