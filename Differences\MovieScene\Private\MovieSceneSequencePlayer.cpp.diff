--- Left: MovieSceneSequencePlayer.cpp
+++ Right: MovieSceneSequencePlayer.cpp
@@ -21,7 +21,7 @@
 void UMovieSceneSequencePlayer::SetWeight(double InWeight) {

 }

 

-void UMovieSceneSequencePlayer::SetTimeRange(float NewStartTime, float duration) {

+void UMovieSceneSequencePlayer::SetTimeRange(float NewStartTime, float Duration) {

 }

 

 void UMovieSceneSequencePlayer::SetPlayRate(float PlayRate) {

@@ -36,7 +36,7 @@
 void UMovieSceneSequencePlayer::SetFrameRate(FFrameRate FrameRate) {

 }

 

-void UMovieSceneSequencePlayer::SetFrameRange(int32 StartFrame, int32 duration, float SubFrames) {

+void UMovieSceneSequencePlayer::SetFrameRange(int32 StartFrame, int32 Duration, float SubFrames) {

 }

 

 void UMovieSceneSequencePlayer::SetDisableCameraCuts(bool bInDisableCameraCuts) {
