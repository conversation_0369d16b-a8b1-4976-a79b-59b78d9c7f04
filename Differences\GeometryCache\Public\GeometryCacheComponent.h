DIFFERENCES IN: GeometryCache\Public\GeometryCacheComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCache\Public\GeometryCacheComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCache\Public\GeometryCacheComponent.h
============================================================

SECTION starting at line 39 (left) / 39 (right):
----------------------------------------
  CONTEXT (line 39):     float ElapsedTime;
  CONTEXT (line 40): 
  CONTEXT (line 41):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 42):     float duration;
  ADDED   (line 42):     float Duration;
  CONTEXT (line 43): 
  CONTEXT (line 44):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 45):     bool bManualTick;