DIFFERENCES IN: RigVM\Private\RigVMFunction_MathBoolFlipFlop.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\RigVM\Private\RigVMFunction_MathBoolFlipFlop.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\RigVM\Private\RigVMFunction_MathBoolFlipFlop.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): FRigVMFunction_MathBoolFlipFlop::FRigVMFunction_MathBoolFlipFlop() {
  CONTEXT (line 4):     this->StartValue = false;
  REMOVED (line 5):     this->duration = 0.00f;
  ADDED   (line 5):     this->Duration = 0.00f;
  CONTEXT (line 6):     this->Result = false;
  CONTEXT (line 7):     this->LastValue = false;
  CONTEXT (line 8):     this->TimeLeft = 0.00f;