--- Left: Actor.h
+++ Right: Actor.h
@@ -140,15 +140,6 @@
     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bIsEditorOnlyActor: 1;

     

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bIsCookedForMobile: 1;

-    

-    UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bIsCookedForDesktop: 1;

-    

-    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    uint8 bShouldAllPrimitivesBeExcludedFromLightmaps: 1;

-    

 protected:

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bReplicates: 1;
