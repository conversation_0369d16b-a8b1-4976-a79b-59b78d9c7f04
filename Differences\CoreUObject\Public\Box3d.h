DIFFERENCES IN: CoreUObject\Public\Box3d.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\CoreUObject\Public\Box3d.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\CoreUObject\Public\Box3d.h
============================================================

SECTION starting at line 8 (left) / 8 (right):
----------------------------------------
  CONTEXT (line 8):     GENERATED_BODY()
  CONTEXT (line 9): public:
  CONTEXT (line 10):     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))
  REMOVED (line 11):     FVector3d Min;
  ADDED   (line 11):     FVector3d min;
  CONTEXT (line 12): 
  CONTEXT (line 13):     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))
  REMOVED (line 14):     FVector3d Max;
  ADDED   (line 14):     FVector3d max;
  CONTEXT (line 15): 
  CONTEXT (line 16):     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))
  CONTEXT (line 17):     bool IsValid;