DIFFERENCES IN: Engine\Private\ParticleSystemComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\ParticleSystemComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\ParticleSystemComponent.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): UParticleSystemComponent::UParticleSystemComponent(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {
  CONTEXT (line 4):     this->bAutoActivate = true;
  ADDED   (line 5):     this->bReceivesDecals = false;
  CONTEXT (line 5):     this->bCastVolumetricTranslucentShadow = true;
  CONTEXT (line 6):     this->bExcludeFromLightAttachmentGroup = true;
  CONTEXT (line 7):     this->Template = NULL;