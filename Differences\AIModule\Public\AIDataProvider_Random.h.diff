--- Left: AIDataProvider_Random.h
+++ Right: AIDataProvider_Random.h
@@ -9,10 +9,10 @@
 public:

 protected:

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float Min;

+    float min;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float Max;

+    float max;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bInteger: 1;
