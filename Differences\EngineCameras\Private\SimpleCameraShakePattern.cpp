DIFFERENCES IN: EngineCameras\Private\SimpleCameraShakePattern.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\EngineCameras\Private\SimpleCameraShakePattern.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\EngineCameras\Private\SimpleCameraShakePattern.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "SimpleCameraShakePattern.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): USimpleCameraShakePattern::USimpleCameraShakePattern() {
  REMOVED (line 4):     this->duration = 1.00f;
  ADDED   (line 4):     this->Duration = 1.00f;
  CONTEXT (line 5):     this->BlendInTime = 0.20f;
  CONTEXT (line 6):     this->BlendOutTime = 0.20f;
  CONTEXT (line 7): }