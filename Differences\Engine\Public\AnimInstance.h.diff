--- Left: AnimInstance.h
+++ Right: AnimInstance.h
@@ -164,7 +164,7 @@
     bool RequestTransitionEvent(const FName EventName, const double RequestTimeout, const ETransitionRequestQueueMode QueueMode, const ETransitionRequestOverwriteMode OverwriteMode);

     

     UFUNCTION(BlueprintCallable)

-    void RequestSlotGroupInertialization(FName InSlotGroupName, float duration, const UBlendProfile* BlendProfile);

+    void RequestSlotGroupInertialization(FName InSlotGroupName, float Duration, const UBlendProfile* BlendProfile);

     

     UFUNCTION(BlueprintCallable)

     void RemovePoseSnapshot(FName SnapshotName);
