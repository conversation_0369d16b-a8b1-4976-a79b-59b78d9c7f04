DIFFERENCES IN: HeadMountedDisplay\Public\XRHMDData.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\HeadMountedDisplay\Public\XRHMDData.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\HeadMountedDisplay\Public\XRHMDData.h
============================================================

SECTION starting at line 23 (left) / 23 (right):
----------------------------------------
  CONTEXT (line 23):     ETrackingStatus TrackingStatus;
  CONTEXT (line 24): 
  CONTEXT (line 25):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 26):     FVector Position;
  ADDED   (line 26):     FVector position;
  CONTEXT (line 27): 
  CONTEXT (line 28):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 29):     FQuat Rotation;