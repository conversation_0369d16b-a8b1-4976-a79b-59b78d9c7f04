DIFFERENCES IN: Engine\Public\MaterialExpressionSmoothStep.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\MaterialExpressionSmoothStep.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\MaterialExpressionSmoothStep.h
============================================================

SECTION starting at line 9 (left) / 9 (right):
----------------------------------------
  CONTEXT (line 9):     GENERATED_BODY()
  CONTEXT (line 10): public:
  CONTEXT (line 11):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 12):     FExpressionInput Min;
  ADDED   (line 12):     FExpressionInput min;
  CONTEXT (line 13): 
  CONTEXT (line 14):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 15):     FExpressionInput Max;
  ADDED   (line 15):     FExpressionInput max;
  CONTEXT (line 16): 
  CONTEXT (line 17):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 18):     FExpressionInput Value;