--- Left: ViewportStatsSubsystem.cpp
+++ Right: ViewportStatsSubsystem.cpp
@@ -6,7 +6,7 @@
 void UViewportStatsSubsystem::RemoveDisplayDelegate(const int32 IndexToRemove) {

 }

 

-void UViewportStatsSubsystem::AddTimedDisplay(FText Text, FLinearColor Color, float duration, const FVector2D& DisplayOffset) {

+void UViewportStatsSubsystem::AddTimedDisplay(FText Text, FLinearColor Color, float Duration, const FVector2D& DisplayOffset) {

 }

 

 int32 UViewportStatsSubsystem::AddDisplayDelegate(const FViewportDisplayCallback& Delegate) {
