DIFFERENCES IN: Engine\Private\MeshBuildSettings.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\MeshBuildSettings.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\MeshBuildSettings.cpp
============================================================

SECTION starting at line 11 (left) / 11 (right):
----------------------------------------
  CONTEXT (line 11):     this->bUseFullPrecisionUVs = false;
  CONTEXT (line 12):     this->bUseBackwardsCompatibleF16TruncUVs = false;
  CONTEXT (line 13):     this->bGenerateLightmapUVs = false;
  REMOVED (line 14):     this->bUseWithVolumetricLightmapsOnly = false;
  CONTEXT (line 15):     this->bGenerateDistanceFieldAsIfTwoSided = false;
  CONTEXT (line 16):     this->bSupportFaceRemap = false;
  CONTEXT (line 17):     this->MinLightmapResolution = 0;