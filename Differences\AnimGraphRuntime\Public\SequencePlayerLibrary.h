DIFFERENCES IN: AnimGraphRuntime\Public\SequencePlayerLibrary.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AnimGraphRuntime\Public\SequencePlayerLibrary.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AnimGraphRuntime\Public\SequencePlayerLibrary.h
============================================================

SECTION starting at line 55 (left) / 55 (right):
----------------------------------------
  CONTEXT (line 55):     static FSequencePlayerReference ConvertToSequencePlayer(const FAnimNodeReference& Node, EAnimNodeReferenceConversionResult& Result);
  CONTEXT (line 56): 
  CONTEXT (line 57):     UFUNCTION(BlueprintCallable, BlueprintPure)
  REMOVED (line 58):     static float ComputePlayRateFromDuration(const FSequencePlayerReference& SequencePlayer, float duration);
  ADDED   (line 58):     static float ComputePlayRateFromDuration(const FSequencePlayerReference& SequencePlayer, float Duration);
  CONTEXT (line 59): 
  CONTEXT (line 60): };
  CONTEXT (line 61): 