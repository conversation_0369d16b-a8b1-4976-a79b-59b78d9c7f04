DIFFERENCES IN: SequencerScripting\Public\MovieSceneSequenceExtensions.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\SequencerScripting\Public\MovieSceneSequenceExtensions.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\SequencerScripting\Public\MovieSceneSequenceExtensions.h
============================================================

SECTION starting at line 93 (left) / 93 (right):
----------------------------------------
  CONTEXT (line 93):     static void RemoveRootFolderFromSequence(UMovieSceneSequence* Sequence, UMovieSceneFolder* Folder);
  CONTEXT (line 94): 
  CONTEXT (line 95):     UFUNCTION(BlueprintCallable)
  REMOVED (line 96):     static FSequencerScriptingRange MakeRangeSeconds(UMovieSceneSequence* Sequence, float StartTime, float duration);
  REMOVED (line 97): 
  REMOVED (line 98):     UFUNCTION(BlueprintCallable)
  REMOVED (line 99):     static FSequencerScriptingRange MakeRange(UMovieSceneSequence* Sequence, int32 StartFrame, int32 duration);
  ADDED   (line 96):     static FSequencerScriptingRange MakeRangeSeconds(UMovieSceneSequence* Sequence, float StartTime, float Duration);
  ADDED   (line 97): 
  ADDED   (line 98):     UFUNCTION(BlueprintCallable)
  ADDED   (line 99):     static FSequencerScriptingRange MakeRange(UMovieSceneSequence* Sequence, int32 StartFrame, int32 Duration);
  CONTEXT (line 100): 
  CONTEXT (line 101):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 102):     static TArray<UObject*> LocateBoundObjects(UMovieSceneSequence* Sequence, const FMovieSceneBindingProxy& InBinding, UObject* Context);