DIFFERENCES IN: InterchangeNodes\Public\InterchangeMeshNode.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\InterchangeNodes\Public\InterchangeMeshNode.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\InterchangeNodes\Public\InterchangeMeshNode.h
============================================================

SECTION starting at line 24 (left) / 24 (right):
----------------------------------------
  CONTEXT (line 24):     bool SetSceneInstanceUid(const FString& DependencyUid);
  CONTEXT (line 25): 
  CONTEXT (line 26):     UFUNCTION(BlueprintCallable)
  REMOVED (line 27):     void SetPayLoadKey(const FString& PayloadKey, const EInterchangeMeshPayLoadType& PayloadType);
  ADDED   (line 27):     void SetPayLoadKey(const FString& PayloadKey, const EInterchangeMeshPayLoadType& PayLoadType);
  CONTEXT (line 28): 
  CONTEXT (line 29):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 30):     bool SetMorphTargetName(const FString& MorphTargetName);