DIFFERENCES IN: UMG\Private\ScrollBar.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\UMG\Private\ScrollBar.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\UMG\Private\ScrollBar.cpp
============================================================

SECTION starting at line 4 (left) / 4 (right):
----------------------------------------
  CONTEXT (line 4):     this->bIsVariable = false;
  CONTEXT (line 5):     this->bAlwaysShowScrollbar = true;
  CONTEXT (line 6):     this->bAlwaysShowScrollbarTrack = true;
  REMOVED (line 7):     this->orientation = Orient_Vertical;
  ADDED   (line 7):     this->Orientation = Orient_Vertical;
  CONTEXT (line 8): }
  CONTEXT (line 9): 
  CONTEXT (line 10): void UScrollBar::SetState(float InOffsetFraction, float InThumbSizeFraction) {