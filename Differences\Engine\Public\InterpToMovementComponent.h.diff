--- Left: InterpToMovementComponent.h
+++ Right: InterpToMovementComponent.h
@@ -19,7 +19,7 @@
     DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInterpToResetDelegate, const FHitResult&, ImpactResult, float, Time);

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float duration;

+    float Duration;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     uint8 bPauseOnImpact: 1;
