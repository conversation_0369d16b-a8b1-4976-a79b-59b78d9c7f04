DIFFERENCES IN: UMG\Private\GameViewportSubsystem.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\UMG\Private\GameViewportSubsystem.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\UMG\Private\GameViewportSubsystem.cpp
============================================================

SECTION starting at line 3 (left) / 3 (right):
----------------------------------------
  CONTEXT (line 3): UGameViewportSubsystem::UGameViewportSubsystem() {
  CONTEXT (line 4): }
  CONTEXT (line 5): 
  REMOVED (line 6): FGameViewportWidgetSlot UGameViewportSubsystem::SetWidgetSlotPosition(FGameViewportWidgetSlot Slot, const UWidget* Widget, FVector2D Position, bool bRemoveDPIScale) {
  ADDED   (line 6): FGameViewportWidgetSlot UGameViewportSubsystem::SetWidgetSlotPosition(FGameViewportWidgetSlot Slot, const UWidget* Widget, FVector2D position, bool bRemoveDPIScale) {
  CONTEXT (line 7):     return FGameViewportWidgetSlot{};
  CONTEXT (line 8): }
  CONTEXT (line 9): 