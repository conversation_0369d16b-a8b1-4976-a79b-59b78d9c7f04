DIFFERENCES IN: GeometryCollectionNodes\Public\CollectionTransformSelectionByFloatAttrDataflowNode.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCollectionNodes\Public\CollectionTransformSelectionByFloatAttrDataflowNode.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCollectionNodes\Public\CollectionTransformSelectionByFloatAttrDataflowNode.h
============================================================

SECTION starting at line 20 (left) / 20 (right):
----------------------------------------
  CONTEXT (line 20):     FString AttrName;
  CONTEXT (line 21): 
  CONTEXT (line 22):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 23):     float Min;
  ADDED   (line 23):     float min;
  CONTEXT (line 24): 
  CONTEXT (line 25):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 26):     float Max;
  ADDED   (line 26):     float max;
  CONTEXT (line 27): 
  CONTEXT (line 28):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 29):     ERangeSettingEnum RangeSetting;