--- Left: OpenXRBlueprintFunctionLibrary.h
+++ Right: OpenXRBlueprintFunctionLibrary.h
@@ -12,14 +12,5 @@
     UFUNCTION(BlueprintCallable)

     static void SetEnvironmentBlendMode(int32 NewBlendMode);

     

-    UFUNCTION(BlueprintCallable)

-    static int32 GetPrimarySwapChainWidth();

-    

-    UFUNCTION(BlueprintCallable)

-    static int32 GetPrimarySwapChainHeight();

-    

-    UFUNCTION(BlueprintCallable)

-    static float GetHMDDisplayRefreshRate();

-    

 };

 
