DIFFERENCES IN: Landscape\Private\LandscapeSplineMeshEntry.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Landscape\Private\LandscapeSplineMeshEntry.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Landscape\Private\LandscapeSplineMeshEntry.cpp
============================================================

SECTION starting at line 5 (left) / 5 (right):
----------------------------------------
  CONTEXT (line 5):     this->bCenterH = false;
  CONTEXT (line 6):     this->bScaleToWidth = false;
  CONTEXT (line 7):     this->bNoZScaling = false;
  REMOVED (line 8):     this->orientation = LSMO_XUp;
  ADDED   (line 8):     this->Orientation = LSMO_XUp;
  CONTEXT (line 9):     this->ForwardAxis = ESplineMeshAxis::X;
  CONTEXT (line 10):     this->UpAxis = ESplineMeshAxis::X;
  CONTEXT (line 11): }