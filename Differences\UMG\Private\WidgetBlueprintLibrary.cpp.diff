--- Left: WidgetBlueprintLibrary.cpp
+++ Right: WidgetBlueprintLibrary.cpp
@@ -146,10 +146,10 @@
     return FEventReply{};

 }

 

-void UWidgetBlueprintLibrary::DrawTextFormatted(FPaintContext& Context, const FText& Text, FVector2D Position, UFont* Font, float FontSize, FName FontTypeFace, FLinearColor Tint) {

-}

-

-void UWidgetBlueprintLibrary::DrawText(FPaintContext& Context, const FString& InString, FVector2D Position, FLinearColor Tint) {

+void UWidgetBlueprintLibrary::DrawTextFormatted(FPaintContext& Context, const FText& Text, FVector2D position, UFont* Font, float FontSize, FName FontTypeFace, FLinearColor Tint) {

+}

+

+void UWidgetBlueprintLibrary::DrawText(FPaintContext& Context, const FString& InString, FVector2D position, FLinearColor Tint) {

 }

 

 void UWidgetBlueprintLibrary::DrawSpline(FPaintContext& Context, FVector2D Start, FVector2D StartDir, FVector2D End, FVector2D EndDir, FLinearColor Tint, float Thickness) {

@@ -161,7 +161,7 @@
 void UWidgetBlueprintLibrary::DrawLine(FPaintContext& Context, FVector2D PositionA, FVector2D PositionB, FLinearColor Tint, bool bAntiAlias, float Thickness) {

 }

 

-void UWidgetBlueprintLibrary::DrawBox(FPaintContext& Context, FVector2D Position, FVector2D Size, USlateBrushAsset* Brush, FLinearColor Tint) {

+void UWidgetBlueprintLibrary::DrawBox(FPaintContext& Context, FVector2D position, FVector2D Size, USlateBrushAsset* Brush, FLinearColor Tint) {

 }

 

 void UWidgetBlueprintLibrary::DismissAllMenus() {
