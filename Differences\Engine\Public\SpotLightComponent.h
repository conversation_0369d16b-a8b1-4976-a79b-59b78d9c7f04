DIFFERENCES IN: Engine\Public\SpotLightComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\SpotLightComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\SpotLightComponent.h
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): #include "CoreMinimal.h"
  CONTEXT (line 3): #include "PointLightComponent.h"
  CONTEXT (line 4): #include "SpotLightComponent.generated.h"
  REMOVED (line 5): 
  REMOVED (line 6): class UTexture2D;
  CONTEXT (line 7): 
  CONTEXT (line 8): UCLASS(Blueprintable, EditInlineNew, MinimalAPI, ClassGroup=Custom, meta=(BlueprintSpawnableComponent))
  CONTEXT (line 9): class USpotLightComponent : public UPointLightComponent {

SECTION starting at line 15 (left) / 13 (right):
----------------------------------------
  CONTEXT (line 15):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 16):     float OuterConeAngle;
  CONTEXT (line 17): 
  REMOVED (line 18):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 19):     float NearClipPlane;
  REMOVED (line 20): 
  REMOVED (line 21):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 22):     UTexture2D* CookieTexture;
  REMOVED (line 23): 
  CONTEXT (line 24):     USpotLightComponent(const FObjectInitializer& ObjectInitializer);
  CONTEXT (line 25): 
  CONTEXT (line 26):     UFUNCTION(BlueprintCallable)