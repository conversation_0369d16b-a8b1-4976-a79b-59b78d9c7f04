--- Left: PlayerCameraManager.h
+++ Right: PlayerCameraManager.h
@@ -173,7 +173,7 @@
     UCameraShakeBase* StartCameraShake(TSubclassOf<UCameraShakeBase> ShakeClass, float Scale, ECameraShakePlaySpace PlaySpace, FRotator UserPlaySpaceRot);

     

     UFUNCTION(BlueprintCallable)

-    void StartCameraFade(float FromAlpha, float ToAlpha, float duration, FLinearColor Color, bool bShouldFadeAudio, bool bHoldWhenFinished);

+    void StartCameraFade(float FromAlpha, float ToAlpha, float Duration, FLinearColor Color, bool bShouldFadeAudio, bool bHoldWhenFinished);

     

     UFUNCTION(BlueprintCallable)

     void SetManualCameraFade(float InFadeAmount, FLinearColor Color, bool bInFadeAudio);
