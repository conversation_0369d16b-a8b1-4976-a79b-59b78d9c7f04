DIFFERENCES IN: Engine\Private\PlayerController.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\PlayerController.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\PlayerController.cpp
============================================================

SECTION starting at line 173 (left) / 173 (right):
----------------------------------------
  CONTEXT (line 173):     return true;
  CONTEXT (line 174): }
  CONTEXT (line 175): 
  REMOVED (line 176): void APlayerController::ServerUnmutePlayer_Implementation(FUniqueNetIdRepl PlayerID) {
  REMOVED (line 177): }
  REMOVED (line 178): bool APlayerController::ServerUnmutePlayer_Validate(FUniqueNetIdRepl PlayerID) {
  REMOVED (line 179):     return true;
  REMOVED (line 180): }
  REMOVED (line 181): 
  REMOVED (line 182): void APlayerController::ServerUnblockPlayer_Implementation(FUniqueNetIdRepl PlayerID) {
  REMOVED (line 183): }
  REMOVED (line 184): bool APlayerController::ServerUnblockPlayer_Validate(FUniqueNetIdRepl PlayerID) {
  ADDED   (line 176): void APlayerController::ServerUnmutePlayer_Implementation(FUniqueNetIdRepl PlayerId) {
  ADDED   (line 177): }
  ADDED   (line 178): bool APlayerController::ServerUnmutePlayer_Validate(FUniqueNetIdRepl PlayerId) {
  ADDED   (line 179):     return true;
  ADDED   (line 180): }
  ADDED   (line 181): 
  ADDED   (line 182): void APlayerController::ServerUnblockPlayer_Implementation(FUniqueNetIdRepl PlayerId) {
  ADDED   (line 183): }
  ADDED   (line 184): bool APlayerController::ServerUnblockPlayer_Validate(FUniqueNetIdRepl PlayerId) {
  CONTEXT (line 185):     return true;
  CONTEXT (line 186): }
  CONTEXT (line 187): 

SECTION starting at line 233 (left) / 233 (right):
----------------------------------------
  CONTEXT (line 233):     return true;
  CONTEXT (line 234): }
  CONTEXT (line 235): 
  REMOVED (line 236): void APlayerController::ServerMutePlayer_Implementation(FUniqueNetIdRepl PlayerID) {
  REMOVED (line 237): }
  REMOVED (line 238): bool APlayerController::ServerMutePlayer_Validate(FUniqueNetIdRepl PlayerID) {
  ADDED   (line 236): void APlayerController::ServerMutePlayer_Implementation(FUniqueNetIdRepl PlayerId) {
  ADDED   (line 237): }
  ADDED   (line 238): bool APlayerController::ServerMutePlayer_Validate(FUniqueNetIdRepl PlayerId) {
  CONTEXT (line 239):     return true;
  CONTEXT (line 240): }
  CONTEXT (line 241): 

SECTION starting at line 272 (left) / 272 (right):
----------------------------------------
  CONTEXT (line 272):     return true;
  CONTEXT (line 273): }
  CONTEXT (line 274): 
  REMOVED (line 275): void APlayerController::ServerBlockPlayer_Implementation(FUniqueNetIdRepl PlayerID) {
  REMOVED (line 276): }
  REMOVED (line 277): bool APlayerController::ServerBlockPlayer_Validate(FUniqueNetIdRepl PlayerID) {
  ADDED   (line 275): void APlayerController::ServerBlockPlayer_Implementation(FUniqueNetIdRepl PlayerId) {
  ADDED   (line 276): }
  ADDED   (line 277): bool APlayerController::ServerBlockPlayer_Validate(FUniqueNetIdRepl PlayerId) {
  CONTEXT (line 278):     return true;
  CONTEXT (line 279): }
  CONTEXT (line 280): 

SECTION starting at line 303 (left) / 303 (right):
----------------------------------------
  CONTEXT (line 303): void APlayerController::PlayHapticEffect(UHapticFeedbackEffect_Base* HapticEffect, EControllerHand Hand, float Scale, bool bLoop) {
  CONTEXT (line 304): }
  CONTEXT (line 305): 
  REMOVED (line 306): void APlayerController::PlayDynamicForceFeedback(float Intensity, float duration, bool bAffectsLeftLarge, bool bAffectsLeftSmall, bool bAffectsRightLarge, bool bAffectsRightSmall, TEnumAsByte<EDynamicForceFeedbackAction::Type> Action, FLatentActionInfo LatentInfo) {
  ADDED   (line 306): void APlayerController::PlayDynamicForceFeedback(float Intensity, float Duration, bool bAffectsLeftLarge, bool bAffectsLeftSmall, bool bAffectsRightLarge, bool bAffectsRightSmall, TEnumAsByte<EDynamicForceFeedbackAction::Type> Action, FLatentActionInfo LatentInfo) {
  CONTEXT (line 307): }
  CONTEXT (line 308): 
  CONTEXT (line 309): void APlayerController::Pause() {

SECTION starting at line 455 (left) / 455 (right):
----------------------------------------
  CONTEXT (line 455): void APlayerController::ClientUnmutePlayers_Implementation(const TArray<FUniqueNetIdRepl>& PlayerIds) {
  CONTEXT (line 456): }
  CONTEXT (line 457): 
  REMOVED (line 458): void APlayerController::ClientUnmutePlayer_Implementation(FUniqueNetIdRepl PlayerID) {
  ADDED   (line 458): void APlayerController::ClientUnmutePlayer_Implementation(FUniqueNetIdRepl PlayerId) {
  CONTEXT (line 459): }
  CONTEXT (line 460): 
  CONTEXT (line 461): void APlayerController::ClientTravelInternal_Implementation(const FString& URL, TEnumAsByte<ETravelType> TravelType, bool bSeamless, FGuid MapPackageGuid) {

SECTION starting at line 557 (left) / 557 (right):
----------------------------------------
  CONTEXT (line 557): void APlayerController::ClientPlayForceFeedback_Internal_Implementation(UForceFeedbackEffect* ForceFeedbackEffect, FForceFeedbackParameters Params) {
  CONTEXT (line 558): }
  CONTEXT (line 559): 
  REMOVED (line 560): void APlayerController::ClientMutePlayer_Implementation(FUniqueNetIdRepl PlayerID) {
  ADDED   (line 560): void APlayerController::ClientMutePlayer_Implementation(FUniqueNetIdRepl PlayerId) {
  CONTEXT (line 561): }
  CONTEXT (line 562): 
  CONTEXT (line 563): void APlayerController::ClientMessage_Implementation(const FString& S, FName Type, float MsgLifeTime) {

SECTION starting at line 602 (left) / 602 (right):
----------------------------------------
  CONTEXT (line 602): void APlayerController::ClientCancelPendingMapChange_Implementation() {
  CONTEXT (line 603): }
  CONTEXT (line 604): 
  REMOVED (line 605): void APlayerController::ClientAddTextureStreamingLoc_Implementation(FVector InLoc, float duration, bool bOverrideLocation) {
  ADDED   (line 605): void APlayerController::ClientAddTextureStreamingLoc_Implementation(FVector InLoc, float Duration, bool bOverrideLocation) {
  CONTEXT (line 606): }
  CONTEXT (line 607): 
  CONTEXT (line 608): void APlayerController::ClientAckUpdateLevelVisibility_Implementation(FName PackageName, FNetLevelVisibilityTransactionId TransactionId, bool bClientAckCanMakeVisible) {