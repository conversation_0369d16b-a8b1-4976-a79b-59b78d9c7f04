DIFFERENCES IN: AudioWidgets\Private\AudioMaterialSlider.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AudioWidgets\Private\AudioMaterialSlider.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AudioWidgets\Private\AudioMaterialSlider.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): UAudioMaterialSlider::UAudioMaterialSlider() {
  CONTEXT (line 4):     this->Value = 1.00f;
  REMOVED (line 5):     this->orientation = Orient_Horizontal;
  ADDED   (line 5):     this->Orientation = Orient_Horizontal;
  CONTEXT (line 6):     this->TuneSpeed = 0.20f;
  CONTEXT (line 7):     this->FineTuneSpeed = 0.05f;
  CONTEXT (line 8):     this->bLocked = false;