DIFFERENCES IN: LevelSequence\Public\LevelSequenceDirector.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\LevelSequence\Public\LevelSequenceDirector.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\LevelSequence\Public\LevelSequenceDirector.h
============================================================

SECTION starting at line 21 (left) / 21 (right):
----------------------------------------
  CONTEXT (line 21):     TWeakObjectPtr<UMovieSceneEntitySystemLinker> WeakLinker;
  CONTEXT (line 22): 
  CONTEXT (line 23):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 24):     uint16 InstanceID;
  ADDED   (line 24):     uint16 InstanceId;
  CONTEXT (line 25): 
  CONTEXT (line 26):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 27):     uint16 InstanceSerial;