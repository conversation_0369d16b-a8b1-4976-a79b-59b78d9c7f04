DIFFERENCES IN: InterchangeCore\Private\InterchangeBaseNode.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\InterchangeCore\Private\InterchangeBaseNode.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\InterchangeCore\Private\InterchangeBaseNode.cpp
============================================================

SECTION starting at line 38 (left) / 38 (right):
----------------------------------------
  CONTEXT (line 38):     return false;
  CONTEXT (line 39): }
  CONTEXT (line 40): 
  REMOVED (line 41): FString UInterchangeBaseNode::GetUniqueId() const {
  ADDED   (line 41): FString UInterchangeBaseNode::GetUniqueID() const {
  CONTEXT (line 42):     return TEXT("");
  CONTEXT (line 43): }
  CONTEXT (line 44): 