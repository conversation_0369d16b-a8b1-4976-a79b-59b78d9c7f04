DIFFERENCES IN: Niagara\Public\NiagaraPreviewAxis_InterpParamInt32.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Niagara\Public\NiagaraPreviewAxis_InterpParamInt32.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Niagara\Public\NiagaraPreviewAxis_InterpParamInt32.h
============================================================

SECTION starting at line 9 (left) / 9 (right):
----------------------------------------
  CONTEXT (line 9): public:
  CONTEXT (line 10): private:
  CONTEXT (line 11):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 12):     int32 Min;
  ADDED   (line 12):     int32 min;
  CONTEXT (line 13): 
  CONTEXT (line 14):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 15):     int32 Max;
  ADDED   (line 15):     int32 max;
  CONTEXT (line 16): 
  CONTEXT (line 17): public:
  CONTEXT (line 18):     UNiagaraPreviewAxis_InterpParamInt32();