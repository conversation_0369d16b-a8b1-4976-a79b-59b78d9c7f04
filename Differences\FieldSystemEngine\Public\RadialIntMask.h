DIFFERENCES IN: FieldSystemEngine\Public\RadialIntMask.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\FieldSystemEngine\Public\RadialIntMask.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\FieldSystemEngine\Public\RadialIntMask.h
============================================================

SECTION starting at line 15 (left) / 15 (right):
----------------------------------------
  CONTEXT (line 15):     float Radius;
  CONTEXT (line 16): 
  CONTEXT (line 17):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 18):     FVector Position;
  ADDED   (line 18):     FVector position;
  CONTEXT (line 19): 
  CONTEXT (line 20):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 21):     int32 InteriorValue;