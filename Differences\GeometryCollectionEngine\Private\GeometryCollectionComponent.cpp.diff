--- Left: GeometryCollectionComponent.cpp
+++ Right: GeometryCollectionComponent.cpp
@@ -225,7 +225,7 @@
 void UGeometryCollectionComponent::ApplyLinearVelocity(int32 ItemIndex, const FVector& LinearVelocity) {

 }

 

-void UGeometryCollectionComponent::Apply<PERSON><PERSON><PERSON><PERSON><PERSON>(float Radius, FVector Position) {

+void UGeometryCollectionComponent::Apply<PERSON><PERSON><PERSON><PERSON><PERSON>(float Radius, FVector position) {

 }

 

 void UGeometryCollectionComponent::ApplyInternalStrain(int32 ItemIndex, const FVector& Location, float Radius, int32 PropagationDepth, float PropagationFactor, float Strain) {
