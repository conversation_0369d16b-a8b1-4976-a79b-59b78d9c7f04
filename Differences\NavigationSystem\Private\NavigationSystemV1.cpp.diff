--- Left: NavigationSystemV1.cpp
+++ Right: NavigationSystemV1.cpp
@@ -4,10 +4,10 @@
 UNavigationSystemV1::UNavigationSystemV1() {

     this->MainNavData = NULL;

     this->AbstractNavData = NULL;

-    this->bAutoCreateNavigationData = false;

-    this->bSpawnNavDataInNavBoundsLevel = true;

-    this->bAllowClientSideNavigation = false;

-    this->bShouldDiscardSubLevelNavData = false;

+    this->bAutoCreateNavigationData = true;

+    this->bSpawnNavDataInNavBoundsLevel = false;

+    this->bAllowClientSideNavigation = true;

+    this->bShouldDiscardSubLevelNavData = true;

     this->bTickWhilePaused = false;

     this->bSupportRebuilding = false;

     this->bInitialBuildingLocked = false;

@@ -16,10 +16,9 @@
     this->bGenerateNavigationOnlyAroundNavigationInvokers = false;

     this->ActiveTilesUpdateInterval = 1.00f;

     this->InvokersMaximumDistanceFromSeed = -1.00f;

-    this->DataGatheringMode = ENavDataGatheringModeConfig::Lazy;

+    this->DataGatheringMode = ENavDataGatheringModeConfig::Instant;

     this->DirtyAreaWarningSizeThreshold = -1.00f;

     this->GatheringNavModifiersWarningLimitTime = -1.00f;

-    this->SupportedAgents.AddDefaulted(2);

     this->OperationMode = FNavigationSystemRunMode::InvalidMode;

     this->Repository = NULL;

 }
