--- Left: Box2D.h
+++ Right: Box2D.h
@@ -8,10 +8,10 @@
     GENERATED_BODY()

 public:

     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))

-    FVector2D Min;

+    FVector2D min;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))

-    FVector2D Max;

+    FVector2D max;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, SaveGame, meta=(AllowPrivateAccess=true))

     bool bIsValid;
