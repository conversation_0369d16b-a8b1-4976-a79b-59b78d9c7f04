DIFFERENCES IN: UMG\Public\WidgetBlueprintLibrary.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\UMG\Public\WidgetBlueprintLibrary.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\UMG\Public\WidgetBlueprintLibrary.h
============================================================

SECTION starting at line 160 (left) / 160 (right):
----------------------------------------
  CONTEXT (line 160):     static FEventReply EndDragDrop(UPARAM(Ref) FEventReply& Reply);
  CONTEXT (line 161): 
  CONTEXT (line 162):     UFUNCTION(BlueprintCallable)
  REMOVED (line 163):     static void DrawTextFormatted(UPARAM(Ref) FPaintContext& Context, const FText& Text, FVector2D Position, UFont* Font, float FontSize, FName FontTypeFace, FLinearColor Tint);
  REMOVED (line 164): 
  REMOVED (line 165):     UFUNCTION(BlueprintCallable)
  REMOVED (line 166):     static void DrawText(UPARAM(Ref) FPaintContext& Context, const FString& InString, FVector2D Position, FLinearColor Tint);
  ADDED   (line 163):     static void DrawTextFormatted(UPARAM(Ref) FPaintContext& Context, const FText& Text, FVector2D position, UFont* Font, float FontSize, FName FontTypeFace, FLinearColor Tint);
  ADDED   (line 164): 
  ADDED   (line 165):     UFUNCTION(BlueprintCallable)
  ADDED   (line 166):     static void DrawText(UPARAM(Ref) FPaintContext& Context, const FString& InString, FVector2D position, FLinearColor Tint);
  CONTEXT (line 167): 
  CONTEXT (line 168):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 169):     static void DrawSpline(UPARAM(Ref) FPaintContext& Context, FVector2D Start, FVector2D StartDir, FVector2D End, FVector2D EndDir, FLinearColor Tint, float Thickness);

SECTION starting at line 175 (left) / 175 (right):
----------------------------------------
  CONTEXT (line 175):     static void DrawLine(UPARAM(Ref) FPaintContext& Context, FVector2D PositionA, FVector2D PositionB, FLinearColor Tint, bool bAntiAlias, float Thickness);
  CONTEXT (line 176): 
  CONTEXT (line 177):     UFUNCTION(BlueprintCallable)
  REMOVED (line 178):     static void DrawBox(UPARAM(Ref) FPaintContext& Context, FVector2D Position, FVector2D Size, USlateBrushAsset* Brush, FLinearColor Tint);
  ADDED   (line 178):     static void DrawBox(UPARAM(Ref) FPaintContext& Context, FVector2D position, FVector2D Size, USlateBrushAsset* Brush, FLinearColor Tint);
  CONTEXT (line 179): 
  CONTEXT (line 180):     UFUNCTION(BlueprintCallable, BlueprintCosmetic)
  CONTEXT (line 181):     static void DismissAllMenus();