DIFFERENCES IN: Engine\Public\HUD.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\HUD.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\HUD.h
============================================================

SECTION starting at line 155 (left) / 155 (right):
----------------------------------------
  CONTEXT (line 155):     void Deproject(float ScreenX, float ScreenY, FVector& WorldPosition, FVector& WorldDirection) const;
  CONTEXT (line 156): 
  CONTEXT (line 157):     UFUNCTION(BlueprintCallable)
  REMOVED (line 158):     void AddHitBox(FVector2D Position, FVector2D Size, FName InName, bool bConsumesInput, int32 Priority);
  ADDED   (line 158):     void AddHitBox(FVector2D position, FVector2D Size, FName InName, bool bConsumesInput, int32 Priority);
  CONTEXT (line 159): 
  CONTEXT (line 160):     UFUNCTION(BlueprintCallable, Client, Reliable)
  REMOVED (line 161):     void AddDebugText(const FString& DebugText, AActor* SrcActor, float duration, FVector Offset, FVector DesiredOffset, FColor TextColor, bool bSkipOverwriteCheck, bool bAbsoluteLocation, bool bKeepAttachedToActor, UFont* InFont, float FontScale, bool bDrawShadow);
  ADDED   (line 161):     void AddDebugText(const FString& DebugText, AActor* SrcActor, float Duration, FVector Offset, FVector DesiredOffset, FColor TextColor, bool bSkipOverwriteCheck, bool bAbsoluteLocation, bool bKeepAttachedToActor, UFont* InFont, float FontScale, bool bDrawShadow);
  CONTEXT (line 162): 
  CONTEXT (line 163): };
  CONTEXT (line 164): 