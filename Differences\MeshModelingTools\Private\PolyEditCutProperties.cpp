DIFFERENCES IN: MeshModelingTools\Private\PolyEditCutProperties.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MeshModelingTools\Private\PolyEditCutProperties.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MeshModelingTools\Private\PolyEditCutProperties.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "PolyEditCutProperties.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): UPolyEditCutProperties::UPolyEditCutProperties() {
  REMOVED (line 4):     this->orientation = EPolyEditCutPlaneOrientation::FaceNormals;
  ADDED   (line 4):     this->Orientation = EPolyEditCutPlaneOrientation::FaceNormals;
  CONTEXT (line 5):     this->bSnapToVertices = true;
  CONTEXT (line 6): }
  CONTEXT (line 7): 