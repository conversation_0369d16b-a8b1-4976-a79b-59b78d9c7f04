DIFFERENCES IN: MeshDescription\Private\MeshDescriptionBase.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MeshDescription\Private\MeshDescriptionBase.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MeshDescription\Private\MeshDescriptionBase.cpp
============================================================

SECTION starting at line 3 (left) / 3 (right):
----------------------------------------
  CONTEXT (line 3): UMeshDescriptionBase::UMeshDescriptionBase() {
  CONTEXT (line 4): }
  CONTEXT (line 5): 
  REMOVED (line 6): void UMeshDescriptionBase::SetVertexPosition(FVertexID VertexID, const FVector& Position) {
  ADDED   (line 6): void UMeshDescriptionBase::SetVertexPosition(FVertexID VertexID, const FVector& position) {
  CONTEXT (line 7): }
  CONTEXT (line 8): 
  CONTEXT (line 9): void UMeshDescriptionBase::SetPolygonVertexInstances(FPolygonID PolygonID, const TArray<FVertexInstanceID>& VertexInstanceIDs) {