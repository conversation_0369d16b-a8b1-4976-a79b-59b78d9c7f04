DIFFERENCES IN: GeometryCollectionEngine\Public\GeometryCollectionComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCollectionEngine\Public\GeometryCollectionComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCollectionEngine\Public\GeometryCollectionComponent.h
============================================================

SECTION starting at line 442 (left) / 442 (right):
----------------------------------------
  CONTEXT (line 442):     void ApplyLinearVelocity(int32 ItemIndex, const FVector& LinearVelocity);
  CONTEXT (line 443): 
  CONTEXT (line 444):     UFUNCTION(BlueprintCallable)
  REMOVED (line 445):     void ApplyKinematicField(float Radius, FVector Position);
  ADDED   (line 445):     void ApplyKinematicField(float Radius, FVector position);
  CONTEXT (line 446): 
  CONTEXT (line 447):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 448):     void ApplyInternalStrain(int32 ItemIndex, const FVector& Location, float Radius, int32 PropagationDepth, float PropagationFactor, float Strain);