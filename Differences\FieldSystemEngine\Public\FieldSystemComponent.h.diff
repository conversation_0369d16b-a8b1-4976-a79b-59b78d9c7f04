--- Left: FieldSystemComponent.h
+++ Right: FieldSystemComponent.h
@@ -42,19 +42,19 @@
     void RemovePersistentFields();

     

     UFUNCTION(BlueprintCallable)

-    void ApplyUniformVectorFalloffForce(bool Enabled, FVector Position, FVector Direction, float Radius, float Magnitude);

+    void ApplyUniformVectorFalloffForce(bool Enabled, FVector position, FVector Direction, float Radius, float Magnitude);

     

     UFUNCTION(BlueprintCallable)

-    void ApplyStrainField(bool Enabled, FVector Position, float Radius, float Magnitude, int32 Iterations);

+    void ApplyStrainField(bool Enabled, FVector position, float Radius, float Magnitude, int32 Iterations);

     

     UFUNCTION(BlueprintCallable)

-    void ApplyStayDynamicField(bool Enabled, FVector Position, float Radius);

+    void ApplyStayDynamicField(bool Enabled, FVector position, float Radius);

     

     UFUNCTION(BlueprintCallable)

-    void ApplyRadialVectorFalloffForce(bool Enabled, FVector Position, float Radius, float Magnitude);

+    void ApplyRadialVectorFalloffForce(bool Enabled, FVector position, float Radius, float Magnitude);

     

     UFUNCTION(BlueprintCallable)

-    void ApplyRadialForce(bool Enabled, FVector Position, float Magnitude);

+    void ApplyRadialForce(bool Enabled, FVector position, float Magnitude);

     

     UFUNCTION(BlueprintCallable)

     void ApplyPhysicsField(bool Enabled, TEnumAsByte<EFieldPhysicsType> Target, UFieldSystemMetaData* MetaData, UFieldNodeBase* Field);
