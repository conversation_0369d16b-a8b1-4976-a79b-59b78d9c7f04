--- Left: WaveScalar.h
+++ Right: WaveScalar.h
@@ -16,10 +16,10 @@
     float Magnitude;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    FVector Position;

+    FVector position;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float WaveLength;

+    float Wavelength;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     float Period;

@@ -33,7 +33,7 @@
     UWaveScalar(const FObjectInitializer& ObjectInitializer);

 

     UFUNCTION(BlueprintCallable, BlueprintPure)

-    UWaveScalar* SetWaveScalar(float NewMagnitude, FVector NewPosition, float NewWaveLength, float NewPeriod, float Time, TEnumAsByte<EWaveFunctionType> NewFunction, TEnumAsByte<EFieldFalloffType> NewFalloff);

+    UWaveScalar* SetWaveScalar(float NewMagnitude, FVector NewPosition, float NewWavelength, float NewPeriod, float Time, TEnumAsByte<EWaveFunctionType> NewFunction, TEnumAsByte<EFieldFalloffType> NewFalloff);

     

 };

 
