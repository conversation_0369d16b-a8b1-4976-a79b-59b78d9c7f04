DIFFERENCES IN: NavigationSystem\Private\RecastNavMesh.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\NavigationSystem\Private\RecastNavMesh.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\NavigationSystem\Private\RecastNavMesh.cpp
============================================================

SECTION starting at line 26 (left) / 26 (right):
----------------------------------------
  CONTEXT (line 26):     this->DrawOffset = 10.00f;
  CONTEXT (line 27):     this->bFixedTilePoolSize = false;
  CONTEXT (line 28):     this->TilePoolSize = 1024;
  REMOVED (line 29):     this->TileSizeUU = 2048.00f;
  REMOVED (line 30):     this->CellSize = 8.00f;
  REMOVED (line 31):     this->CellHeight = 15.00f;
  ADDED   (line 29):     this->TileSizeUU = 1000.00f;
  ADDED   (line 30):     this->CellSize = 19.00f;
  ADDED   (line 31):     this->CellHeight = 10.00f;
  CONTEXT (line 32):     this->AgentMaxStepHeight = 35.00f;
  REMOVED (line 33):     this->AgentRadius = 18.00f;
  REMOVED (line 34):     this->AgentHeight = 180.00f;
  ADDED   (line 33):     this->AgentRadius = 34.00f;
  ADDED   (line 34):     this->AgentHeight = 144.00f;
  CONTEXT (line 35):     this->AgentMaxSlope = 44.00f;
  REMOVED (line 36):     this->MinRegionArea = 100.00f;
  ADDED   (line 36):     this->MinRegionArea = 0.00f;
  CONTEXT (line 37):     this->MergeRegionSize = 400.00f;
  CONTEXT (line 38):     this->MaxVerticalMergeError = 2147483647;
  REMOVED (line 39):     this->MaxSimplificationError = 2.00f;
  ADDED   (line 39):     this->MaxSimplificationError = 1.30f;
  CONTEXT (line 40):     this->SimplificationElevationRatio = 0.00f;
  CONTEXT (line 41):     this->MaxSimultaneousTileGenerationJobsCount = 1024;
  CONTEXT (line 42):     this->TileNumberHardLimit = 1048576;

SECTION starting at line 47 (left) / 47 (right):
----------------------------------------
  CONTEXT (line 47):     this->DefaultMaxSearchNodes = 2048.00f;
  CONTEXT (line 48):     this->DefaultMaxHierarchicalSearchNodes = 2048.00f;
  CONTEXT (line 49):     this->LedgeSlopeFilterMode = ENavigationLedgeSlopeFilterMode::Recast;
  REMOVED (line 50):     this->RegionPartitioning = ERecastPartitioning::ChunkyMonotone;
  ADDED   (line 50):     this->RegionPartitioning = ERecastPartitioning::Watershed;
  CONTEXT (line 51):     this->LayerPartitioning = ERecastPartitioning::Watershed;
  REMOVED (line 52):     this->RegionChunkSplits = 8;
  ADDED   (line 52):     this->RegionChunkSplits = 2;
  CONTEXT (line 53):     this->LayerChunkSplits = 2;
  CONTEXT (line 54):     this->bSortNavigationAreasByCost = true;
  CONTEXT (line 55):     this->bIsWorldPartitioned = false;