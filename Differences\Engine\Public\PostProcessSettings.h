DIFFERENCES IN: Engine\Public\PostProcessSettings.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\PostProcessSettings.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\PostProcessSettings.h
============================================================

SECTION starting at line 476 (left) / 476 (right):
----------------------------------------
  CONTEXT (line 476):     uint8 bOverride_IndirectLightingIntensity: 1;
  CONTEXT (line 477): 
  CONTEXT (line 478):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 479):     uint8 bOverride_IndirectLightingColorFar: 1;
  REMOVED (line 480): 
  REMOVED (line 481):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 482):     uint8 bOverride_IndirectLightingIntensityFar: 1;
  REMOVED (line 483): 
  REMOVED (line 484):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 485):     uint8 bOverride_IndirectLightingFarBegin: 1;
  REMOVED (line 486): 
  REMOVED (line 487):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 488):     uint8 bOverride_IndirectLightingFarEnd: 1;
  REMOVED (line 489): 
  REMOVED (line 490):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 491):     uint8 bOverride_IndirectLightingBrightnessThreshold: 1;
  REMOVED (line 492): 
  REMOVED (line 493):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 494):     uint8 bOverride_IndirectLightingBrightnessSmoothness: 1;
  REMOVED (line 495): 
  REMOVED (line 496):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 497):     uint8 bOverride_ColorGradingIntensity: 1;
  CONTEXT (line 498): 
  CONTEXT (line 499):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

SECTION starting at line 942 (left) / 924 (right):
----------------------------------------
  CONTEXT (line 942): 
  CONTEXT (line 943):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))
  CONTEXT (line 944):     float IndirectLightingIntensity;
  REMOVED (line 945): 
  REMOVED (line 946):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))
  REMOVED (line 947):     FLinearColor IndirectLightingColorFar;
  REMOVED (line 948): 
  REMOVED (line 949):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))
  REMOVED (line 950):     float IndirectLightingIntensityFar;
  REMOVED (line 951): 
  REMOVED (line 952):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))
  REMOVED (line 953):     float IndirectLightingFarBegin;
  REMOVED (line 954): 
  REMOVED (line 955):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))
  REMOVED (line 956):     float IndirectLightingFarEnd;
  REMOVED (line 957): 
  REMOVED (line 958):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))
  REMOVED (line 959):     float IndirectLightingBrightnessThreshold;
  REMOVED (line 960): 
  REMOVED (line 961):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))
  REMOVED (line 962):     float IndirectLightingBrightnessSmoothness;
  CONTEXT (line 963): 
  CONTEXT (line 964):     UPROPERTY(BlueprintReadWrite, EditAnywhere, Interp, meta=(AllowPrivateAccess=true))
  CONTEXT (line 965):     ELumenRayLightingModeOverride LumenRayLightingMode;