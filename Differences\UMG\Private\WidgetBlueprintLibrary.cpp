DIFFERENCES IN: UMG\Private\WidgetBlueprintLibrary.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\UMG\Private\WidgetBlueprintLibrary.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\UMG\Private\WidgetBlueprintLibrary.cpp
============================================================

SECTION starting at line 146 (left) / 146 (right):
----------------------------------------
  CONTEXT (line 146):     return FEventReply{};
  CONTEXT (line 147): }
  CONTEXT (line 148): 
  REMOVED (line 149): void UWidgetBlueprintLibrary::DrawTextFormatted(FPaintContext& Context, const FText& Text, FVector2D Position, UFont* Font, float FontSize, FName FontTypeFace, FLinearColor Tint) {
  REMOVED (line 150): }
  REMOVED (line 151): 
  REMOVED (line 152): void UWidgetBlueprintLibrary::DrawText(FPaintContext& Context, const FString& InString, FVector2D Position, FLinearColor Tint) {
  ADDED   (line 149): void UWidgetBlueprintLibrary::DrawTextFormatted(FPaintContext& Context, const FText& Text, FVector2D position, UFont* Font, float FontSize, FName FontTypeFace, FLinearColor Tint) {
  ADDED   (line 150): }
  ADDED   (line 151): 
  ADDED   (line 152): void UWidgetBlueprintLibrary::DrawText(FPaintContext& Context, const FString& InString, FVector2D position, FLinearColor Tint) {
  CONTEXT (line 153): }
  CONTEXT (line 154): 
  CONTEXT (line 155): void UWidgetBlueprintLibrary::DrawSpline(FPaintContext& Context, FVector2D Start, FVector2D StartDir, FVector2D End, FVector2D EndDir, FLinearColor Tint, float Thickness) {

SECTION starting at line 161 (left) / 161 (right):
----------------------------------------
  CONTEXT (line 161): void UWidgetBlueprintLibrary::DrawLine(FPaintContext& Context, FVector2D PositionA, FVector2D PositionB, FLinearColor Tint, bool bAntiAlias, float Thickness) {
  CONTEXT (line 162): }
  CONTEXT (line 163): 
  REMOVED (line 164): void UWidgetBlueprintLibrary::DrawBox(FPaintContext& Context, FVector2D Position, FVector2D Size, USlateBrushAsset* Brush, FLinearColor Tint) {
  ADDED   (line 164): void UWidgetBlueprintLibrary::DrawBox(FPaintContext& Context, FVector2D position, FVector2D Size, USlateBrushAsset* Brush, FLinearColor Tint) {
  CONTEXT (line 165): }
  CONTEXT (line 166): 
  CONTEXT (line 167): void UWidgetBlueprintLibrary::DismissAllMenus() {