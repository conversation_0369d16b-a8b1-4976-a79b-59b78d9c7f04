DIFFERENCES IN: Engine\Public\KismetSystemLibrary.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\KismetSystemLibrary.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\KismetSystemLibrary.h
============================================================

SECTION starting at line 191 (left) / 191 (right):
----------------------------------------
  CONTEXT (line 191):     static void SetBoolPropertyByName(UObject* Object, FName PropertyName, bool Value);
  CONTEXT (line 192): 
  CONTEXT (line 193):     UFUNCTION(BlueprintCallable, meta=(Latent, LatentInfo="LatentInfo", WorldContext="WorldContextObject"))
  REMOVED (line 194):     static void RetriggerableDelay(const UObject* WorldContextObject, float duration, FLatentActionInfo LatentInfo);
  ADDED   (line 194):     static void RetriggerableDelay(const UObject* WorldContextObject, float Duration, FLatentActionInfo LatentInfo);
  CONTEXT (line 195): 
  CONTEXT (line 196):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 197):     static void ResetGamepadAssignmentToController(int32 ControllerId);

SECTION starting at line 209 (left) / 209 (right):
----------------------------------------
  CONTEXT (line 209):     static void PrintWarning(const FString& InString);
  CONTEXT (line 210): 
  CONTEXT (line 211):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 212):     static void PrintText(const UObject* WorldContextObject, const FText InText, bool bPrintToScreen, bool bPrintToLog, FLinearColor TextColor, float duration, const FName Key);
  REMOVED (line 213): 
  REMOVED (line 214):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 215):     static void PrintString(const UObject* WorldContextObject, const FString& InString, bool bPrintToScreen, bool bPrintToLog, FLinearColor TextColor, float duration, const FName Key);
  ADDED   (line 212):     static void PrintText(const UObject* WorldContextObject, const FText InText, bool bPrintToScreen, bool bPrintToLog, FLinearColor TextColor, float Duration, const FName Key);
  ADDED   (line 213): 
  ADDED   (line 214):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 215):     static void PrintString(const UObject* WorldContextObject, const FString& InString, bool bPrintToScreen, bool bPrintToLog, FLinearColor TextColor, float Duration, const FName Key);
  CONTEXT (line 216): 
  CONTEXT (line 217):     UFUNCTION(BlueprintCallable, BlueprintPure)
  CONTEXT (line 218):     static bool ParseParamValue(const FString& InString, const FString& InParam, FString& OutValue);

SECTION starting at line 683 (left) / 683 (right):
----------------------------------------
  CONTEXT (line 683):     static int32 EndTransaction();
  CONTEXT (line 684): 
  CONTEXT (line 685):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 686):     static void DrawDebugString(const UObject* WorldContextObject, const FVector TextLocation, const FString& Text, AActor* TestBaseActor, FLinearColor TextColor, float duration);
  REMOVED (line 687): 
  REMOVED (line 688):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 689):     static void DrawDebugSphere(const UObject* WorldContextObject, const FVector Center, float Radius, int32 Segments, FLinearColor LineColor, float duration, float Thickness);
  REMOVED (line 690): 
  REMOVED (line 691):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 692):     static void DrawDebugPoint(const UObject* WorldContextObject, const FVector Position, float Size, FLinearColor PointColor, float duration);
  REMOVED (line 693): 
  REMOVED (line 694):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 695):     static void DrawDebugPlane(const UObject* WorldContextObject, const FPlane& PlaneCoordinates, const FVector Location, float Size, FLinearColor PlaneColor, float duration);
  REMOVED (line 696): 
  REMOVED (line 697):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 698):     static void DrawDebugLine(const UObject* WorldContextObject, const FVector LineStart, const FVector LineEnd, FLinearColor LineColor, float duration, float Thickness);
  REMOVED (line 699): 
  REMOVED (line 700):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 701):     static void DrawDebugFrustum(const UObject* WorldContextObject, const FTransform& FrustumTransform, FLinearColor FrustumColor, float duration, float Thickness);
  REMOVED (line 702): 
  REMOVED (line 703):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 704):     static void DrawDebugFloatHistoryTransform(const UObject* WorldContextObject, const FDebugFloatHistory& FloatHistory, const FTransform& DrawTransform, FVector2D DrawSize, FLinearColor DrawColor, float duration);
  REMOVED (line 705): 
  REMOVED (line 706):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 707):     static void DrawDebugFloatHistoryLocation(const UObject* WorldContextObject, const FDebugFloatHistory& FloatHistory, FVector DrawLocation, FVector2D DrawSize, FLinearColor DrawColor, float duration);
  REMOVED (line 708): 
  REMOVED (line 709):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 710):     static void DrawDebugCylinder(const UObject* WorldContextObject, const FVector Start, const FVector End, float Radius, int32 Segments, FLinearColor LineColor, float duration, float Thickness);
  REMOVED (line 711): 
  REMOVED (line 712):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 713):     static void DrawDebugCoordinateSystem(const UObject* WorldContextObject, const FVector AxisLoc, const FRotator AxisRot, float Scale, float duration, float Thickness);
  REMOVED (line 714): 
  REMOVED (line 715):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 716):     static void DrawDebugConeInDegrees(const UObject* WorldContextObject, const FVector Origin, const FVector Direction, float Length, float AngleWidth, float AngleHeight, int32 NumSides, FLinearColor LineColor, float duration, float Thickness);
  REMOVED (line 717): 
  REMOVED (line 718):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 719):     static void DrawDebugCone(const UObject* WorldContextObject, const FVector Origin, const FVector Direction, float Length, float AngleWidth, float AngleHeight, int32 NumSides, FLinearColor LineColor, float duration, float Thickness);
  REMOVED (line 720): 
  REMOVED (line 721):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 722):     static void DrawDebugCircle(const UObject* WorldContextObject, FVector Center, float Radius, int32 NumSegments, FLinearColor LineColor, float duration, float Thickness, FVector YAxis, FVector ZAxis, bool bDrawAxis);
  REMOVED (line 723): 
  REMOVED (line 724):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 725):     static void DrawDebugCapsule(const UObject* WorldContextObject, const FVector Center, float HalfHeight, float Radius, const FRotator Rotation, FLinearColor LineColor, float duration, float Thickness);
  REMOVED (line 726): 
  REMOVED (line 727):     UFUNCTION(BlueprintCallable)
  REMOVED (line 728):     static void DrawDebugCamera(const ACameraActor* CameraActor, FLinearColor CameraColor, float duration);
  REMOVED (line 729): 
  REMOVED (line 730):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 731):     static void DrawDebugBox(const UObject* WorldContextObject, const FVector Center, FVector Extent, FLinearColor LineColor, const FRotator Rotation, float duration, float Thickness);
  REMOVED (line 732): 
  REMOVED (line 733):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 734):     static void DrawDebugArrow(const UObject* WorldContextObject, const FVector LineStart, const FVector LineEnd, float ArrowSize, FLinearColor LineColor, float duration, float Thickness);
  ADDED   (line 686):     static void DrawDebugString(const UObject* WorldContextObject, const FVector TextLocation, const FString& Text, AActor* TestBaseActor, FLinearColor TextColor, float Duration);
  ADDED   (line 687): 
  ADDED   (line 688):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 689):     static void DrawDebugSphere(const UObject* WorldContextObject, const FVector Center, float Radius, int32 Segments, FLinearColor LineColor, float Duration, float Thickness);
  ADDED   (line 690): 
  ADDED   (line 691):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 692):     static void DrawDebugPoint(const UObject* WorldContextObject, const FVector position, float Size, FLinearColor PointColor, float Duration);
  ADDED   (line 693): 
  ADDED   (line 694):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 695):     static void DrawDebugPlane(const UObject* WorldContextObject, const FPlane& PlaneCoordinates, const FVector Location, float Size, FLinearColor PlaneColor, float Duration);
  ADDED   (line 696): 
  ADDED   (line 697):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 698):     static void DrawDebugLine(const UObject* WorldContextObject, const FVector LineStart, const FVector LineEnd, FLinearColor LineColor, float Duration, float Thickness);
  ADDED   (line 699): 
  ADDED   (line 700):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 701):     static void DrawDebugFrustum(const UObject* WorldContextObject, const FTransform& FrustumTransform, FLinearColor FrustumColor, float Duration, float Thickness);
  ADDED   (line 702): 
  ADDED   (line 703):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 704):     static void DrawDebugFloatHistoryTransform(const UObject* WorldContextObject, const FDebugFloatHistory& FloatHistory, const FTransform& DrawTransform, FVector2D DrawSize, FLinearColor DrawColor, float Duration);
  ADDED   (line 705): 
  ADDED   (line 706):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 707):     static void DrawDebugFloatHistoryLocation(const UObject* WorldContextObject, const FDebugFloatHistory& FloatHistory, FVector DrawLocation, FVector2D DrawSize, FLinearColor DrawColor, float Duration);
  ADDED   (line 708): 
  ADDED   (line 709):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 710):     static void DrawDebugCylinder(const UObject* WorldContextObject, const FVector Start, const FVector End, float Radius, int32 Segments, FLinearColor LineColor, float Duration, float Thickness);
  ADDED   (line 711): 
  ADDED   (line 712):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 713):     static void DrawDebugCoordinateSystem(const UObject* WorldContextObject, const FVector AxisLoc, const FRotator AxisRot, float Scale, float Duration, float Thickness);
  ADDED   (line 714): 
  ADDED   (line 715):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 716):     static void DrawDebugConeInDegrees(const UObject* WorldContextObject, const FVector Origin, const FVector Direction, float Length, float AngleWidth, float AngleHeight, int32 NumSides, FLinearColor LineColor, float Duration, float Thickness);
  ADDED   (line 717): 
  ADDED   (line 718):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 719):     static void DrawDebugCone(const UObject* WorldContextObject, const FVector Origin, const FVector Direction, float Length, float AngleWidth, float AngleHeight, int32 NumSides, FLinearColor LineColor, float Duration, float Thickness);
  ADDED   (line 720): 
  ADDED   (line 721):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 722):     static void DrawDebugCircle(const UObject* WorldContextObject, FVector Center, float Radius, int32 NumSegments, FLinearColor LineColor, float Duration, float Thickness, FVector YAxis, FVector ZAxis, bool bDrawAxis);
  ADDED   (line 723): 
  ADDED   (line 724):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 725):     static void DrawDebugCapsule(const UObject* WorldContextObject, const FVector Center, float HalfHeight, float Radius, const FRotator Rotation, FLinearColor LineColor, float Duration, float Thickness);
  ADDED   (line 726): 
  ADDED   (line 727):     UFUNCTION(BlueprintCallable)
  ADDED   (line 728):     static void DrawDebugCamera(const ACameraActor* CameraActor, FLinearColor CameraColor, float Duration);
  ADDED   (line 729): 
  ADDED   (line 730):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 731):     static void DrawDebugBox(const UObject* WorldContextObject, const FVector Center, FVector Extent, FLinearColor LineColor, const FRotator Rotation, float Duration, float Thickness);
  ADDED   (line 732): 
  ADDED   (line 733):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 734):     static void DrawDebugArrow(const UObject* WorldContextObject, const FVector LineStart, const FVector LineEnd, float ArrowSize, FLinearColor LineColor, float Duration, float Thickness);
  CONTEXT (line 735): 
  CONTEXT (line 736):     UFUNCTION(BlueprintCallable, BlueprintPure)
  CONTEXT (line 737):     static bool DoesImplementInterface(const UObject* TestObject, TSubclassOf<UInterface> Interface);

SECTION starting at line 743 (left) / 743 (right):
----------------------------------------
  CONTEXT (line 743):     static void DelayUntilNextTick(const UObject* WorldContextObject, FLatentActionInfo LatentInfo);
  CONTEXT (line 744): 
  CONTEXT (line 745):     UFUNCTION(BlueprintCallable, meta=(Latent, LatentInfo="LatentInfo", WorldContext="WorldContextObject"))
  REMOVED (line 746):     static void Delay(const UObject* WorldContextObject, float duration, FLatentActionInfo LatentInfo);
  ADDED   (line 746):     static void Delay(const UObject* WorldContextObject, float Duration, FLatentActionInfo LatentInfo);
  CONTEXT (line 747): 
  CONTEXT (line 748):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 749):     static void CreateCopyForUndoBuffer(UObject* ObjectToModify);

SECTION starting at line 854 (left) / 854 (right):
----------------------------------------
  CONTEXT (line 854):     static void BreakARFilter(FARFilter InARFilter, TArray<FName>& PackageNames, TArray<FName>& PackagePaths, TArray<FSoftObjectPath>& SoftObjectPaths, TArray<FTopLevelAssetPath>& ClassPaths, TSet<FTopLevelAssetPath>& RecursiveClassPathsExclusionSet, TArray<FName>& ClassNames, TSet<FName>& RecursiveClassesExclusionSet, bool& bRecursivePaths, bool& bRecursiveClasses, bool& bIncludeOnlyOnDiskAssets);
  CONTEXT (line 855): 
  CONTEXT (line 856):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 857):     static bool BoxTraceSingleForObjects(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator orientation, const TArray<TEnumAsByte<EObjectTypeQuery>>& ObjectTypes, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);
  REMOVED (line 858): 
  REMOVED (line 859):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 860):     static bool BoxTraceSingleByProfile(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator orientation, FName ProfileName, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);
  REMOVED (line 861): 
  REMOVED (line 862):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 863):     static bool BoxTraceSingle(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator orientation, TEnumAsByte<ETraceTypeQuery> TraceChannel, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);
  REMOVED (line 864): 
  REMOVED (line 865):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 866):     static bool BoxTraceMultiForObjects(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator orientation, const TArray<TEnumAsByte<EObjectTypeQuery>>& ObjectTypes, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);
  REMOVED (line 867): 
  REMOVED (line 868):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 869):     static bool BoxTraceMultiByProfile(const UObject* WorldContextObject, const FVector Start, const FVector End, FVector HalfSize, const FRotator orientation, FName ProfileName, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);
  REMOVED (line 870): 
  REMOVED (line 871):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  REMOVED (line 872):     static bool BoxTraceMulti(const UObject* WorldContextObject, const FVector Start, const FVector End, FVector HalfSize, const FRotator orientation, TEnumAsByte<ETraceTypeQuery> TraceChannel, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);
  ADDED   (line 857):     static bool BoxTraceSingleForObjects(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator Orientation, const TArray<TEnumAsByte<EObjectTypeQuery>>& ObjectTypes, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);
  ADDED   (line 858): 
  ADDED   (line 859):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 860):     static bool BoxTraceSingleByProfile(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator Orientation, FName ProfileName, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);
  ADDED   (line 861): 
  ADDED   (line 862):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 863):     static bool BoxTraceSingle(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator Orientation, TEnumAsByte<ETraceTypeQuery> TraceChannel, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, FHitResult& OutHit, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);
  ADDED   (line 864): 
  ADDED   (line 865):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 866):     static bool BoxTraceMultiForObjects(const UObject* WorldContextObject, const FVector Start, const FVector End, const FVector HalfSize, const FRotator Orientation, const TArray<TEnumAsByte<EObjectTypeQuery>>& ObjectTypes, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);
  ADDED   (line 867): 
  ADDED   (line 868):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 869):     static bool BoxTraceMultiByProfile(const UObject* WorldContextObject, const FVector Start, const FVector End, FVector HalfSize, const FRotator Orientation, FName ProfileName, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);
  ADDED   (line 870): 
  ADDED   (line 871):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  ADDED   (line 872):     static bool BoxTraceMulti(const UObject* WorldContextObject, const FVector Start, const FVector End, FVector HalfSize, const FRotator Orientation, TEnumAsByte<ETraceTypeQuery> TraceChannel, bool bTraceComplex, const TArray<AActor*>& ActorsToIgnore, TEnumAsByte<EDrawDebugTrace::Type> DrawDebugType, TArray<FHitResult>& OutHits, bool bIgnoreSelf, FLinearColor TraceColor, FLinearColor TraceHitColor, float DrawTime);
  CONTEXT (line 873): 
  CONTEXT (line 874):     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContextObject"))
  CONTEXT (line 875):     static bool BoxOverlapComponents(const UObject* WorldContextObject, const FVector BoxPos, FVector Extent, const TArray<TEnumAsByte<EObjectTypeQuery>>& ObjectTypes, UClass* ComponentClassFilter, const TArray<AActor*>& ActorsToIgnore, TArray<UPrimitiveComponent*>& OutComponents);