--- Left: GeometryCollectionComponent.h
+++ Right: GeometryCollectionComponent.h
@@ -442,7 +442,7 @@
     void ApplyLinearVelocity(int32 ItemIndex, const FVector& LinearVelocity);

     

     UFUNCTION(BlueprintCallable)

-    void ApplyKinematicField(float Radius, FVector Position);

+    void ApplyKinematicField(float Radius, FVector position);

     

     UFUNCTION(BlueprintCallable)

     void ApplyInternalStrain(int32 ItemIndex, const FVector& Location, float Radius, int32 PropagationDepth, float PropagationFactor, float Strain);
