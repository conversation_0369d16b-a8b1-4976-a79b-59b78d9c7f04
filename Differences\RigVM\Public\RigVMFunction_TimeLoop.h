DIFFERENCES IN: RigVM\Public\RigVMFunction_TimeLoop.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\RigVM\Public\RigVMFunction_TimeLoop.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\RigVM\Public\RigVMFunction_TimeLoop.h
============================================================

SECTION starting at line 11 (left) / 11 (right):
----------------------------------------
  CONTEXT (line 11):     float Speed;
  CONTEXT (line 12): 
  CONTEXT (line 13):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 14):     float duration;
  ADDED   (line 14):     float Duration;
  CONTEXT (line 15): 
  CONTEXT (line 16):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 17):     bool Normalize;