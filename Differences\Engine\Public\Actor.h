DIFFERENCES IN: Engine\Public\Actor.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\Actor.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\Actor.h
============================================================

SECTION starting at line 140 (left) / 140 (right):
----------------------------------------
  CONTEXT (line 140):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 141):     uint8 bIsEditorOnlyActor: 1;
  CONTEXT (line 142): 
  REMOVED (line 143):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 144):     uint8 bIsCookedForMobile: 1;
  REMOVED (line 145): 
  REMOVED (line 146):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 147):     uint8 bIsCookedForDesktop: 1;
  REMOVED (line 148): 
  REMOVED (line 149):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 150):     uint8 bShouldAllPrimitivesBeExcludedFromLightmaps: 1;
  REMOVED (line 151): 
  CONTEXT (line 152): protected:
  CONTEXT (line 153):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 154):     uint8 bReplicates: 1;