DIFFERENCES IN: LiveLink\Private\LiveLinkMessageBusFinder.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\LiveLink\Private\LiveLinkMessageBusFinder.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\LiveLink\Private\LiveLinkMessageBusFinder.cpp
============================================================

SECTION starting at line 3 (left) / 3 (right):
----------------------------------------
  CONTEXT (line 3): ULiveLinkMessageBusFinder::ULiveLinkMessageBusFinder() {
  CONTEXT (line 4): }
  CONTEXT (line 5): 
  REMOVED (line 6): void ULiveLinkMessageBusFinder::GetAvailableProviders(UObject* WorldContextObject, FLatentActionInfo LatentInfo, float duration, TArray<FProviderPollResult>& AvailableProviders) {
  ADDED   (line 6): void ULiveLinkMessageBusFinder::GetAvailableProviders(UObject* WorldContextObject, FLatentActionInfo LatentInfo, float Duration, TArray<FProviderPollResult>& AvailableProviders) {
  CONTEXT (line 7): }
  CONTEXT (line 8): 
  CONTEXT (line 9): ULiveLinkMessageBusFinder* ULiveLinkMessageBusFinder::ConstructMessageBusFinder() {