DIFFERENCES IN: Engine\Private\PlayerCameraManager.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\PlayerCameraManager.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\PlayerCameraManager.cpp
============================================================

SECTION starting at line 60 (left) / 60 (right):
----------------------------------------
  CONTEXT (line 60):     return NULL;
  CONTEXT (line 61): }
  CONTEXT (line 62): 
  REMOVED (line 63): void APlayerCameraManager::StartCameraFade(float FromAlpha, float ToAlpha, float duration, FLinearColor Color, bool bShouldFadeAudio, bool bHoldWhenFinished) {
  ADDED   (line 63): void APlayerCameraManager::StartCameraFade(float FromAlpha, float ToAlpha, float Duration, FLinearColor Color, bool bShouldFadeAudio, bool bHoldWhenFinished) {
  CONTEXT (line 64): }
  CONTEXT (line 65): 
  CONTEXT (line 66): void APlayerCameraManager::SetManualCameraFade(float InFadeAmount, FLinearColor Color, bool bInFadeAudio) {