DIFFERENCES IN: MediaAssets\Private\MediaPlayer.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MediaAssets\Private\MediaPlayer.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MediaAssets\Private\MediaPlayer.cpp
============================================================

SECTION starting at line 65 (left) / 65 (right):
----------------------------------------
  CONTEXT (line 65):     return false;
  CONTEXT (line 66): }
  CONTEXT (line 67): 
  REMOVED (line 68): void UMediaPlayer::SetDesiredPlayerName(FName playerName) {
  ADDED   (line 68): void UMediaPlayer::SetDesiredPlayerName(FName PlayerName) {
  CONTEXT (line 69): }
  CONTEXT (line 70): 
  CONTEXT (line 71): void UMediaPlayer::SetBlockOnTime(const FTimespan& Time) {