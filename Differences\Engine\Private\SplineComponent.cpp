DIFFERENCES IN: Engine\Private\SplineComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\SplineComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\SplineComponent.cpp
============================================================

SECTION starting at line 3 (left) / 3 (right):
----------------------------------------
  CONTEXT (line 3): 
  CONTEXT (line 4): USplineComponent::USplineComponent(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {
  CONTEXT (line 5):     this->ReparamStepsPerSegment = 10;
  REMOVED (line 6):     this->duration = 1.00f;
  ADDED   (line 6):     this->Duration = 1.00f;
  CONTEXT (line 7):     this->bStationaryEndpoints = false;
  CONTEXT (line 8):     this->bSplineHasBeenEdited = false;
  CONTEXT (line 9):     this->bModifiedByConstructionScript = false;

SECTION starting at line 410 (left) / 410 (right):
----------------------------------------
  CONTEXT (line 410): void USplineComponent::ClearSplinePoints(bool bUpdateSpline) {
  CONTEXT (line 411): }
  CONTEXT (line 412): 
  REMOVED (line 413): void USplineComponent::AddSplineWorldPoint(const FVector& Position) {
  REMOVED (line 414): }
  REMOVED (line 415): 
  REMOVED (line 416): void USplineComponent::AddSplinePointAtIndex(const FVector& Position, int32 Index, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline) {
  REMOVED (line 417): }
  REMOVED (line 418): 
  REMOVED (line 419): void USplineComponent::AddSplinePoint(const FVector& Position, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline) {
  REMOVED (line 420): }
  REMOVED (line 421): 
  REMOVED (line 422): void USplineComponent::AddSplineLocalPoint(const FVector& Position) {
  ADDED   (line 413): void USplineComponent::AddSplineWorldPoint(const FVector& position) {
  ADDED   (line 414): }
  ADDED   (line 415): 
  ADDED   (line 416): void USplineComponent::AddSplinePointAtIndex(const FVector& position, int32 Index, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline) {
  ADDED   (line 417): }
  ADDED   (line 418): 
  ADDED   (line 419): void USplineComponent::AddSplinePoint(const FVector& position, TEnumAsByte<ESplineCoordinateSpace::Type> CoordinateSpace, bool bUpdateSpline) {
  ADDED   (line 420): }
  ADDED   (line 421): 
  ADDED   (line 422): void USplineComponent::AddSplineLocalPoint(const FVector& position) {
  CONTEXT (line 423): }
  CONTEXT (line 424): 
  CONTEXT (line 425): void USplineComponent::AddPoints(const TArray<FSplinePoint>& Points, bool bUpdateSpline) {