DIFFERENCES IN: ChaosNiagara\Public\ChaosDestructionEvent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\ChaosNiagara\Public\ChaosDestructionEvent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\ChaosNiagara\Public\ChaosDestructionEvent.h
============================================================

SECTION starting at line 8 (left) / 8 (right):
----------------------------------------
  CONTEXT (line 8):     GENERATED_BODY()
  CONTEXT (line 9): public:
  CONTEXT (line 10):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 11):     FVector Position;
  ADDED   (line 11):     FVector position;
  CONTEXT (line 12): 
  CONTEXT (line 13):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 14):     FVector Normal;