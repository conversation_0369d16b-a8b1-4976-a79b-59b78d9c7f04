DIFFERENCES IN: Engine\Private\Actor.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\Actor.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\Actor.cpp
============================================================

SECTION starting at line 28 (left) / 28 (right):
----------------------------------------
  CONTEXT (line 28):     this->bIgnoresOriginShifting = false;
  CONTEXT (line 29):     this->bEnableAutoLODGeneration = true;
  CONTEXT (line 30):     this->bIsEditorOnlyActor = false;
  REMOVED (line 31):     this->bIsCookedForMobile = true;
  REMOVED (line 32):     this->bIsCookedForDesktop = true;
  REMOVED (line 33):     this->bShouldAllPrimitivesBeExcludedFromLightmaps = false;
  CONTEXT (line 34):     this->bReplicates = false;
  CONTEXT (line 35):     this->bCanBeInCluster = false;
  CONTEXT (line 36):     this->bAllowReceiveTickEventOnDedicatedServer = true;