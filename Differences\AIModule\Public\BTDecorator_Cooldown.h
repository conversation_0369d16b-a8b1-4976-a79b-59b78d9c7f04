DIFFERENCES IN: AIModule\Public\BTDecorator_Cooldown.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AIModule\Public\BTDecorator_Cooldown.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AIModule\Public\BTDecorator_Cooldown.h
============================================================

SECTION starting at line 9 (left) / 9 (right):
----------------------------------------
  CONTEXT (line 9):     GENERATED_BODY()
  CONTEXT (line 10): public:
  CONTEXT (line 11):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 12):     FValueOrBBKey_Float CooldownTime;
  ADDED   (line 12):     FValueOrBBKey_Float CoolDownTime;
  CONTEXT (line 13): 
  CONTEXT (line 14):     UBTDecorator_Cooldown();
  CONTEXT (line 15): 