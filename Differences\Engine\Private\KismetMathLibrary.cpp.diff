--- Left: KismetMathLibrary.cpp
+++ Right: KismetMathLibrary.cpp
@@ -1213,7 +1213,7 @@
 void UKismetMathLibrary::MinAreaRectangle(UObject* WorldContextObject, const TArray<FVector>& InPoints, const FVector& SampleSurfaceNormal, FVector& OutRectCenter, FRotator& OutRectRotation, float& OutRectLengthX, float& OutRectLengthY, bool bDebugDraw) {

 }

 

-int32 UKismetMathLibrary::Min(int32 A, int32 B) {

+int32 UKismetMathLibrary::min(int32 A, int32 B) {

     return 0;

 }

 

@@ -1233,7 +1233,7 @@
     return 0;

 }

 

-int32 UKismetMathLibrary::Max(int32 A, int32 B) {

+int32 UKismetMathLibrary::max(int32 A, int32 B) {

     return 0;

 }

 
