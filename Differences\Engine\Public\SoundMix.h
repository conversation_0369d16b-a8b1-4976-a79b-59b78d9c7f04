DIFFERENCES IN: Engine\Public\SoundMix.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\SoundMix.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\SoundMix.h
============================================================

SECTION starting at line 28 (left) / 28 (right):
----------------------------------------
  CONTEXT (line 28):     float FadeInTime;
  CONTEXT (line 29): 
  CONTEXT (line 30):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 31):     float duration;
  ADDED   (line 31):     float Duration;
  CONTEXT (line 32): 
  CONTEXT (line 33):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 34):     float FadeOutTime;