--- Left: TextureEncodingProjectSettings.cpp
+++ Right: TextureEncodingProjectSettings.cpp
@@ -2,16 +2,16 @@
 

 UTextureEncodingProjectSettings::UTextureEncodingProjectSettings() {

     this->bSharedLinearTextureEncoding = false;

-    this->bFinalUsesRDO = true;

-    this->FinalRDOLambda = 45;

-    this->FinalEffortLevel = ETextureEncodeEffort::High;

+    this->bFinalUsesRDO = false;

+    this->FinalRDOLambda = 30;

+    this->FinalEffortLevel = ETextureEncodeEffort::Normal;

     this->FinalUniversalTiling = ETextureUniversalTiling::Disabled;

     this->bFastUsesRDO = false;

-    this->FastRDOLambda = 45;

+    this->FastRDOLambda = 30;

     this->FastEffortLevel = ETextureEncodeEffort::Normal;

     this->FastUniversalTiling = ETextureUniversalTiling::Disabled;

     this->CookUsesSpeed = ETextureEncodeSpeed::Final;

-    this->EditorUsesSpeed = ETextureEncodeSpeed::Fast;

+    this->EditorUsesSpeed = ETextureEncodeSpeed::FinalIfAvailable;

 }

 

 
