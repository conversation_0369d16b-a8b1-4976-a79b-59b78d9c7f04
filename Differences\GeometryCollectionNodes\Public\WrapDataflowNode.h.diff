--- Left: WrapDataflowNode.h
+++ Right: WrapDataflowNode.h
@@ -11,10 +11,10 @@
     float float;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float Min;

+    float min;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    float Max;

+    float max;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     float ReturnValue;
