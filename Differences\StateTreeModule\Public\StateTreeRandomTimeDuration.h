DIFFERENCES IN: StateTreeModule\Public\StateTreeRandomTimeDuration.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\StateTreeModule\Public\StateTreeRandomTimeDuration.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\StateTreeModule\Public\StateTreeRandomTimeDuration.h
============================================================

SECTION starting at line 8 (left) / 8 (right):
----------------------------------------
  CONTEXT (line 8): public:
  CONTEXT (line 9): protected:
  CONTEXT (line 10):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 11):     uint16 duration;
  ADDED   (line 11):     uint16 Duration;
  CONTEXT (line 12): 
  CONTEXT (line 13):     UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 14):     uint16 RandomVariance;