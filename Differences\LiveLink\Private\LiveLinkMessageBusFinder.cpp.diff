--- Left: LiveLinkMessageBusFinder.cpp
+++ Right: LiveLinkMessageBusFinder.cpp
@@ -3,7 +3,7 @@
 ULiveLinkMessageBusFinder::ULiveLinkMessageBusFinder() {

 }

 

-void ULiveLinkMessageBusFinder::GetAvailableProviders(UObject* WorldContextObject, FLatentActionInfo LatentInfo, float duration, TArray<FProviderPollResult>& AvailableProviders) {

+void ULiveLinkMessageBusFinder::GetAvailableProviders(UObject* WorldContextObject, FLatentActionInfo LatentInfo, float Duration, TArray<FProviderPollResult>& AvailableProviders) {

 }

 

 ULiveLinkMessageBusFinder* ULiveLinkMessageBusFinder::ConstructMessageBusFinder() {
