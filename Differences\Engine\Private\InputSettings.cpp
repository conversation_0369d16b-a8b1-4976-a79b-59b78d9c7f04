DIFFERENCES IN: Engine\Private\InputSettings.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\InputSettings.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\InputSettings.cpp
============================================================

SECTION starting at line 1 (left) / 1 (right):
----------------------------------------
  CONTEXT (line 1): #include "InputSettings.h"
  CONTEXT (line 2): 
  CONTEXT (line 3): UInputSettings::UInputSettings() {
  REMOVED (line 4):     this->AxisConfig.AddDefaulted(83);
  ADDED   (line 4):     this->AxisConfig.AddDefaulted(77);
  CONTEXT (line 5):     this->bAltEnterTogglesFullscreen = true;
  CONTEXT (line 6):     this->bF11TogglesFullscreen = true;
  CONTEXT (line 7):     this->bUseMouseForTouch = false;