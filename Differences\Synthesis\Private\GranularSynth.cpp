DIFFERENCES IN: Synthesis\Private\GranularSynth.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Synthesis\Private\GranularSynth.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Synthesis\Private\GranularSynth.cpp
============================================================

SECTION starting at line 49 (left) / 49 (right):
----------------------------------------
  CONTEXT (line 49): void UGranularSynth::SetAttackTime(const float AttackTimeMsec) {
  CONTEXT (line 50): }
  CONTEXT (line 51): 
  REMOVED (line 52): void UGranularSynth::NoteOn(const float Note, const int32 Velocity, const float duration) {
  ADDED   (line 52): void UGranularSynth::NoteOn(const float Note, const int32 Velocity, const float Duration) {
  CONTEXT (line 53): }
  CONTEXT (line 54): 
  CONTEXT (line 55): void UGranularSynth::NoteOff(const float Note, const bool bKill) {