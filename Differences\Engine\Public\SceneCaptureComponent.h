DIFFERENCES IN: Engine\Public\SceneCaptureComponent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\SceneCaptureComponent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\SceneCaptureComponent.h
============================================================

SECTION starting at line 25 (left) / 25 (right):
----------------------------------------
  CONTEXT (line 25):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 26):     uint8 bCaptureOnMovement: 1;
  CONTEXT (line 27): 
  REMOVED (line 28):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 29):     uint8 bShowEditorWidget: 1;
  REMOVED (line 30): 
  CONTEXT (line 31):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, DuplicateTransient, EditAnywhere, NonTransactional, SkipSerialization, Transient, meta=(AllowPrivateAccess=true))
  CONTEXT (line 32):     uint8 bCaptureGpuNextRender: 1;
  CONTEXT (line 33): 

SECTION starting at line 51 (left) / 48 (right):
----------------------------------------
  CONTEXT (line 51): 
  CONTEXT (line 52):     UPROPERTY(AdvancedDisplay, BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 53):     float LODDistanceFactor;
  REMOVED (line 54): 
  REMOVED (line 55):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 56):     float ViewDistanceScaleOverride;
  CONTEXT (line 57): 
  CONTEXT (line 58):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 59):     float MaxViewDistanceOverride;