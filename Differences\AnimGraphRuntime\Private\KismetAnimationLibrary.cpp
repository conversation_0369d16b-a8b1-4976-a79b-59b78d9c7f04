DIFFERENCES IN: AnimGraphRuntime\Private\KismetAnimationLibrary.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AnimGraphRuntime\Private\KismetAnimationLibrary.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AnimGraphRuntime\Private\KismetAnimationLibrary.cpp
============================================================

SECTION starting at line 37 (left) / 37 (right):
----------------------------------------
  CONTEXT (line 37):     return 0.0f;
  CONTEXT (line 38): }
  CONTEXT (line 39): 
  REMOVED (line 40): float UKismetAnimationLibrary::K2_CalculateVelocityFromPositionHistory(float DeltaSeconds, FVector Position, FPositionHistory& History, int32 NumberOfSamples, float VelocityMin, float VelocityMax) {
  ADDED   (line 40): float UKismetAnimationLibrary::K2_CalculateVelocityFromPositionHistory(float DeltaSeconds, FVector position, FPositionHistory& History, int32 NumberOfSamples, float VelocityMin, float VelocityMax) {
  CONTEXT (line 41):     return 0.0f;
  CONTEXT (line 42): }
  CONTEXT (line 43): 