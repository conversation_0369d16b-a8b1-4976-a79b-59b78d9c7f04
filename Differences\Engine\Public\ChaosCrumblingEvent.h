DIFFERENCES IN: Engine\Public\ChaosCrumblingEvent.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\ChaosCrumblingEvent.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\ChaosCrumblingEvent.h
============================================================

SECTION starting at line 18 (left) / 18 (right):
----------------------------------------
  CONTEXT (line 18):     FVector Location;
  CONTEXT (line 19): 
  CONTEXT (line 20):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 21):     FQuat orientation;
  ADDED   (line 21):     FQuat Orientation;
  CONTEXT (line 22): 
  CONTEXT (line 23):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 24):     FVector LinearVelocity;