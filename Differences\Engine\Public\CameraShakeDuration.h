DIFFERENCES IN: Engine\Public\CameraShakeDuration.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\CameraShakeDuration.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\CameraShakeDuration.h
============================================================

SECTION starting at line 9 (left) / 9 (right):
----------------------------------------
  CONTEXT (line 9): public:
  CONTEXT (line 10): private:
  CONTEXT (line 11):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 12):     float duration;
  ADDED   (line 12):     float Duration;
  CONTEXT (line 13): 
  CONTEXT (line 14):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 15):     ECameraShakeDurationType Type;