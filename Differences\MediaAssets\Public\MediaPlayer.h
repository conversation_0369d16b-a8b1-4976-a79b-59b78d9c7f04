DIFFERENCES IN: MediaAssets\Public\MediaPlayer.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\MediaAssets\Public\MediaPlayer.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\MediaAssets\Public\MediaPlayer.h
============================================================

SECTION starting at line 142 (left) / 142 (right):
----------------------------------------
  CONTEXT (line 142):     bool SetLooping(bool Looping);
  CONTEXT (line 143): 
  CONTEXT (line 144):     UFUNCTION(BlueprintCallable)
  REMOVED (line 145):     void SetDesiredPlayerName(FName playerName);
  ADDED   (line 145):     void SetDesiredPlayerName(FName PlayerName);
  CONTEXT (line 146): 
  CONTEXT (line 147):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 148):     void SetBlockOnTime(const FTimespan& Time);