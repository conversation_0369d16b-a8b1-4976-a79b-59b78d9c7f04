--- Left: SceneCaptureComponent.cpp
+++ Right: SceneCaptureComponent.cpp
@@ -5,12 +5,10 @@
     this->CaptureSource = SCS_SceneColorHDR;

     this->bCaptureEveryFrame = true;

     this->bCaptureOnMovement = true;

-    this->bShowEditorWidget = true;

     this->bCaptureGpuNextRender = false;

     this->bDumpGpuNextRender = false;

     this->bAlwaysPersistRenderingState = false;

     this->LODDistanceFactor = 1.00f;

-    this->ViewDistanceScaleOverride = -1.00f;

     this->MaxViewDistanceOverride = -1.00f;

     this->CaptureSortPriority = 0;

     this->bUseRayTracingIfEnabled = false;
