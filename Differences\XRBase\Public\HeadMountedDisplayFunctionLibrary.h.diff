--- Left: HeadMountedDisplayFunctionLibrary.h
+++ Right: HeadMountedDisplayFunctionLibrary.h
@@ -141,16 +141,16 @@
     static void GetHandTrackingState(UObject* WorldContext, const EXRSpaceType XRSpaceType, const EControllerHand Hand, FXRHandTrackingState& HandTrackingState);

     

     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContext"))

-    static void GetDeviceWorldPose(UObject* WorldContext, const FXRDeviceId& XRDeviceId, bool& bIsTracked, FRotator& orientation, bool& bHasPositionalTracking, FVector& Position);

+    static void GetDeviceWorldPose(UObject* WorldContext, const FXRDeviceId& XRDeviceId, bool& bIsTracked, FRotator& Orientation, bool& bHasPositionalTracking, FVector& position);

     

     UFUNCTION(BlueprintCallable)

-    static void GetDevicePose(const FXRDeviceId& XRDeviceId, bool& bIsTracked, FRotator& orientation, bool& bHasPositionalTracking, FVector& Position);

+    static void GetDevicePose(const FXRDeviceId& XRDeviceId, bool& bIsTracked, FRotator& Orientation, bool& bHasPositionalTracking, FVector& position);

     

     UFUNCTION(BlueprintCallable)

     static bool GetCurrentInteractionProfile(const EControllerHand Hand, FString& InteractionProfile);

     

     UFUNCTION(BlueprintCallable, meta=(WorldContext="WorldContext"))

-    static bool GetControllerTransformForTime2(UObject* WorldContext, const int32 ControllerIndex, const FName MotionSource, FTimespan Time, bool& bTimeWasUsed, FRotator& orientation, FVector& Position, bool& bProvidedLinearVelocity, FVector& LinearVelocity, bool& bProvidedAngularVelocity, FRotator& AngularVelocity, bool& bProvidedLinearAcceleration, FVector& LinearAcceleration);

+    static bool GetControllerTransformForTime2(UObject* WorldContext, const int32 ControllerIndex, const FName MotionSource, FTimespan Time, bool& bTimeWasUsed, FRotator& Orientation, FVector& position, bool& bProvidedLinearVelocity, FVector& LinearVelocity, bool& bProvidedAngularVelocity, FRotator& AngularVelocity, bool& bProvidedLinearAcceleration, FVector& LinearAcceleration);

     

     UFUNCTION(BlueprintCallable)

     static TArray<FXRDeviceId> EnumerateTrackedDevices(const FName SystemId, EXRTrackedDeviceType DeviceType);
