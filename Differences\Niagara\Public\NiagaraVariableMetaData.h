DIFFERENCES IN: Niagara\Public\NiagaraVariableMetaData.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Niagara\Public\NiagaraVariableMetaData.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Niagara\Public\NiagaraVariableMetaData.h
============================================================

SECTION starting at line 63 (left) / 63 (right):
----------------------------------------
  CONTEXT (line 63): 
  CONTEXT (line 64): private:
  CONTEXT (line 65):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  REMOVED (line 66):     FGuid VariableGUID;
  ADDED   (line 66):     FGuid VariableGuid;
  CONTEXT (line 67): 
  CONTEXT (line 68):     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
  CONTEXT (line 69):     bool bIsStaticSwitch;