DIFFERENCES IN: InterchangeNodes\Public\InterchangeSkeletalAnimationTrackNode.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\InterchangeNodes\Public\InterchangeSkeletalAnimationTrackNode.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\InterchangeNodes\Public\InterchangeSkeletalAnimationTrackNode.h
============================================================

SECTION starting at line 23 (left) / 23 (right):
----------------------------------------
  CONTEXT (line 23):     bool SetCustomAnimationSampleRate(const double& SampleRate);
  CONTEXT (line 24): 
  CONTEXT (line 25):     UFUNCTION(BlueprintCallable)
  REMOVED (line 26):     bool SetAnimationPayloadKeyForSceneNodeUid(const FString& SceneNodeUid, const FString& InUniqueID, const EInterchangeAnimationPayLoadType& InType);
  ADDED   (line 26):     bool SetAnimationPayloadKeyForSceneNodeUid(const FString& SceneNodeUid, const FString& InUniqueId, const EInterchangeAnimationPayLoadType& InType);
  CONTEXT (line 27): 
  CONTEXT (line 28):     UFUNCTION(BlueprintCallable)
  REMOVED (line 29):     bool SetAnimationPayloadKeyForMorphTargetNodeUid(const FString& MorphTargetNodeUid, const FString& InUniqueID, const EInterchangeAnimationPayLoadType& InType);
  ADDED   (line 29):     bool SetAnimationPayloadKeyForMorphTargetNodeUid(const FString& MorphTargetNodeUid, const FString& InUniqueId, const EInterchangeAnimationPayLoadType& InType);
  CONTEXT (line 30): 
  CONTEXT (line 31):     UFUNCTION(BlueprintCallable, BlueprintPure)
  CONTEXT (line 32):     bool IsNodeAnimatedWithBakedCurve(const FString& SceneNodeUid) const;