DIFFERENCES IN: GeometryCollectionNodes\Private\WaveScalarFieldDataflowNode.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\GeometryCollectionNodes\Private\WaveScalarFieldDataflowNode.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\GeometryCollectionNodes\Private\WaveScalarFieldDataflowNode.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): FWaveScalarFieldDataflowNode::FWaveScalarFieldDataflowNode() {
  CONTEXT (line 4):     this->Magnitude = 0.00f;
  REMOVED (line 5):     this->WaveLength = 0.00f;
  ADDED   (line 5):     this->Wavelength = 0.00f;
  CONTEXT (line 6):     this->Period = 0.00f;
  CONTEXT (line 7):     this->FunctionType = EDataflowWaveFunctionType::Dataflow_WaveFunctionType_Cosine;
  CONTEXT (line 8):     this->FalloffType = EDataflowFieldFalloffType::Dataflow_FieldFalloffType_None;