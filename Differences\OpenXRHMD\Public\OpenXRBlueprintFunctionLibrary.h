DIFFERENCES IN: OpenXRHMD\Public\OpenXRBlueprintFunctionLibrary.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\OpenXRHMD\Public\OpenXRBlueprintFunctionLibrary.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\OpenXRHMD\Public\OpenXRBlueprintFunctionLibrary.h
============================================================

SECTION starting at line 12 (left) / 12 (right):
----------------------------------------
  CONTEXT (line 12):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 13):     static void SetEnvironmentBlendMode(int32 NewBlendMode);
  CONTEXT (line 14): 
  REMOVED (line 15):     UFUNCTION(BlueprintCallable)
  REMOVED (line 16):     static int32 GetPrimarySwapChainWidth();
  REMOVED (line 17): 
  REMOVED (line 18):     UFUNCTION(BlueprintCallable)
  REMOVED (line 19):     static int32 GetPrimarySwapChainHeight();
  REMOVED (line 20): 
  REMOVED (line 21):     UFUNCTION(BlueprintCallable)
  REMOVED (line 22):     static float GetHMDDisplayRefreshRate();
  REMOVED (line 23): 
  CONTEXT (line 24): };
  CONTEXT (line 25): 