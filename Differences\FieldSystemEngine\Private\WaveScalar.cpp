DIFFERENCES IN: FieldSystemEngine\Private\WaveScalar.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\FieldSystemEngine\Private\WaveScalar.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\FieldSystemEngine\Private\WaveScalar.cpp
============================================================

SECTION starting at line 2 (left) / 2 (right):
----------------------------------------
  CONTEXT (line 2): 
  CONTEXT (line 3): UWaveScalar::UWaveScalar(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {
  CONTEXT (line 4):     this->Magnitude = 1.00f;
  REMOVED (line 5):     this->WaveLength = 10000.00f;
  ADDED   (line 5):     this->Wavelength = 10000.00f;
  CONTEXT (line 6):     this->Period = 1.00f;
  CONTEXT (line 7):     this->Function = Field_Wave_Cosine;
  CONTEXT (line 8):     this->Falloff = Field_Falloff_Linear;
  CONTEXT (line 9): }
  CONTEXT (line 10): 
  REMOVED (line 11): UWaveScalar* UWaveScalar::SetWaveScalar(float NewMagnitude, FVector NewPosition, float NewWaveLength, float NewPeriod, float Time, TEnumAsByte<EWaveFunctionType> NewFunction, TEnumAsByte<EFieldFalloffType> NewFalloff) {
  ADDED   (line 11): UWaveScalar* UWaveScalar::SetWaveScalar(float NewMagnitude, FVector NewPosition, float NewWavelength, float NewPeriod, float Time, TEnumAsByte<EWaveFunctionType> NewFunction, TEnumAsByte<EFieldFalloffType> NewFalloff) {
  CONTEXT (line 12):     return NULL;
  CONTEXT (line 13): }
  CONTEXT (line 14): 