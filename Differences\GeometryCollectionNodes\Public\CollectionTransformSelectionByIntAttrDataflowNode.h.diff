--- Left: CollectionTransformSelectionByIntAttrDataflowNode.h
+++ Right: CollectionTransformSelectionByIntAttrDataflowNode.h
@@ -20,10 +20,10 @@
     FString AttrName;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    int32 Min;

+    int32 min;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

-    int32 Max;

+    int32 max;

     

     UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))

     ERangeSettingEnum RangeSetting;
