DIFFERENCES IN: Engine\Private\DecalComponent.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\DecalComponent.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\DecalComponent.cpp
============================================================

SECTION starting at line 17 (left) / 17 (right):
----------------------------------------
  CONTEXT (line 17): void UDecalComponent::SetFadeScreenSize(float NewFadeScreenSize) {
  CONTEXT (line 18): }
  CONTEXT (line 19): 
  REMOVED (line 20): void UDecalComponent::SetFadeOut(float StartDelay, float duration, bool DestroyOwnerAfterFade) {
  ADDED   (line 20): void UDecalComponent::SetFadeOut(float StartDelay, float Duration, bool DestroyOwnerAfterFade) {
  CONTEXT (line 21): }
  CONTEXT (line 22): 
  REMOVED (line 23): void UDecalComponent::SetFadeIn(float StartDelay, float duration) {
  ADDED   (line 23): void UDecalComponent::SetFadeIn(float StartDelay, float Duration) {
  CONTEXT (line 24): }
  CONTEXT (line 25): 
  CONTEXT (line 26): void UDecalComponent::SetDecalMaterial(UMaterialInterface* NewDecalMaterial) {