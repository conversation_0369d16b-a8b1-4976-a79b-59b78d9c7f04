DIFFERENCES IN: Engine\Public\InputDeviceProperty.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Public\InputDeviceProperty.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Public\InputDeviceProperty.h
============================================================

SECTION starting at line 21 (left) / 21 (right):
----------------------------------------
  CONTEXT (line 21):     void ResetDeviceProperty(const FPlatformUserId PlatformUser, const FInputDeviceId DeviceID, bool bForceReset);
  CONTEXT (line 22): 
  CONTEXT (line 23):     UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
  REMOVED (line 24):     void EvaluateDeviceProperty(const FPlatformUserId PlatformUser, const FInputDeviceId DeviceID, const float DeltaTime, const float duration);
  ADDED   (line 24):     void EvaluateDeviceProperty(const FPlatformUserId PlatformUser, const FInputDeviceId DeviceID, const float DeltaTime, const float Duration);
  CONTEXT (line 25): 
  CONTEXT (line 26):     UFUNCTION(BlueprintCallable)
  CONTEXT (line 27):     void ApplyDeviceProperty(const FPlatformUserId UserId, const FInputDeviceId DeviceID);