DIFFERENCES IN: AnimGraphRuntime\Public\AnimationStateMachineLibrary.h
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\AnimGraphRuntime\Public\AnimationStateMachineLibrary.h
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\AnimGraphRuntime\Public\AnimationStateMachineLibrary.h
============================================================

SECTION starting at line 20 (left) / 20 (right):
----------------------------------------
  CONTEXT (line 20):     UAnimationStateMachineLibrary();
  CONTEXT (line 21): 
  CONTEXT (line 22):     UFUNCTION(BlueprintCallable)
  REMOVED (line 23):     static void SetState(const FAnimUpdateContext& UpdateContext, const FAnimationStateMachineReference& Node, FName TargetState, float duration, TEnumAsByte<ETransitionLogicType::Type> BlendType, UBlendProfile* BlendProfile, EAlphaBlendOption AlphaBlendOption, UCurveFloat* CustomBlendCurve);
  ADDED   (line 23):     static void SetState(const FAnimUpdateContext& UpdateContext, const FAnimationStateMachineReference& Node, FName TargetState, float Duration, TEnumAsByte<ETransitionLogicType::Type> BlendType, UBlendProfile* BlendProfile, EAlphaBlendOption AlphaBlendOption, UCurveFloat* CustomBlendCurve);
  CONTEXT (line 24): 
  CONTEXT (line 25):     UFUNCTION(BlueprintCallable, BlueprintPure)
  CONTEXT (line 26):     static bool IsStateBlendingOut(const FAnimUpdateContext& UpdateContext, const FAnimationStateResultReference& Node);