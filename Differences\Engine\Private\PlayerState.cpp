DIFFERENCES IN: Engine\Private\PlayerState.cpp
============================================================
Left file:  C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHT_14Beta1\Engine\Private\PlayerState.cpp
Right file: C:\Users\<USER>\Desktop\ITR5_5_staging\UHT_Compare\UHTHeaderDumpVanilla_5_5\Engine\Private\PlayerState.cpp
============================================================

SECTION starting at line 9 (left) / 9 (right):
----------------------------------------
  CONTEXT (line 9):     const FProperty* p_RemoteRole = GetClass()->FindPropertyByName("RemoteRole");
  CONTEXT (line 10):     (*p_RemoteRole->ContainerPtrToValuePtr<TEnumAsByte<ENetRole>>(this)) = ROLE_SimulatedProxy;
  CONTEXT (line 11):     this->Score = 0.00f;
  REMOVED (line 12):     this->PlayerID = 0;
  ADDED   (line 12):     this->PlayerId = 0;
  CONTEXT (line 13):     this->CompressedPing = 0;
  CONTEXT (line 14):     this->bShouldUpdateReplicatedPing = true;
  CONTEXT (line 15):     this->bIsSpectator = false;

SECTION starting at line 90 (left) / 90 (right):
----------------------------------------
  CONTEXT (line 90):     Super::GetLifetimeReplicatedProps(OutLifetimeProps);
  CONTEXT (line 91): 
  CONTEXT (line 92):     DOREPLIFETIME(APlayerState, Score);
  REMOVED (line 93):     DOREPLIFETIME(APlayerState, PlayerID);
  ADDED   (line 93):     DOREPLIFETIME(APlayerState, PlayerId);
  CONTEXT (line 94):     DOREPLIFETIME(APlayerState, CompressedPing);
  CONTEXT (line 95):     DOREPLIFETIME(APlayerState, bIsSpectator);
  CONTEXT (line 96):     DOREPLIFETIME(APlayerState, bOnlySpectator);